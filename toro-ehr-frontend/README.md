# toro-ehr-frontend

Project information todo

## Prerequisites

Ensure that you have the following installed on your system:

- [Node.js](https://nodejs.org/) (Recommended version: >= 20.x.x)
- [npm](https://npmjs.com/) (comes with Node.js)

### Installation

- Clone the repo - git clone https://github.com/torohealth/toro-ehr.git
- Install project dependencies — npm install
- Create .env file
- Launch the app — npm run dev, it will become available at http://localhost:3000
