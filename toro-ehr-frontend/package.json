{"name": "toro-ehr-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"start": "npm run dev", "generate:api": "swagger-typescript-api -p http://localhost:5233/swagger/v1/swagger.json -o src/api --axios -n api-reference.ts", "dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.18", "@fullcalendar/moment-timezone": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@fullcalendar/vue3": "^6.1.18", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@infectoone/vue-ganttastic": "^2.3.2", "@vuepic/vue-datepicker": "^11.0.1", "axios": "^1.7.9", "echarts": "^5.6.0", "html2pdf.js": "^0.10.3", "libphonenumber-js": "^1.12.15", "lodash.debounce": "^4.0.8", "luxon": "^3.7.1", "moment": "^2.30.1", "pinia": "^2.3.0", "preline": "^2.7.0", "primeicons": "^7.0.0", "primevue": "^4.2.5", "radar-sdk-js": "^4.5.5", "tailwindcss-primeui": "^0.4.0", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-draggable-resizable": "^3.0.0", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue-toastification": "^2.0.0-rc.5", "yup": "^1.6.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/lodash.debounce": "^4.0.9", "@types/luxon": "^3.6.2", "@types/node": "^22.10.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.1", "postcss-import": "^16.1.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.1.10"}}