import type {SelectListItem} from "@/utils/interfaces.ts";

export const getTribalAffiliations = (): SelectListItem[] => [
  { text: 'Native Entity Alaska', value: 'Native Entity Alaska' },
  { text: 'Native Village of Afognak', value: 'Native Village of Afognak' },
  { text: 'Agdaagux Tribe of King Cove', value: 'Agdaagux Tribe of King Cove' },
  { text: 'Native Village of Akhiok', value: 'Native Village of Akhiok' },
  { text: 'Akiachak Native Community', value: 'Akiachak Native Community' },
  { text: 'Akiak Native Community', value: 'Akiak Native Community' },
  { text: 'Native Village of Akutan', value: 'Native Village of Akutan' },
  { text: 'Village of Alakanuk', value: 'Village of Alakanuk' },
  { text: 'Alatna Village', value: 'Alatna Village' },
  { text: 'Native Village of Aleknagik', value: 'Native Village of Aleknagik' },
  { text: 'Algaaciq Native Village (St. Mary\'s)', value: 'Algaaciq Native Village (St. Mary\'s)' },
  { text: 'Allakaket Village', value: 'Allakaket Village' },
  { text: 'Native Village of Ambler', value: 'Native Village of Ambler' },
  { text: 'Village of Anaktuvuk Pass', value: 'Village of Anaktuvuk Pass' },
  { text: 'Yupiit of Andreafski', value: 'Yupiit of Andreafski' },
  { text: 'Angoon Community Association', value: 'Angoon Community Association' },
  { text: 'Village of Aniak', value: 'Village of Aniak' },
  { text: 'Anvik Village', value: 'Anvik Village' },
  { text: 'Arctic Village', value: 'Arctic Village' },
  { text: 'Asa\'carsarmiut Tribe', value: 'Asa\'carsarmiut Tribe' },
  { text: 'Native Village of Atka', value: 'Native Village of Atka' },
  { text: 'Village of Atmautluak', value: 'Village of Atmautluak' },
  { text: 'Native Village of Atqasuk', value: 'Native Village of Atqasuk' },
  { text: 'Native Village of Barrow Inupiat Traditional Government', value: 'Native Village of Barrow Inupiat Traditional Government' },
  { text: 'Beaver Village', value: 'Beaver Village' },
  { text: 'Native Village of Belkofski', value: 'Native Village of Belkofski' }
];
