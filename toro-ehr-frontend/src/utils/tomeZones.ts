import type { SelectListItem } from './interfaces'

export const getAllTimeZones = (): SelectListItem[] => [
  { text: '(GMT-10:00) Pacific/Honolulu', value: 'Pacific/Honolulu' },
  { text: '(GMT-08:00) America/Los_Angeles', value: 'America/Los_Angeles' },
  { text: '(GMT-07:00) America/Denver', value: 'America/Denver' },
  { text: '(GMT-07:00) America/Phoenix', value: 'America/Phoenix' },
  { text: '(GMT-06:00) America/Chicago', value: 'America/Chicago' },
  { text: '(GMT-05:00) America/New_York', value: 'America/New_York' },
  { text: '(GMT-03:00) America/Sao_Paulo', value: 'America/Sao_Paulo' },
  { text: '(GMT-03:00) America/Argentina/Buenos_Aires', value: 'America/Argentina/Buenos_Aires' },
  { text: '(GMT+00:00) Greenwich Mean Time', value: 'UTC' },
  { text: '(GMT+00:00) Europe/London', value: 'Europe/London' },
  { text: '(GMT+01:00) Europe/Berlin', value: 'Europe/Berlin' },
  { text: '(GMT+01:00) Europe/Belgrade', value: 'Europe/Belgrade' },
  { text: '(GMT+01:00) Africa/Lagos', value: 'Africa/Lagos' },
  { text: '(GMT+02:00) Africa/Cairo', value: 'Africa/Cairo' },
  { text: '(GMT+02:00) Africa/Johannesburg', value: 'Africa/Johannesburg' },
  { text: '(GMT+02:00) Asia/Jerusalem', value: 'Asia/Jerusalem' },
  { text: '(GMT+03:00) Europe/Moscow', value: 'Europe/Moscow' },
  { text: '(GMT+04:00) Asia/Dubai', value: 'Asia/Dubai' },
  { text: '(GMT+05:30) Asia/Kolkata', value: 'Asia/Kolkata' },
  { text: '(GMT+08:00) Asia/Singapore', value: 'Asia/Singapore' },
  { text: '(GMT+08:00) Asia/Shanghai', value: 'Asia/Shanghai' },
  { text: '(GMT+08:00) Australia/Perth', value: 'Australia/Perth' },
  { text: '(GMT+09:00) Asia/Tokyo', value: 'Asia/Tokyo' },
  { text: '(GMT+10:00) Australia/Sydney', value: 'Australia/Sydney' },
  { text: '(GMT+12:00) Pacific/Auckland', value: 'Pacific/Auckland' },
]
