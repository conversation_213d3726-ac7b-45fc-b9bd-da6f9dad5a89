import type { SelectListItem } from './interfaces'

export const getDurations = (): SelectListItem[] => [
  { text: '1 dose only', value: '1DoseOnly' },
  { text: '3 days', value: '3Days' },
  { text: '5 days', value: '5Days' },
  { text: '7 days', value: '7Days' },
  { text: '10 days', value: '10Days' },
  { text: '14 days', value: '14Days' },
  { text: 'Until discontinued', value: 'UntilDiscontinued' },
  { text: 'Custom', value: 'Custom' },
]
