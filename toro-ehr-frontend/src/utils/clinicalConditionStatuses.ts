import type { SelectListItem } from '@/api/api-reference'

export const getClinicalConditionStatuses = (): SelectListItem[] => [
  { text: 'Active', value: 'active' },
  { text: 'Recurrence', value: 'recurrence' },
  { text: 'Relapse', value: 'relapse' },
  { text: 'Inactive', value: 'inactive' },
  { text: 'Remission', value: 'remission' },
  { text: 'Resolved', value: 'resolved' },
]

export const getConditionVerificationStatuses = (): SelectListItem[] => [
  { text: 'Unconfirmed', value: 'unconfirmed' },
  { text: 'Provisional', value: 'provisional' },
  { text: 'Differential', value: 'differential' },
  { text: 'Confirmed', value: 'confirmed' },
  { text: 'Refuted', value: 'refuted' },
  { text: 'Entered in Error', value: 'entered-in-error' },
]
