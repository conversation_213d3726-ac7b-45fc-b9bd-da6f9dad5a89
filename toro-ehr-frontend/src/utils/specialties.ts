// Auto-generated from NUCC taxonomy CSV
export const specialtiesCodes = [
  {
    "code": "193200000X",
    "classification": "Multi-Specialty",
    "specialization": ""
  },
  {
    "code": "193400000X",
    "classification": "Single Specialty",
    "specialization": ""
  },
  {
    "code": "207K00000X",
    "classification": "Allergy & Immunology",
    "specialization": ""
  },
  {
    "code": "207KA0200X",
    "classification": "Allergy & Immunology",
    "specialization": "Allergy"
  },
  {
    "code": "207KI0005X",
    "classification": "Allergy & Immunology",
    "specialization": "Clinical & Laboratory Immunology"
  },
  {
    "code": "207L00000X",
    "classification": "Anesthesiology",
    "specialization": ""
  },
  {
    "code": "207LA0401X",
    "classification": "Anesthesiology",
    "specialization": "Addiction Medicine"
  },
  {
    "code": "207LC0200X",
    "classification": "Anesthesiology",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "207LH0002X",
    "classification": "Anesthesiology",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "207LP2900X",
    "classification": "Anesthesiology",
    "specialization": "Pain Medicine"
  },
  {
    "code": "207LP3000X",
    "classification": "Anesthesiology",
    "specialization": "Pediatric Anesthesiology"
  },
  {
    "code": "207LP4000X",
    "classification": "Anesthesiology",
    "specialization": "Physician Nutrition Specialist "
  },
  {
    "code": "208U00000X",
    "classification": "Clinical Pharmacology",
    "specialization": ""
  },
  {
    "code": "208C00000X",
    "classification": "Colon & Rectal Surgery",
    "specialization": ""
  },
  {
    "code": "207N00000X",
    "classification": "Dermatology",
    "specialization": ""
  },
  {
    "code": "207NI0002X",
    "classification": "Dermatology",
    "specialization": "Clinical & Laboratory Dermatological Immunology"
  },
  {
    "code": "207ND0900X",
    "classification": "Dermatology",
    "specialization": "Dermatopathology"
  },
  {
    "code": "207ND0101X",
    "classification": "Dermatology",
    "specialization": "MOHS-Micrographic Surgery"
  },
  {
    "code": "207NP0225X",
    "classification": "Dermatology",
    "specialization": "Pediatric Dermatology"
  },
  {
    "code": "207NS0135X",
    "classification": "Dermatology",
    "specialization": "Procedural Dermatology"
  },
  {
    "code": "204R00000X",
    "classification": "Electrodiagnostic Medicine",
    "specialization": ""
  },
  {
    "code": "207P00000X",
    "classification": "Emergency Medicine",
    "specialization": ""
  },
  {
    "code": "207PE0004X",
    "classification": "Emergency Medicine",
    "specialization": "Emergency Medical Services"
  },
  {
    "code": "207PH0002X",
    "classification": "Emergency Medicine",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "207PT0002X",
    "classification": "Emergency Medicine",
    "specialization": "Medical Toxicology"
  },
  {
    "code": "207PP0204X",
    "classification": "Emergency Medicine",
    "specialization": "Pediatric Emergency Medicine"
  },
  {
    "code": "207PS0010X",
    "classification": "Emergency Medicine",
    "specialization": "Sports Medicine"
  },
  {
    "code": "207PE0005X",
    "classification": "Emergency Medicine",
    "specialization": "Undersea and Hyperbaric Medicine"
  },
  {
    "code": "207Q00000X",
    "classification": "Family Medicine",
    "specialization": ""
  },
  {
    "code": "207QA0401X",
    "classification": "Family Medicine",
    "specialization": "Addiction Medicine"
  },
  {
    "code": "207QA0000X",
    "classification": "Family Medicine",
    "specialization": "Adolescent Medicine"
  },
  {
    "code": "207QA0505X",
    "classification": "Family Medicine",
    "specialization": "Adult Medicine"
  },
  {
    "code": "207QG0300X",
    "classification": "Family Medicine",
    "specialization": "Geriatric Medicine"
  },
  {
    "code": "207QH0002X",
    "classification": "Family Medicine",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "207QB0002X",
    "classification": "Family Medicine",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "207QP0002X",
    "classification": "Family Medicine",
    "specialization": "Physician Nutrition Specialist "
  },
  {
    "code": "207QS1201X",
    "classification": "Family Medicine",
    "specialization": "Sleep Medicine"
  },
  {
    "code": "207QS0010X",
    "classification": "Family Medicine",
    "specialization": "Sports Medicine"
  },
  {
    "code": "208D00000X",
    "classification": "General Practice",
    "specialization": ""
  },
  {
    "code": "208M00000X",
    "classification": "Hospitalist",
    "specialization": ""
  },
  {
    "code": "202C00000X",
    "classification": "Independent Medical Examiner",
    "specialization": ""
  },
  {
    "code": "202D00000X",
    "classification": "Integrative Medicine",
    "specialization": ""
  },
  {
    "code": "207R00000X",
    "classification": "Internal Medicine",
    "specialization": ""
  },
  {
    "code": "207RA0401X",
    "classification": "Internal Medicine",
    "specialization": "Addiction Medicine"
  },
  {
    "code": "207RA0000X",
    "classification": "Internal Medicine",
    "specialization": "Adolescent Medicine"
  },
  {
    "code": "207RA0002X",
    "classification": "Internal Medicine",
    "specialization": "Adult Congenital Heart Disease"
  },
  {
    "code": "207RA0001X",
    "classification": "Internal Medicine",
    "specialization": "Advanced Heart Failure and Transplant Cardiology"
  },
  {
    "code": "207RA0201X",
    "classification": "Internal Medicine",
    "specialization": "Allergy & Immunology"
  },
  {
    "code": "207RC0000X",
    "classification": "Internal Medicine",
    "specialization": "Cardiovascular Disease"
  },
  {
    "code": "207RI0001X",
    "classification": "Internal Medicine",
    "specialization": "Clinical & Laboratory Immunology"
  },
  {
    "code": "207RC0001X",
    "classification": "Internal Medicine",
    "specialization": "Clinical Cardiac Electrophysiology"
  },
  {
    "code": "207RC0200X",
    "classification": "Internal Medicine",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "207RE0101X",
    "classification": "Internal Medicine",
    "specialization": "Endocrinology, Diabetes & Metabolism"
  },
  {
    "code": "207RG0100X",
    "classification": "Internal Medicine",
    "specialization": "Gastroenterology"
  },
  {
    "code": "207RG0300X",
    "classification": "Internal Medicine",
    "specialization": "Geriatric Medicine"
  },
  {
    "code": "207RH0000X",
    "classification": "Internal Medicine",
    "specialization": "Hematology"
  },
  {
    "code": "207RH0003X",
    "classification": "Internal Medicine",
    "specialization": "Hematology & Oncology"
  },
  {
    "code": "207RI0008X",
    "classification": "Internal Medicine",
    "specialization": "Hepatology"
  },
  {
    "code": "207RH0002X",
    "classification": "Internal Medicine",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "207RH0005X",
    "classification": "Internal Medicine",
    "specialization": "Hypertension Specialist"
  },
  {
    "code": "207RI0200X",
    "classification": "Internal Medicine",
    "specialization": "Infectious Disease"
  },
  {
    "code": "207RI0011X",
    "classification": "Internal Medicine",
    "specialization": "Interventional Cardiology"
  },
  {
    "code": "207RM1200X",
    "classification": "Internal Medicine",
    "specialization": "Magnetic Resonance Imaging (MRI)"
  },
  {
    "code": "207RX0202X",
    "classification": "Internal Medicine",
    "specialization": "Medical Oncology"
  },
  {
    "code": "207RN0300X",
    "classification": "Internal Medicine",
    "specialization": "Nephrology"
  },
  {
    "code": "207RB0002X",
    "classification": "Internal Medicine",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "207RP1002X",
    "classification": "Internal Medicine",
    "specialization": "Physician Nutrition Specialist "
  },
  {
    "code": "207RP1001X",
    "classification": "Internal Medicine",
    "specialization": "Pulmonary Disease"
  },
  {
    "code": "207RR0500X",
    "classification": "Internal Medicine",
    "specialization": "Rheumatology"
  },
  {
    "code": "207RS0012X",
    "classification": "Internal Medicine",
    "specialization": "Sleep Medicine"
  },
  {
    "code": "207RS0010X",
    "classification": "Internal Medicine",
    "specialization": "Sports Medicine"
  },
  {
    "code": "207RT0003X",
    "classification": "Internal Medicine",
    "specialization": "Transplant Hepatology"
  },
  {
    "code": "209800000X",
    "classification": "Legal Medicine",
    "specialization": ""
  },
  {
    "code": "207SG0202X",
    "classification": "Medical Genetics",
    "specialization": "Clinical Biochemical Genetics"
  },
  {
    "code": "207SC0300X",
    "classification": "Medical Genetics",
    "specialization": "Clinical Cytogenetics"
  },
  {
    "code": "207SG0201X",
    "classification": "Medical Genetics",
    "specialization": "Clinical Genetics (M.D.)"
  },
  {
    "code": "207SG0203X",
    "classification": "Medical Genetics",
    "specialization": "Clinical Molecular Genetics"
  },
  {
    "code": "207SG0207X",
    "classification": "Medical Genetics",
    "specialization": "Medical Biochemical Genetics"
  },
  {
    "code": "207SM0001X",
    "classification": "Medical Genetics",
    "specialization": "Molecular Genetic Pathology"
  },
  {
    "code": "207SG0205X",
    "classification": "Medical Genetics",
    "specialization": "Ph.D. Medical Genetics"
  },
  {
    "code": "207T00000X",
    "classification": "Neurological Surgery",
    "specialization": ""
  },
  {
    "code": "204D00000X",
    "classification": "Neuromusculoskeletal Medicine & OMM",
    "specialization": ""
  },
  {
    "code": "204C00000X",
    "classification": "Neuromusculoskeletal Medicine, Sports Medicine",
    "specialization": ""
  },
  {
    "code": "207U00000X",
    "classification": "Nuclear Medicine",
    "specialization": ""
  },
  {
    "code": "207UN0903X",
    "classification": "Nuclear Medicine",
    "specialization": "In Vivo & In Vitro Nuclear Medicine"
  },
  {
    "code": "207UN0901X",
    "classification": "Nuclear Medicine",
    "specialization": "Nuclear Cardiology"
  },
  {
    "code": "207UN0902X",
    "classification": "Nuclear Medicine",
    "specialization": "Nuclear Imaging & Therapy"
  },
  {
    "code": "207V00000X",
    "classification": "Obstetrics & Gynecology",
    "specialization": ""
  },
  {
    "code": "207VC0300X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Complex Family Planning"
  },
  {
    "code": "207VC0200X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "207VF0040X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Urogynecology and Reconstructive Pelvic Surgery"
  },
  {
    "code": "207VX0201X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Gynecologic Oncology"
  },
  {
    "code": "207VG0400X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Gynecology"
  },
  {
    "code": "207VH0002X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "207VM0101X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Maternal & Fetal Medicine"
  },
  {
    "code": "207VB0002X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "207VX0000X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Obstetrics"
  },
  {
    "code": "207VE0102X",
    "classification": "Obstetrics & Gynecology",
    "specialization": "Reproductive Endocrinology"
  },
  {
    "code": "207W00000X",
    "classification": "Ophthalmology",
    "specialization": ""
  },
  {
    "code": "207WX0120X",
    "classification": "Ophthalmology",
    "specialization": "Cornea and External Diseases Specialist"
  },
  {
    "code": "207WX0009X",
    "classification": "Ophthalmology",
    "specialization": "Glaucoma Specialist"
  },
  {
    "code": "207WX0109X",
    "classification": "Ophthalmology",
    "specialization": "Neuro-ophthalmology"
  },
  {
    "code": "207WX0200X",
    "classification": "Ophthalmology",
    "specialization": "Ophthalmic Plastic and Reconstructive Surgery"
  },
  {
    "code": "207WX0110X",
    "classification": "Ophthalmology",
    "specialization": "Pediatric Ophthalmology and Strabismus Specialist"
  },
  {
    "code": "207WX0107X",
    "classification": "Ophthalmology",
    "specialization": "Retina Specialist"
  },
  {
    "code": "207WX0108X",
    "classification": "Ophthalmology",
    "specialization": "Uveitis and Ocular Inflammatory Disease"
  },
  {
    "code": "204E00000X",
    "classification": "Oral & Maxillofacial Surgery",
    "specialization": ""
  },
  {
    "code": "207X00000X",
    "classification": "Orthopaedic Surgery",
    "specialization": ""
  },
  {
    "code": "207XS0114X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Adult Reconstructive Orthopaedic Surgery"
  },
  {
    "code": "207XX0004X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Foot and Ankle Surgery"
  },
  {
    "code": "207XS0106X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Hand Surgery"
  },
  {
    "code": "207XS0117X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Orthopaedic Surgery of the Spine"
  },
  {
    "code": "207XX0801X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Orthopaedic Trauma"
  },
  {
    "code": "207XP3100X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Pediatric Orthopaedic Surgery"
  },
  {
    "code": "207XX0005X",
    "classification": "Orthopaedic Surgery",
    "specialization": "Sports Medicine"
  },
  {
    "code": "207Y00000X",
    "classification": "Otolaryngology",
    "specialization": ""
  },
  {
    "code": "207YS0123X",
    "classification": "Otolaryngology",
    "specialization": "Facial Plastic Surgery"
  },
  {
    "code": "207YX0602X",
    "classification": "Otolaryngology",
    "specialization": "Otolaryngic Allergy"
  },
  {
    "code": "207YX0905X",
    "classification": "Otolaryngology",
    "specialization": "Otolaryngology/Facial Plastic Surgery"
  },
  {
    "code": "207YX0901X",
    "classification": "Otolaryngology",
    "specialization": "Otology & Neurotology"
  },
  {
    "code": "207YP0228X",
    "classification": "Otolaryngology",
    "specialization": "Pediatric Otolaryngology"
  },
  {
    "code": "207YX0007X",
    "classification": "Otolaryngology",
    "specialization": "Plastic Surgery within the Head & Neck"
  },
  {
    "code": "207YS0012X",
    "classification": "Otolaryngology",
    "specialization": "Sleep Medicine"
  },
  {
    "code": "208VP0014X",
    "classification": "Pain Medicine",
    "specialization": "Interventional Pain Medicine"
  },
  {
    "code": "208VP0000X",
    "classification": "Pain Medicine",
    "specialization": "Pain Medicine"
  },
  {
    "code": "207ZP0101X",
    "classification": "Pathology",
    "specialization": "Anatomic Pathology"
  },
  {
    "code": "207ZP0102X",
    "classification": "Pathology",
    "specialization": "Anatomic Pathology & Clinical Pathology"
  },
  {
    "code": "207ZB0001X",
    "classification": "Pathology",
    "specialization": "Blood Banking & Transfusion Medicine"
  },
  {
    "code": "207ZP0104X",
    "classification": "Pathology",
    "specialization": "Chemical Pathology"
  },
  {
    "code": "207ZC0008X",
    "classification": "Pathology",
    "specialization": "Clinical Informatics"
  },
  {
    "code": "207ZC0006X",
    "classification": "Pathology",
    "specialization": "Clinical Pathology"
  },
  {
    "code": "207ZP0105X",
    "classification": "Pathology",
    "specialization": "Clinical Pathology/Laboratory Medicine"
  },
  {
    "code": "207ZC0500X",
    "classification": "Pathology",
    "specialization": "Cytopathology"
  },
  {
    "code": "207ZD0900X",
    "classification": "Pathology",
    "specialization": "Dermatopathology"
  },
  {
    "code": "207ZF0201X",
    "classification": "Pathology",
    "specialization": "Forensic Pathology"
  },
  {
    "code": "207ZH0000X",
    "classification": "Pathology",
    "specialization": "Hematology"
  },
  {
    "code": "207ZI0100X",
    "classification": "Pathology",
    "specialization": "Immunopathology"
  },
  {
    "code": "207ZM0300X",
    "classification": "Pathology",
    "specialization": "Medical Microbiology"
  },
  {
    "code": "207ZP0007X",
    "classification": "Pathology",
    "specialization": "Molecular Genetic Pathology"
  },
  {
    "code": "207ZN0500X",
    "classification": "Pathology",
    "specialization": "Neuropathology"
  },
  {
    "code": "207ZP0213X",
    "classification": "Pathology",
    "specialization": "Pediatric Pathology"
  },
  {
    "code": "208000000X",
    "classification": "Pediatrics",
    "specialization": ""
  },
  {
    "code": "2080A0000X",
    "classification": "Pediatrics",
    "specialization": "Adolescent Medicine"
  },
  {
    "code": "2080C0008X",
    "classification": "Pediatrics",
    "specialization": "Child Abuse Pediatrics"
  },
  {
    "code": "2080I0007X",
    "classification": "Pediatrics",
    "specialization": "Clinical & Laboratory Immunology"
  },
  {
    "code": "2080P0006X",
    "classification": "Pediatrics",
    "specialization": "Developmental - Behavioral Pediatrics"
  },
  {
    "code": "2080H0002X",
    "classification": "Pediatrics",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "2080T0002X",
    "classification": "Pediatrics",
    "specialization": "Medical Toxicology"
  },
  {
    "code": "2080N0001X",
    "classification": "Pediatrics",
    "specialization": "Neonatal-Perinatal Medicine"
  },
  {
    "code": "2080P0008X",
    "classification": "Pediatrics",
    "specialization": "Neurodevelopmental Disabilities"
  },
  {
    "code": "2080B0002X",
    "classification": "Pediatrics",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "2080P0201X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Allergy/Immunology"
  },
  {
    "code": "2080P0202X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Cardiology"
  },
  {
    "code": "2080P0203X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Critical Care Medicine"
  },
  {
    "code": "2080P0204X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Emergency Medicine"
  },
  {
    "code": "2080P0205X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Endocrinology"
  },
  {
    "code": "2080P0206X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Gastroenterology"
  },
  {
    "code": "2080P0207X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Hematology-Oncology"
  },
  {
    "code": "2080P0208X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Infectious Diseases"
  },
  {
    "code": "2080P0210X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Nephrology"
  },
  {
    "code": "2080P0214X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Pulmonology"
  },
  {
    "code": "2080P0216X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Rheumatology"
  },
  {
    "code": "2080T0004X",
    "classification": "Pediatrics",
    "specialization": "Pediatric Transplant Hepatology"
  },
  {
    "code": "2080P1004X",
    "classification": "Pediatrics",
    "specialization": "Physician Nutrition Specialist "
  },
  {
    "code": "2080S0012X",
    "classification": "Pediatrics",
    "specialization": "Sleep Medicine"
  },
  {
    "code": "2080S0010X",
    "classification": "Pediatrics",
    "specialization": "Sports Medicine"
  },
  {
    "code": "202K00000X",
    "classification": "Phlebology",
    "specialization": ""
  },
  {
    "code": "208100000X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": ""
  },
  {
    "code": "2081P0301X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Brain Injury Medicine"
  },
  {
    "code": "2081H0002X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "2081N0008X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Neuromuscular Medicine"
  },
  {
    "code": "2081P2900X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Pain Medicine"
  },
  {
    "code": "2081P0010X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Pediatric Rehabilitation Medicine"
  },
  {
    "code": "2081P0004X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Spinal Cord Injury Medicine"
  },
  {
    "code": "2081S0010X",
    "classification": "Physical Medicine & Rehabilitation",
    "specialization": "Sports Medicine"
  },
  {
    "code": "208200000X",
    "classification": "Plastic Surgery",
    "specialization": ""
  },
  {
    "code": "2082S0099X",
    "classification": "Plastic Surgery",
    "specialization": "Plastic Surgery Within the Head and Neck"
  },
  {
    "code": "2082S0105X",
    "classification": "Plastic Surgery",
    "specialization": "Surgery of the Hand"
  },
  {
    "code": "2083A0300X",
    "classification": "Preventive Medicine",
    "specialization": "Addiction Medicine"
  },
  {
    "code": "2083A0100X",
    "classification": "Preventive Medicine",
    "specialization": "Aerospace Medicine"
  },
  {
    "code": "2083C0008X",
    "classification": "Preventive Medicine",
    "specialization": "Clinical Informatics"
  },
  {
    "code": "2083T0002X",
    "classification": "Preventive Medicine",
    "specialization": "Medical Toxicology"
  },
  {
    "code": "2083B0002X",
    "classification": "Preventive Medicine",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "2083X0100X",
    "classification": "Preventive Medicine",
    "specialization": "Occupational Medicine"
  },
  {
    "code": "2083P0500X",
    "classification": "Preventive Medicine",
    "specialization": "Preventive Medicine/Occupational Environmental Medicine"
  },
  {
    "code": "2083P0901X",
    "classification": "Preventive Medicine",
    "specialization": "Public Health & General Preventive Medicine"
  },
  {
    "code": "2083S0010X",
    "classification": "Preventive Medicine",
    "specialization": "Sports Medicine"
  },
  {
    "code": "2083P0011X",
    "classification": "Preventive Medicine",
    "specialization": "Undersea and Hyperbaric Medicine"
  },
  {
    "code": "2084A0401X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Addiction Medicine"
  },
  {
    "code": "2084P0802X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Addiction Psychiatry"
  },
  {
    "code": "2084B0040X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Behavioral Neurology & Neuropsychiatry"
  },
  {
    "code": "2084P0301X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Brain Injury Medicine"
  },
  {
    "code": "2084P0804X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Child & Adolescent Psychiatry"
  },
  {
    "code": "2084N0600X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Clinical Neurophysiology"
  },
  {
    "code": "2084D0003X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Diagnostic Neuroimaging"
  },
  {
    "code": "2084E0001X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Epilepsy"
  },
  {
    "code": "2084F0202X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Forensic Psychiatry"
  },
  {
    "code": "2084P0805X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Geriatric Psychiatry"
  },
  {
    "code": "2084H0002X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "2084A2900X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Neurocritical Care"
  },
  {
    "code": "2084P0005X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Neurodevelopmental Disabilities"
  },
  {
    "code": "2084N0400X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Neurology"
  },
  {
    "code": "2084N0402X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Neurology with Special Qualifications in Child Neurology"
  },
  {
    "code": "2084N0008X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Neuromuscular Medicine"
  },
  {
    "code": "2084B0002X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Obesity Medicine"
  },
  {
    "code": "2084P2900X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Pain Medicine"
  },
  {
    "code": "2084P0800X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Psychiatry"
  },
  {
    "code": "2084P0015X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Psychosomatic Medicine"
  },
  {
    "code": "2084S0012X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Sleep Medicine"
  },
  {
    "code": "2084S0010X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Sports Medicine"
  },
  {
    "code": "2084V0102X",
    "classification": "Psychiatry & Neurology",
    "specialization": "Vascular Neurology"
  },
  {
    "code": "2085B0100X",
    "classification": "Radiology",
    "specialization": "Body Imaging"
  },
  {
    "code": "2085D0003X",
    "classification": "Radiology",
    "specialization": "Diagnostic Neuroimaging"
  },
  {
    "code": "2085R0202X",
    "classification": "Radiology",
    "specialization": "Diagnostic Radiology"
  },
  {
    "code": "2085U0001X",
    "classification": "Radiology",
    "specialization": "Diagnostic Ultrasound"
  },
  {
    "code": "2085H0002X",
    "classification": "Radiology",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "2085N0700X",
    "classification": "Radiology",
    "specialization": "Neuroradiology"
  },
  {
    "code": "2085N0904X",
    "classification": "Radiology",
    "specialization": "Nuclear Radiology"
  },
  {
    "code": "2085P0229X",
    "classification": "Radiology",
    "specialization": "Pediatric Radiology"
  },
  {
    "code": "2085R0001X",
    "classification": "Radiology",
    "specialization": "Radiation Oncology"
  },
  {
    "code": "2085R0205X",
    "classification": "Radiology",
    "specialization": "Radiological Physics"
  },
  {
    "code": "2085R0203X",
    "classification": "Radiology",
    "specialization": "Therapeutic Radiology"
  },
  {
    "code": "2085R0204X",
    "classification": "Radiology",
    "specialization": "Vascular & Interventional Radiology"
  },
  {
    "code": "208600000X",
    "classification": "Surgery",
    "specialization": ""
  },
  {
    "code": "2086H0002X",
    "classification": "Surgery",
    "specialization": "Hospice and Palliative Medicine"
  },
  {
    "code": "2086S0120X",
    "classification": "Surgery",
    "specialization": "Pediatric Surgery"
  },
  {
    "code": "2086P0122X",
    "classification": "Surgery",
    "specialization": "Physician Nutrition Specialist"
  },
  {
    "code": "2086S0122X",
    "classification": "Surgery",
    "specialization": "Plastic and Reconstructive Surgery"
  },
  {
    "code": "2086S0105X",
    "classification": "Surgery",
    "specialization": "Surgery of the Hand"
  },
  {
    "code": "2086S0102X",
    "classification": "Surgery",
    "specialization": "Surgical Critical Care"
  },
  {
    "code": "2086X0206X",
    "classification": "Surgery",
    "specialization": "Surgical Oncology"
  },
  {
    "code": "2086S0127X",
    "classification": "Surgery",
    "specialization": "Trauma Surgery"
  },
  {
    "code": "2086S0129X",
    "classification": "Surgery",
    "specialization": "Vascular Surgery"
  },
  {
    "code": "208G00000X",
    "classification": "Thoracic Surgery (Cardiothoracic Vascular Surgery)",
    "specialization": ""
  },
  {
    "code": "204F00000X",
    "classification": "Transplant Surgery",
    "specialization": ""
  },
  {
    "code": "208800000X",
    "classification": "Urology",
    "specialization": ""
  },
  {
    "code": "2088F0040X",
    "classification": "Urology",
    "specialization": "Urogynecology and Reconstructive Pelvic Surgery"
  },
  {
    "code": "2088P0231X",
    "classification": "Urology",
    "specialization": "Pediatric Urology"
  },
  {
    "code": "106E00000X",
    "classification": "Assistant Behavior Analyst",
    "specialization": ""
  },
  {
    "code": "106S00000X",
    "classification": "Behavior Technician",
    "specialization": ""
  },
  {
    "code": "103K00000X",
    "classification": "Behavior Analyst",
    "specialization": ""
  },
  {
    "code": "103G00000X",
    "classification": "Clinical Neuropsychologist",
    "specialization": ""
  },
  {
    "code": "103GC0700X",
    "classification": "Clinical Neuropsychologist",
    "specialization": "Clinical"
  },
  {
    "code": "101Y00000X",
    "classification": "Counselor",
    "specialization": ""
  },
  {
    "code": "101YA0400X",
    "classification": "Counselor",
    "specialization": "Addiction (Substance Use Disorder)"
  },
  {
    "code": "101YM0800X",
    "classification": "Counselor",
    "specialization": "Mental Health"
  },
  {
    "code": "101YP1600X",
    "classification": "Counselor",
    "specialization": "Pastoral"
  },
  {
    "code": "101YP2500X",
    "classification": "Counselor",
    "specialization": "Professional"
  },
  {
    "code": "101YS0200X",
    "classification": "Counselor",
    "specialization": "School"
  },
  {
    "code": "101200000X",
    "classification": "Drama Therapist",
    "specialization": ""
  },
  {
    "code": "106H00000X",
    "classification": "Marriage & Family Therapist",
    "specialization": ""
  },
  {
    "code": "102X00000X",
    "classification": "Poetry Therapist",
    "specialization": ""
  },
  {
    "code": "102L00000X",
    "classification": "Psychoanalyst",
    "specialization": ""
  },
  {
    "code": "103T00000X",
    "classification": "Psychologist",
    "specialization": ""
  },
  {
    "code": "103TA0400X",
    "classification": "Psychologist",
    "specialization": "Addiction (Substance Use Disorder)"
  },
  {
    "code": "103TA0700X",
    "classification": "Psychologist",
    "specialization": "Adult Development & Aging"
  },
  {
    "code": "103TC0700X",
    "classification": "Psychologist",
    "specialization": "Clinical"
  },
  {
    "code": "103TC2200X",
    "classification": "Psychologist",
    "specialization": "Clinical Child & Adolescent"
  },
  {
    "code": "103TB0200X",
    "classification": "Psychologist",
    "specialization": "Cognitive & Behavioral"
  },
  {
    "code": "103TC1900X",
    "classification": "Psychologist",
    "specialization": "Counseling"
  },
  {
    "code": "103TE1000X",
    "classification": "Psychologist",
    "specialization": "Educational"
  },
  {
    "code": "103TE1100X",
    "classification": "Psychologist",
    "specialization": "Exercise & Sports"
  },
  {
    "code": "103TF0000X",
    "classification": "Psychologist",
    "specialization": "Family"
  },
  {
    "code": "103TF0200X",
    "classification": "Psychologist",
    "specialization": "Forensic"
  },
  {
    "code": "103TP2701X",
    "classification": "Psychologist",
    "specialization": "Group Psychotherapy"
  },
  {
    "code": "103TH0004X",
    "classification": "Psychologist",
    "specialization": "Health"
  },
  {
    "code": "103TH0100X",
    "classification": "Psychologist",
    "specialization": "Health Service"
  },
  {
    "code": "103TM1700X",
    "classification": "Psychologist",
    "specialization": "Men & Masculinity"
  },
  {
    "code": "103TM1800X",
    "classification": "Psychologist",
    "specialization": "Intellectual & Developmental Disabilities"
  },
  {
    "code": "103TP0016X",
    "classification": "Psychologist",
    "specialization": "Prescribing (Medical)"
  },
  {
    "code": "103TP0814X",
    "classification": "Psychologist",
    "specialization": "Psychoanalysis"
  },
  {
    "code": "103TP2700X",
    "classification": "Psychologist",
    "specialization": "Psychotherapy"
  },
  {
    "code": "103TR0400X",
    "classification": "Psychologist",
    "specialization": "Rehabilitation"
  },
  {
    "code": "103TS0200X",
    "classification": "Psychologist",
    "specialization": "School"
  },
  {
    "code": "103TW0100X",
    "classification": "Psychologist",
    "specialization": "Women"
  },
  {
    "code": "104100000X",
    "classification": "Social Worker",
    "specialization": ""
  },
  {
    "code": "1041C0700X",
    "classification": "Social Worker",
    "specialization": "Clinical"
  },
  {
    "code": "1041S0200X",
    "classification": "Social Worker",
    "specialization": "School"
  },
  {
    "code": "111N00000X",
    "classification": "Chiropractor",
    "specialization": ""
  },
  {
    "code": "111NI0013X",
    "classification": "Chiropractor",
    "specialization": "Independent Medical Examiner"
  },
  {
    "code": "111NI0900X",
    "classification": "Chiropractor",
    "specialization": "Internist"
  },
  {
    "code": "111NN0400X",
    "classification": "Chiropractor",
    "specialization": "Neurology"
  },
  {
    "code": "111NN1001X",
    "classification": "Chiropractor",
    "specialization": "Nutrition"
  },
  {
    "code": "111NX0100X",
    "classification": "Chiropractor",
    "specialization": "Occupational Health"
  },
  {
    "code": "111NX0800X",
    "classification": "Chiropractor",
    "specialization": "Orthopedic"
  },
  {
    "code": "111NP0017X",
    "classification": "Chiropractor",
    "specialization": "Pediatric Chiropractor"
  },
  {
    "code": "111NR0200X",
    "classification": "Chiropractor",
    "specialization": "Radiology"
  },
  {
    "code": "111NR0400X",
    "classification": "Chiropractor",
    "specialization": "Rehabilitation"
  },
  {
    "code": "111NS0005X",
    "classification": "Chiropractor",
    "specialization": "Sports Physician"
  },
  {
    "code": "111NT0100X",
    "classification": "Chiropractor",
    "specialization": "Thermography"
  },
  {
    "code": "125K00000X",
    "classification": "Advanced Practice Dental Therapist",
    "specialization": ""
  },
  {
    "code": "126800000X",
    "classification": "Dental Assistant",
    "specialization": ""
  },
  {
    "code": "124Q00000X",
    "classification": "Dental Hygienist",
    "specialization": ""
  },
  {
    "code": "126900000X",
    "classification": "Dental Laboratory Technician",
    "specialization": ""
  },
  {
    "code": "125J00000X",
    "classification": "Dental Therapist",
    "specialization": ""
  },
  {
    "code": "122300000X",
    "classification": "Dentist",
    "specialization": ""
  },
  {
    "code": "1223D0004X",
    "classification": "Dentist",
    "specialization": "Dental Anesthesiology"
  },
  {
    "code": "1223D0001X",
    "classification": "Dentist",
    "specialization": "Dental Public Health"
  },
  {
    "code": "1223E0200X",
    "classification": "Dentist",
    "specialization": "Endodontics"
  },
  {
    "code": "1223G0001X",
    "classification": "Dentist",
    "specialization": "General Practice"
  },
  {
    "code": "1223P0106X",
    "classification": "Dentist",
    "specialization": "Oral and Maxillofacial Pathology"
  },
  {
    "code": "1223X0008X",
    "classification": "Dentist",
    "specialization": "Oral and Maxillofacial Radiology"
  },
  {
    "code": "1223S0112X",
    "classification": "Dentist",
    "specialization": "Oral and Maxillofacial Surgery"
  },
  {
    "code": "125Q00000X",
    "classification": "Dentist",
    "specialization": "Oral Medicine"
  },
  {
    "code": "1223X2210X",
    "classification": "Dentist",
    "specialization": "Orofacial Pain"
  },
  {
    "code": "1223X0400X",
    "classification": "Dentist",
    "specialization": "Orthodontics and Dentofacial Orthopedics"
  },
  {
    "code": "1223P0221X",
    "classification": "Dentist",
    "specialization": "Pediatric Dentistry"
  },
  {
    "code": "1223P0300X",
    "classification": "Dentist",
    "specialization": "Periodontics"
  },
  {
    "code": "1223P0700X",
    "classification": "Dentist",
    "specialization": "Prosthodontics"
  },
  {
    "code": "122400000X",
    "classification": "Denturist",
    "specialization": ""
  },
  {
    "code": "132700000X",
    "classification": "Dietary Manager",
    "specialization": ""
  },
  {
    "code": "136A00000X",
    "classification": "Dietetic Technician, Registered",
    "specialization": ""
  },
  {
    "code": "133V00000X",
    "classification": "Dietitian, Registered",
    "specialization": ""
  },
  {
    "code": "133VN1101X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Gerontological"
  },
  {
    "code": "133VN1006X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Metabolic"
  },
  {
    "code": "133VN1201X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Obesity and Weight Management"
  },
  {
    "code": "133VN1301X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Oncology"
  },
  {
    "code": "133VN1004X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Pediatric"
  },
  {
    "code": "133VN1401X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Pediatric Critical Care"
  },
  {
    "code": "133VN1005X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Renal"
  },
  {
    "code": "133VN1501X",
    "classification": "Dietitian, Registered",
    "specialization": "Nutrition, Sports Dietetics"
  },
  {
    "code": "133N00000X",
    "classification": "Nutritionist",
    "specialization": ""
  },
  {
    "code": "133NN1002X",
    "classification": "Nutritionist",
    "specialization": "Nutrition, Education"
  },
  {
    "code": "146N00000X",
    "classification": "Emergency Medical Technician, Basic",
    "specialization": ""
  },
  {
    "code": "146M00000X",
    "classification": "Emergency Medical Technician, Intermediate",
    "specialization": ""
  },
  {
    "code": "146L00000X",
    "classification": "Emergency Medical Technician, Paramedic",
    "specialization": ""
  },
  {
    "code": "146D00000X",
    "classification": "Personal Emergency Response Attendant",
    "specialization": ""
  },
  {
    "code": "152W00000X",
    "classification": "Optometrist",
    "specialization": ""
  },
  {
    "code": "152WC0802X",
    "classification": "Optometrist",
    "specialization": "Corneal and Contact Management"
  },
  {
    "code": "152WL0500X",
    "classification": "Optometrist",
    "specialization": "Low Vision Rehabilitation"
  },
  {
    "code": "152WX0102X",
    "classification": "Optometrist",
    "specialization": "Occupational Vision"
  },
  {
    "code": "152WP0200X",
    "classification": "Optometrist",
    "specialization": "Pediatrics"
  },
  {
    "code": "152WS0006X",
    "classification": "Optometrist",
    "specialization": "Sports Vision"
  },
  {
    "code": "152WV0400X",
    "classification": "Optometrist",
    "specialization": "Vision Therapy"
  },
  {
    "code": "156F00000X",
    "classification": "Technician/Technologist",
    "specialization": ""
  },
  {
    "code": "156FC0800X",
    "classification": "Technician/Technologist",
    "specialization": "Contact Lens"
  },
  {
    "code": "156FC0801X",
    "classification": "Technician/Technologist",
    "specialization": "Contact Lens Fitter"
  },
  {
    "code": "156FX1700X",
    "classification": "Technician/Technologist",
    "specialization": "Ocularist"
  },
  {
    "code": "156FX1100X",
    "classification": "Technician/Technologist",
    "specialization": "Ophthalmic"
  },
  {
    "code": "156FX1101X",
    "classification": "Technician/Technologist",
    "specialization": "Ophthalmic Assistant"
  },
  {
    "code": "156FX1800X",
    "classification": "Technician/Technologist",
    "specialization": "Optician"
  },
  {
    "code": "156FX1201X",
    "classification": "Technician/Technologist",
    "specialization": "Optometric Assistant"
  },
  {
    "code": "156FX1202X",
    "classification": "Technician/Technologist",
    "specialization": "Optometric Technician"
  },
  {
    "code": "156FX1900X",
    "classification": "Technician/Technologist",
    "specialization": "Orthoptist"
  },
  {
    "code": "164W00000X",
    "classification": "Licensed Practical Nurse",
    "specialization": ""
  },
  {
    "code": "167G00000X",
    "classification": "Licensed Psychiatric Technician",
    "specialization": ""
  },
  {
    "code": "164X00000X",
    "classification": "Licensed Vocational Nurse",
    "specialization": ""
  },
  {
    "code": "163W00000X",
    "classification": "Registered Nurse",
    "specialization": ""
  },
  {
    "code": "163WA0400X",
    "classification": "Registered Nurse",
    "specialization": "Addiction (Substance Use Disorder)"
  },
  {
    "code": "163WA2000X",
    "classification": "Registered Nurse",
    "specialization": "Administrator"
  },
  {
    "code": "163WP2201X",
    "classification": "Registered Nurse",
    "specialization": "Ambulatory Care"
  },
  {
    "code": "163WC3500X",
    "classification": "Registered Nurse",
    "specialization": "Cardiac Rehabilitation"
  },
  {
    "code": "163WC0400X",
    "classification": "Registered Nurse",
    "specialization": "Case Management"
  },
  {
    "code": "163WC1400X",
    "classification": "Registered Nurse",
    "specialization": "College Health"
  },
  {
    "code": "163WC1500X",
    "classification": "Registered Nurse",
    "specialization": "Community Health"
  },
  {
    "code": "163WC2100X",
    "classification": "Registered Nurse",
    "specialization": "Continence Care"
  },
  {
    "code": "163WC1600X",
    "classification": "Registered Nurse",
    "specialization": "Continuing Education/Staff Development"
  },
  {
    "code": "163WC0200X",
    "classification": "Registered Nurse",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "163WD0400X",
    "classification": "Registered Nurse",
    "specialization": "Diabetes Educator"
  },
  {
    "code": "163WD1100X",
    "classification": "Registered Nurse",
    "specialization": "Dialysis, Peritoneal"
  },
  {
    "code": "163WE0003X",
    "classification": "Registered Nurse",
    "specialization": "Emergency"
  },
  {
    "code": "163WE0900X",
    "classification": "Registered Nurse",
    "specialization": "Enterostomal Therapy"
  },
  {
    "code": "163WF0300X",
    "classification": "Registered Nurse",
    "specialization": "Flight"
  },
  {
    "code": "163WG0100X",
    "classification": "Registered Nurse",
    "specialization": "Gastroenterology"
  },
  {
    "code": "163WG0000X",
    "classification": "Registered Nurse",
    "specialization": "General Practice"
  },
  {
    "code": "163WG0600X",
    "classification": "Registered Nurse",
    "specialization": "Gerontology"
  },
  {
    "code": "163WH0500X",
    "classification": "Registered Nurse",
    "specialization": "Hemodialysis"
  },
  {
    "code": "163WH0200X",
    "classification": "Registered Nurse",
    "specialization": "Home Health"
  },
  {
    "code": "163WH1000X",
    "classification": "Registered Nurse",
    "specialization": "Hospice"
  },
  {
    "code": "163WI0600X",
    "classification": "Registered Nurse",
    "specialization": "Infection Control"
  },
  {
    "code": "163WI0500X",
    "classification": "Registered Nurse",
    "specialization": "Infusion Therapy"
  },
  {
    "code": "163WL0100X",
    "classification": "Registered Nurse",
    "specialization": "Lactation Consultant"
  },
  {
    "code": "163WM0102X",
    "classification": "Registered Nurse",
    "specialization": "Maternal Newborn"
  },
  {
    "code": "163WM0705X",
    "classification": "Registered Nurse",
    "specialization": "Medical-Surgical"
  },
  {
    "code": "163WN0002X",
    "classification": "Registered Nurse",
    "specialization": "Neonatal Intensive Care"
  },
  {
    "code": "163WN0003X",
    "classification": "Registered Nurse",
    "specialization": "Neonatal, Low-Risk"
  },
  {
    "code": "163WN0300X",
    "classification": "Registered Nurse",
    "specialization": "Nephrology"
  },
  {
    "code": "163WN0800X",
    "classification": "Registered Nurse",
    "specialization": "Neuroscience"
  },
  {
    "code": "163WM1400X",
    "classification": "Registered Nurse",
    "specialization": "Nurse Massage Therapist (NMT)"
  },
  {
    "code": "163WN1003X",
    "classification": "Registered Nurse",
    "specialization": "Nutrition Support"
  },
  {
    "code": "163WX0002X",
    "classification": "Registered Nurse",
    "specialization": "Obstetric, High-Risk"
  },
  {
    "code": "163WX0003X",
    "classification": "Registered Nurse",
    "specialization": "Obstetric, Inpatient"
  },
  {
    "code": "163WX0106X",
    "classification": "Registered Nurse",
    "specialization": "Occupational Health"
  },
  {
    "code": "163WX0200X",
    "classification": "Registered Nurse",
    "specialization": "Oncology"
  },
  {
    "code": "163WX1100X",
    "classification": "Registered Nurse",
    "specialization": "Ophthalmic"
  },
  {
    "code": "163WX0800X",
    "classification": "Registered Nurse",
    "specialization": "Orthopedic"
  },
  {
    "code": "163WX1500X",
    "classification": "Registered Nurse",
    "specialization": "Ostomy Care"
  },
  {
    "code": "163WX0601X",
    "classification": "Registered Nurse",
    "specialization": "Otorhinolaryngology & Head-Neck"
  },
  {
    "code": "163WP0000X",
    "classification": "Registered Nurse",
    "specialization": "Pain Management"
  },
  {
    "code": "163WP0218X",
    "classification": "Registered Nurse",
    "specialization": "Pediatric Oncology"
  },
  {
    "code": "163WP0200X",
    "classification": "Registered Nurse",
    "specialization": "Pediatrics"
  },
  {
    "code": "163WP1700X",
    "classification": "Registered Nurse",
    "specialization": "Perinatal"
  },
  {
    "code": "163WS0121X",
    "classification": "Registered Nurse",
    "specialization": "Plastic Surgery"
  },
  {
    "code": "163WP0808X",
    "classification": "Registered Nurse",
    "specialization": "Psychiatric/Mental Health"
  },
  {
    "code": "163WP0809X",
    "classification": "Registered Nurse",
    "specialization": "Psychiatric/Mental Health, Adult"
  },
  {
    "code": "163WP0807X",
    "classification": "Registered Nurse",
    "specialization": "Psychiatric/Mental Health, Child & Adolescent"
  },
  {
    "code": "163WR0006X",
    "classification": "Registered Nurse",
    "specialization": "Registered Nurse First Assistant"
  },
  {
    "code": "163WR0400X",
    "classification": "Registered Nurse",
    "specialization": "Rehabilitation"
  },
  {
    "code": "163WR1000X",
    "classification": "Registered Nurse",
    "specialization": "Reproductive Endocrinology/Infertility"
  },
  {
    "code": "163WS0200X",
    "classification": "Registered Nurse",
    "specialization": "School"
  },
  {
    "code": "163WU0100X",
    "classification": "Registered Nurse",
    "specialization": "Urology"
  },
  {
    "code": "163WW0101X",
    "classification": "Registered Nurse",
    "specialization": "Women's Health Care, Ambulatory"
  },
  {
    "code": "163WW0000X",
    "classification": "Registered Nurse",
    "specialization": "Wound Care"
  },
  {
    "code": "372600000X",
    "classification": "Adult Companion",
    "specialization": ""
  },
  {
    "code": "372500000X",
    "classification": "Chore Provider",
    "specialization": ""
  },
  {
    "code": "373H00000X",
    "classification": "Day Training/Habilitation Specialist",
    "specialization": ""
  },
  {
    "code": "374J00000X",
    "classification": "Doula",
    "specialization": ""
  },
  {
    "code": "374U00000X",
    "classification": "Home Health Aide",
    "specialization": ""
  },
  {
    "code": "376J00000X",
    "classification": "Homemaker",
    "specialization": ""
  },
  {
    "code": "376K00000X",
    "classification": "Nurse's Aide",
    "specialization": ""
  },
  {
    "code": "376G00000X",
    "classification": "Nursing Home Administrator",
    "specialization": ""
  },
  {
    "code": "374T00000X",
    "classification": "Religious Nonmedical Nursing Personnel",
    "specialization": ""
  },
  {
    "code": "374K00000X",
    "classification": "Religious Nonmedical Practitioner",
    "specialization": ""
  },
  {
    "code": "374700000X",
    "classification": "Technician",
    "specialization": ""
  },
  {
    "code": "3747A0650X",
    "classification": "Technician",
    "specialization": "Attendant Care Provider"
  },
  {
    "code": "3747P1801X",
    "classification": "Technician",
    "specialization": "Personal Care Attendant"
  },
  {
    "code": "171100000X",
    "classification": "Acupuncturist",
    "specialization": ""
  },
  {
    "code": "171M00000X",
    "classification": "Case Manager/Care Coordinator",
    "specialization": ""
  },
  {
    "code": "174V00000X",
    "classification": "Clinical Ethicist",
    "specialization": ""
  },
  {
    "code": "172V00000X",
    "classification": "Community Health Worker",
    "specialization": ""
  },
  {
    "code": "171W00000X",
    "classification": "Contractor",
    "specialization": ""
  },
  {
    "code": "171WH0202X",
    "classification": "Contractor",
    "specialization": "Home Modifications"
  },
  {
    "code": "171WV0202X",
    "classification": "Contractor",
    "specialization": "Vehicle Modifications"
  },
  {
    "code": "172A00000X",
    "classification": "Driver",
    "specialization": ""
  },
  {
    "code": "176P00000X",
    "classification": "Funeral Director",
    "specialization": ""
  },
  {
    "code": "170300000X",
    "classification": "Genetic Counselor, MS",
    "specialization": ""
  },
  {
    "code": "171400000X",
    "classification": "Health & Wellness Coach",
    "specialization": ""
  },
  {
    "code": "174H00000X",
    "classification": "Health Educator",
    "specialization": ""
  },
  {
    "code": "175L00000X",
    "classification": "Homeopath",
    "specialization": ""
  },
  {
    "code": "171R00000X",
    "classification": "Interpreter",
    "specialization": ""
  },
  {
    "code": "174N00000X",
    "classification": "Lactation Consultant, Non-RN",
    "specialization": ""
  },
  {
    "code": "175M00000X",
    "classification": "Midwife, Lay",
    "specialization": ""
  },
  {
    "code": "173000000X",
    "classification": "Legal Medicine",
    "specialization": ""
  },
  {
    "code": "172M00000X",
    "classification": "Mechanotherapist",
    "specialization": ""
  },
  {
    "code": "176B00000X",
    "classification": "Midwife",
    "specialization": ""
  },
  {
    "code": "171000000X",
    "classification": "Military Health Care Provider",
    "specialization": ""
  },
  {
    "code": "1710I1002X",
    "classification": "Military Health Care Provider",
    "specialization": "Independent Duty Corpsman"
  },
  {
    "code": "1710I1003X",
    "classification": "Military Health Care Provider",
    "specialization": "Independent Duty Medical Technicians"
  },
  {
    "code": "172P00000X",
    "classification": "Naprapath",
    "specialization": ""
  },
  {
    "code": "175F00000X",
    "classification": "Naturopath",
    "specialization": ""
  },
  {
    "code": "175T00000X",
    "classification": "Peer Specialist",
    "specialization": ""
  },
  {
    "code": "170100000X",
    "classification": "Medical Genetics, Ph.D. Medical Genetics",
    "specialization": ""
  },
  {
    "code": "405300000X",
    "classification": "Prevention Professional",
    "specialization": ""
  },
  {
    "code": "173C00000X",
    "classification": "Reflexologist",
    "specialization": ""
  },
  {
    "code": "173F00000X",
    "classification": "Sleep Specialist, PhD",
    "specialization": ""
  },
  {
    "code": "174400000X",
    "classification": "Specialist",
    "specialization": ""
  },
  {
    "code": "1744G0900X",
    "classification": "Specialist",
    "specialization": "Graphics Designer"
  },
  {
    "code": "1744P3200X",
    "classification": "Specialist",
    "specialization": "Prosthetics Case Management"
  },
  {
    "code": "1744R1103X",
    "classification": "Specialist",
    "specialization": "Research Data Abstracter/Coder"
  },
  {
    "code": "1744R1102X",
    "classification": "Specialist",
    "specialization": "Research Study"
  },
  {
    "code": "174M00000X",
    "classification": "Veterinarian",
    "specialization": ""
  },
  {
    "code": "174MM1900X",
    "classification": "Veterinarian",
    "specialization": "Medical Research"
  },
  {
    "code": "183500000X",
    "classification": "Pharmacist",
    "specialization": ""
  },
  {
    "code": "1835P2201X",
    "classification": "Pharmacist",
    "specialization": "Ambulatory Care"
  },
  {
    "code": "1835C0206X",
    "classification": "Pharmacist",
    "specialization": "Cardiology"
  },
  {
    "code": "1835C0207X",
    "classification": "Pharmacist",
    "specialization": "Compounded Sterile Preparations"
  },
  {
    "code": "1835C0205X",
    "classification": "Pharmacist",
    "specialization": "Critical Care"
  },
  {
    "code": "1835E0208X",
    "classification": "Pharmacist",
    "specialization": "Emergency Medicine"
  },
  {
    "code": "1835G0000X",
    "classification": "Pharmacist",
    "specialization": "General Practice"
  },
  {
    "code": "1835G0303X",
    "classification": "Pharmacist",
    "specialization": "Geriatric"
  },
  {
    "code": "1835I0206X",
    "classification": "Pharmacist",
    "specialization": "Infectious Diseases"
  },
  {
    "code": "1835N0905X",
    "classification": "Pharmacist",
    "specialization": "Nuclear"
  },
  {
    "code": "1835N1003X",
    "classification": "Pharmacist",
    "specialization": "Nutrition Support"
  },
  {
    "code": "1835X0200X",
    "classification": "Pharmacist",
    "specialization": "Oncology"
  },
  {
    "code": "1835P0200X",
    "classification": "Pharmacist",
    "specialization": "Pediatrics"
  },
  {
    "code": "1835P0018X",
    "classification": "Pharmacist",
    "specialization": "Pharmacist Clinician (PhC)/ Clinical Pharmacy Specialist"
  },
  {
    "code": "1835P1200X",
    "classification": "Pharmacist",
    "specialization": "Pharmacotherapy"
  },
  {
    "code": "1835P1300X",
    "classification": "Pharmacist",
    "specialization": "Psychiatric"
  },
  {
    "code": "1835S0206X",
    "classification": "Pharmacist",
    "specialization": "Solid Organ Transplant"
  },
  {
    "code": "183700000X",
    "classification": "Pharmacy Technician",
    "specialization": ""
  },
  {
    "code": "367A00000X",
    "classification": "Advanced Practice Midwife",
    "specialization": ""
  },
  {
    "code": "367H00000X",
    "classification": "Anesthesiologist Assistant",
    "specialization": ""
  },
  {
    "code": "364S00000X",
    "classification": "Clinical Nurse Specialist",
    "specialization": ""
  },
  {
    "code": "364SA2100X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Acute Care"
  },
  {
    "code": "364SA2200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Adult Health"
  },
  {
    "code": "364SC2300X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Chronic Care"
  },
  {
    "code": "364SC1501X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Community Health/Public Health"
  },
  {
    "code": "364SC0200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "364SE0003X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Emergency"
  },
  {
    "code": "364SE1400X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Ethics"
  },
  {
    "code": "364SF0001X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Family Health"
  },
  {
    "code": "364SG0600X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Gerontology"
  },
  {
    "code": "364SH1100X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Holistic"
  },
  {
    "code": "364SH0200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Home Health"
  },
  {
    "code": "364SI0800X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Informatics"
  },
  {
    "code": "364SL0600X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Long-Term Care"
  },
  {
    "code": "364SM0705X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Medical-Surgical"
  },
  {
    "code": "364SN0000X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Neonatal"
  },
  {
    "code": "364SN0800X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Neuroscience"
  },
  {
    "code": "364SX0106X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Occupational Health"
  },
  {
    "code": "364SX0200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Oncology"
  },
  {
    "code": "364SX0204X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Oncology, Pediatrics"
  },
  {
    "code": "364SP0200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Pediatrics"
  },
  {
    "code": "364SP1700X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Perinatal"
  },
  {
    "code": "364SP2800X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Perioperative"
  },
  {
    "code": "364SP0808X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health"
  },
  {
    "code": "364SP0809X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Adult"
  },
  {
    "code": "364SP0807X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Child & Adolescent"
  },
  {
    "code": "364SP0810X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Child & Family"
  },
  {
    "code": "364SP0811X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Chronically Ill"
  },
  {
    "code": "364SP0812X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Community"
  },
  {
    "code": "364SP0813X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Psychiatric/Mental Health, Geropsychiatric"
  },
  {
    "code": "364SR0400X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Rehabilitation"
  },
  {
    "code": "364SS0200X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "School"
  },
  {
    "code": "364ST0500X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Transplantation"
  },
  {
    "code": "364SW0102X",
    "classification": "Clinical Nurse Specialist",
    "specialization": "Women's Health"
  },
  {
    "code": "367500000X",
    "classification": "Nurse Anesthetist, Certified Registered",
    "specialization": ""
  },
  {
    "code": "363L00000X",
    "classification": "Nurse Practitioner",
    "specialization": ""
  },
  {
    "code": "363LA2100X",
    "classification": "Nurse Practitioner",
    "specialization": "Acute Care"
  },
  {
    "code": "363LA2200X",
    "classification": "Nurse Practitioner",
    "specialization": "Adult Health"
  },
  {
    "code": "363LC1500X",
    "classification": "Nurse Practitioner",
    "specialization": "Community Health"
  },
  {
    "code": "363LC0200X",
    "classification": "Nurse Practitioner",
    "specialization": "Critical Care Medicine"
  },
  {
    "code": "363LF0000X",
    "classification": "Nurse Practitioner",
    "specialization": "Family"
  },
  {
    "code": "363LG0600X",
    "classification": "Nurse Practitioner",
    "specialization": "Gerontology"
  },
  {
    "code": "363LN0000X",
    "classification": "Nurse Practitioner",
    "specialization": "Neonatal"
  },
  {
    "code": "363LN0005X",
    "classification": "Nurse Practitioner",
    "specialization": "Neonatal, Critical Care"
  },
  {
    "code": "363LX0001X",
    "classification": "Nurse Practitioner",
    "specialization": "Obstetrics & Gynecology"
  },
  {
    "code": "363LX0106X",
    "classification": "Nurse Practitioner",
    "specialization": "Occupational Health"
  },
  {
    "code": "363LP0200X",
    "classification": "Nurse Practitioner",
    "specialization": "Pediatrics"
  },
  {
    "code": "363LP0222X",
    "classification": "Nurse Practitioner",
    "specialization": "Pediatrics, Critical Care"
  },
  {
    "code": "363LP1700X",
    "classification": "Nurse Practitioner",
    "specialization": "Perinatal"
  },
  {
    "code": "363LP2300X",
    "classification": "Nurse Practitioner",
    "specialization": "Primary Care"
  },
  {
    "code": "363LP0808X",
    "classification": "Nurse Practitioner",
    "specialization": "Psychiatric/Mental Health"
  },
  {
    "code": "363LS0200X",
    "classification": "Nurse Practitioner",
    "specialization": "School"
  },
  {
    "code": "363LW0102X",
    "classification": "Nurse Practitioner",
    "specialization": "Women's Health"
  },
  {
    "code": "363A00000X",
    "classification": "Physician Assistant",
    "specialization": ""
  },
  {
    "code": "363AM0700X",
    "classification": "Physician Assistant",
    "specialization": "Medical"
  },
  {
    "code": "363AS0400X",
    "classification": "Physician Assistant",
    "specialization": "Surgical"
  },
  {
    "code": "211D00000X",
    "classification": "Assistant, Podiatric",
    "specialization": ""
  },
  {
    "code": "213E00000X",
    "classification": "Podiatrist",
    "specialization": ""
  },
  {
    "code": "213ES0103X",
    "classification": "Podiatrist",
    "specialization": "Foot & Ankle Surgery"
  },
  {
    "code": "213ES0131X",
    "classification": "Podiatrist",
    "specialization": "Foot Surgery"
  },
  {
    "code": "213EG0000X",
    "classification": "Podiatrist",
    "specialization": "General Practice"
  },
  {
    "code": "213EP1101X",
    "classification": "Podiatrist",
    "specialization": "Primary Podiatric Medicine"
  },
  {
    "code": "213EP0504X",
    "classification": "Podiatrist",
    "specialization": "Public Medicine"
  },
  {
    "code": "213ER0200X",
    "classification": "Podiatrist",
    "specialization": "Radiology"
  },
  {
    "code": "213ES0000X",
    "classification": "Podiatrist",
    "specialization": "Sports Medicine"
  },
  {
    "code": "229N00000X",
    "classification": "Anaplastologist",
    "specialization": ""
  },
  {
    "code": "221700000X",
    "classification": "Art Therapist",
    "specialization": ""
  },
  {
    "code": "224Y00000X",
    "classification": "Clinical Exercise Physiologist",
    "specialization": ""
  },
  {
    "code": "225600000X",
    "classification": "Dance Therapist",
    "specialization": ""
  },
  {
    "code": "222Q00000X",
    "classification": "Developmental Therapist",
    "specialization": ""
  },
  {
    "code": "226300000X",
    "classification": "Kinesiotherapist",
    "specialization": ""
  },
  {
    "code": "225700000X",
    "classification": "Massage Therapist",
    "specialization": ""
  },
  {
    "code": "224900000X",
    "classification": "Mastectomy Fitter",
    "specialization": ""
  },
  {
    "code": "225A00000X",
    "classification": "Music Therapist",
    "specialization": ""
  },
  {
    "code": "225X00000X",
    "classification": "Occupational Therapist",
    "specialization": ""
  },
  {
    "code": "225XR0403X",
    "classification": "Occupational Therapist",
    "specialization": "Driving and Community Mobility"
  },
  {
    "code": "225XE0001X",
    "classification": "Occupational Therapist",
    "specialization": "Environmental Modification"
  },
  {
    "code": "225XE1200X",
    "classification": "Occupational Therapist",
    "specialization": "Ergonomics"
  },
  {
    "code": "225XF0002X",
    "classification": "Occupational Therapist",
    "specialization": "Feeding, Eating & Swallowing"
  },
  {
    "code": "225XG0600X",
    "classification": "Occupational Therapist",
    "specialization": "Gerontology"
  },
  {
    "code": "225XH1200X",
    "classification": "Occupational Therapist",
    "specialization": "Hand"
  },
  {
    "code": "225XH1300X",
    "classification": "Occupational Therapist",
    "specialization": "Human Factors"
  },
  {
    "code": "225XL0004X",
    "classification": "Occupational Therapist",
    "specialization": "Low Vision"
  },
  {
    "code": "225XM0800X",
    "classification": "Occupational Therapist",
    "specialization": "Mental Health"
  },
  {
    "code": "225XN1300X",
    "classification": "Occupational Therapist",
    "specialization": "Neurorehabilitation"
  },
  {
    "code": "225XP0200X",
    "classification": "Occupational Therapist",
    "specialization": "Pediatrics"
  },
  {
    "code": "225XP0019X",
    "classification": "Occupational Therapist",
    "specialization": "Physical Rehabilitation"
  },
  {
    "code": "224Z00000X",
    "classification": "Occupational Therapy Assistant",
    "specialization": ""
  },
  {
    "code": "224ZR0403X",
    "classification": "Occupational Therapy Assistant",
    "specialization": "Driving and Community Mobility"
  },
  {
    "code": "224ZE0001X",
    "classification": "Occupational Therapy Assistant",
    "specialization": "Environmental Modification"
  },
  {
    "code": "224ZF0002X",
    "classification": "Occupational Therapy Assistant",
    "specialization": "Feeding, Eating & Swallowing"
  },
  {
    "code": "224ZL0004X",
    "classification": "Occupational Therapy Assistant",
    "specialization": "Low Vision"
  },
  {
    "code": "225000000X",
    "classification": "Orthotic Fitter",
    "specialization": ""
  },
  {
    "code": "222Z00000X",
    "classification": "Orthotist",
    "specialization": ""
  },
  {
    "code": "224L00000X",
    "classification": "Pedorthist",
    "specialization": ""
  },
  {
    "code": "225100000X",
    "classification": "Physical Therapist",
    "specialization": ""
  },
  {
    "code": "2251C2600X",
    "classification": "Physical Therapist",
    "specialization": "Cardiopulmonary"
  },
  {
    "code": "2251E1300X",
    "classification": "Physical Therapist",
    "specialization": "Electrophysiology, Clinical"
  },
  {
    "code": "2251E1200X",
    "classification": "Physical Therapist",
    "specialization": "Ergonomics"
  },
  {
    "code": "2251G0304X",
    "classification": "Physical Therapist",
    "specialization": "Geriatrics"
  },
  {
    "code": "2251H1200X",
    "classification": "Physical Therapist",
    "specialization": "Hand"
  },
  {
    "code": "2251H1300X",
    "classification": "Physical Therapist",
    "specialization": "Human Factors"
  },
  {
    "code": "2251N0400X",
    "classification": "Physical Therapist",
    "specialization": "Neurology"
  },
  {
    "code": "2251X0800X",
    "classification": "Physical Therapist",
    "specialization": "Orthopedic"
  },
  {
    "code": "2251P0200X",
    "classification": "Physical Therapist",
    "specialization": "Pediatrics"
  },
  {
    "code": "2251S0007X",
    "classification": "Physical Therapist",
    "specialization": "Sports"
  },
  {
    "code": "225200000X",
    "classification": "Physical Therapy Assistant",
    "specialization": ""
  },
  {
    "code": "224P00000X",
    "classification": "Prosthetist",
    "specialization": ""
  },
  {
    "code": "225B00000X",
    "classification": "Pulmonary Function Technologist",
    "specialization": ""
  },
  {
    "code": "225800000X",
    "classification": "Recreation Therapist",
    "specialization": ""
  },
  {
    "code": "226000000X",
    "classification": "Recreational Therapist Assistant",
    "specialization": ""
  },
  {
    "code": "225C00000X",
    "classification": "Rehabilitation Counselor",
    "specialization": ""
  },
  {
    "code": "225CA2400X",
    "classification": "Rehabilitation Counselor",
    "specialization": "Assistive Technology Practitioner"
  },
  {
    "code": "225CA2500X",
    "classification": "Rehabilitation Counselor",
    "specialization": "Assistive Technology Supplier"
  },
  {
    "code": "225CX0006X",
    "classification": "Rehabilitation Counselor",
    "specialization": "Orientation and Mobility Training Provider"
  },
  {
    "code": "225400000X",
    "classification": "Rehabilitation Practitioner",
    "specialization": ""
  },
  {
    "code": "227800000X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": ""
  },
  {
    "code": "2278C0205X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Critical Care"
  },
  {
    "code": "2278E1000X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Educational"
  },
  {
    "code": "2278E0002X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Emergency Care"
  },
  {
    "code": "2278G1100X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "General Care"
  },
  {
    "code": "2278G0305X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Geriatric Care"
  },
  {
    "code": "2278H0200X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Home Health"
  },
  {
    "code": "2278P3900X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Neonatal/Pediatrics"
  },
  {
    "code": "2278P3800X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Palliative/Hospice"
  },
  {
    "code": "2278P4000X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Patient Transport"
  },
  {
    "code": "2278P1004X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Pulmonary Diagnostics"
  },
  {
    "code": "2278P1006X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Pulmonary Function Technologist"
  },
  {
    "code": "2278P1005X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "Pulmonary Rehabilitation"
  },
  {
    "code": "2278S1500X",
    "classification": "Respiratory Therapist, Certified",
    "specialization": "SNF/Subacute Care"
  },
  {
    "code": "227900000X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": ""
  },
  {
    "code": "2279C0205X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Critical Care"
  },
  {
    "code": "2279E1000X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Educational"
  },
  {
    "code": "2279E0002X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Emergency Care"
  },
  {
    "code": "2279G1100X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "General Care"
  },
  {
    "code": "2279G0305X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Geriatric Care"
  },
  {
    "code": "2279H0200X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Home Health"
  },
  {
    "code": "2279P3900X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Neonatal/Pediatrics"
  },
  {
    "code": "2279P3800X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Palliative/Hospice"
  },
  {
    "code": "2279P4000X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Patient Transport"
  },
  {
    "code": "2279P1004X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Pulmonary Diagnostics"
  },
  {
    "code": "2279P1006X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Pulmonary Function Technologist"
  },
  {
    "code": "2279P1005X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "Pulmonary Rehabilitation"
  },
  {
    "code": "2279S1500X",
    "classification": "Respiratory Therapist, Registered",
    "specialization": "SNF/Subacute Care"
  },
  {
    "code": "225500000X",
    "classification": "Specialist/Technologist",
    "specialization": ""
  },
  {
    "code": "2255A2300X",
    "classification": "Specialist/Technologist",
    "specialization": "Athletic Trainer"
  },
  {
    "code": "2255R0406X",
    "classification": "Specialist/Technologist",
    "specialization": "Rehabilitation, Blind"
  },
  {
    "code": "231H00000X",
    "classification": "Audiologist",
    "specialization": ""
  },
  {
    "code": "231HA2400X",
    "classification": "Audiologist",
    "specialization": "Assistive Technology Practitioner"
  },
  {
    "code": "231HA2500X",
    "classification": "Audiologist",
    "specialization": "Assistive Technology Supplier"
  },
  {
    "code": "237600000X",
    "classification": "Audiologist-Hearing Aid Fitter",
    "specialization": ""
  },
  {
    "code": "237700000X",
    "classification": "Hearing Instrument Specialist",
    "specialization": ""
  },
  {
    "code": "235500000X",
    "classification": "Specialist/Technologist",
    "specialization": ""
  },
  {
    "code": "2355A2700X",
    "classification": "Specialist/Technologist",
    "specialization": "Audiology Assistant"
  },
  {
    "code": "2355S0801X",
    "classification": "Specialist/Technologist",
    "specialization": "Speech-Language Assistant"
  },
  {
    "code": "235Z00000X",
    "classification": "Speech-Language Pathologist",
    "specialization": ""
  },
  {
    "code": "390200000X",
    "classification": "Student in an Organized Health Care Education/Training Program",
    "specialization": ""
  },
  {
    "code": "242T00000X",
    "classification": "Perfusionist",
    "specialization": ""
  },
  {
    "code": "247100000X",
    "classification": "Radiologic Technologist",
    "specialization": ""
  },
  {
    "code": "2471B0102X",
    "classification": "Radiologic Technologist",
    "specialization": "Bone Densitometry"
  },
  {
    "code": "2471C1106X",
    "classification": "Radiologic Technologist",
    "specialization": "Cardiac-Interventional Technology"
  },
  {
    "code": "2471C1101X",
    "classification": "Radiologic Technologist",
    "specialization": "Cardiovascular-Interventional Technology"
  },
  {
    "code": "2471C3401X",
    "classification": "Radiologic Technologist",
    "specialization": "Computed Tomography"
  },
  {
    "code": "2471M1202X",
    "classification": "Radiologic Technologist",
    "specialization": "Magnetic Resonance Imaging"
  },
  {
    "code": "2471M2300X",
    "classification": "Radiologic Technologist",
    "specialization": "Mammography"
  },
  {
    "code": "2471N0900X",
    "classification": "Radiologic Technologist",
    "specialization": "Nuclear Medicine Technology"
  },
  {
    "code": "2471Q0001X",
    "classification": "Radiologic Technologist",
    "specialization": "Quality Management"
  },
  {
    "code": "2471R0002X",
    "classification": "Radiologic Technologist",
    "specialization": "Radiation Therapy"
  },
  {
    "code": "2471C3402X",
    "classification": "Radiologic Technologist",
    "specialization": "Radiography"
  },
  {
    "code": "2471S1302X",
    "classification": "Radiologic Technologist",
    "specialization": "Sonography"
  },
  {
    "code": "2471V0105X",
    "classification": "Radiologic Technologist",
    "specialization": "Vascular Sonography"
  },
  {
    "code": "2471V0106X",
    "classification": "Radiologic Technologist",
    "specialization": "Vascular-Interventional Technology"
  },
  {
    "code": "243U00000X",
    "classification": "Radiology Practitioner Assistant",
    "specialization": ""
  },
  {
    "code": "246X00000X",
    "classification": "Specialist/Technologist Cardiovascular",
    "specialization": ""
  },
  {
    "code": "246XC2901X",
    "classification": "Specialist/Technologist Cardiovascular",
    "specialization": "Cardiovascular Invasive Specialist"
  },
  {
    "code": "246XS1301X",
    "classification": "Specialist/Technologist Cardiovascular",
    "specialization": "Sonography"
  },
  {
    "code": "246XC2903X",
    "classification": "Specialist/Technologist Cardiovascular",
    "specialization": "Vascular Specialist"
  },
  {
    "code": "246Y00000X",
    "classification": "Specialist/Technologist, Health Information",
    "specialization": ""
  },
  {
    "code": "246YC3301X",
    "classification": "Specialist/Technologist, Health Information",
    "specialization": "Coding Specialist, Hospital Based"
  },
  {
    "code": "246YC3302X",
    "classification": "Specialist/Technologist, Health Information",
    "specialization": "Coding Specialist, Physician Office Based"
  },
  {
    "code": "246YR1600X",
    "classification": "Specialist/Technologist, Health Information",
    "specialization": "Registered Record Administrator"
  },
  {
    "code": "246Z00000X",
    "classification": "Specialist/Technologist, Other",
    "specialization": ""
  },
  {
    "code": "246ZA2600X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Art, Medical"
  },
  {
    "code": "246ZB0500X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Biochemist"
  },
  {
    "code": "246ZB0301X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Biomedical Engineering"
  },
  {
    "code": "246ZB0302X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Biomedical Photographer"
  },
  {
    "code": "246ZB0600X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Biostatistician"
  },
  {
    "code": "246ZE0500X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "EEG"
  },
  {
    "code": "246ZE0600X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Electroneurodiagnostic"
  },
  {
    "code": "246ZG1000X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Geneticist, Medical (PhD)"
  },
  {
    "code": "246ZG0701X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Graphics Methods"
  },
  {
    "code": "246ZI1000X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Illustration, Medical"
  },
  {
    "code": "246ZN0300X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Nephrology"
  },
  {
    "code": "246ZX2200X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Orthopedic Assistant"
  },
  {
    "code": "246ZC0007X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Surgical Assistant"
  },
  {
    "code": "246ZS0410X",
    "classification": "Specialist/Technologist, Other",
    "specialization": "Surgical Technologist"
  },
  {
    "code": "246Q00000X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": ""
  },
  {
    "code": "246QB0000X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Blood Banking"
  },
  {
    "code": "246QC1000X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Chemistry"
  },
  {
    "code": "246QC2700X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Cytotechnology"
  },
  {
    "code": "246QH0401X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Hemapheresis Practitioner"
  },
  {
    "code": "246QH0000X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Hematology"
  },
  {
    "code": "246QH0600X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Histology"
  },
  {
    "code": "246QI0000X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Immunology"
  },
  {
    "code": "246QL0900X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Laboratory Management"
  },
  {
    "code": "246QL0901X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Laboratory Management, Diplomate"
  },
  {
    "code": "246QM0706X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Medical Technologist"
  },
  {
    "code": "246QM0900X",
    "classification": "Specialist/Technologist, Pathology",
    "specialization": "Microbiology"
  },
  {
    "code": "246W00000X",
    "classification": "Technician, Cardiology",
    "specialization": ""
  },
  {
    "code": "247000000X",
    "classification": "Technician, Health Information",
    "specialization": ""
  },
  {
    "code": "2470A2800X",
    "classification": "Technician, Health Information",
    "specialization": "Assistant Record Technician"
  },
  {
    "code": "247200000X",
    "classification": "Technician, Other",
    "specialization": ""
  },
  {
    "code": "2472B0301X",
    "classification": "Technician, Other",
    "specialization": "Biomedical Engineering"
  },
  {
    "code": "2472D0500X",
    "classification": "Technician, Other",
    "specialization": "Darkroom"
  },
  {
    "code": "2472E0500X",
    "classification": "Technician, Other",
    "specialization": "EEG"
  },
  {
    "code": "2472R0900X",
    "classification": "Technician, Other",
    "specialization": "Renal Dialysis"
  },
  {
    "code": "2472V0600X",
    "classification": "Technician, Other",
    "specialization": "Veterinary"
  },
  {
    "code": "246R00000X",
    "classification": "Technician, Pathology",
    "specialization": ""
  },
  {
    "code": "247ZC0005X",
    "classification": "Technician, Pathology",
    "specialization": "Clinical Laboratory Director, Non-physician"
  },
  {
    "code": "246RH0600X",
    "classification": "Technician, Pathology",
    "specialization": "Histology"
  },
  {
    "code": "246RM2200X",
    "classification": "Technician, Pathology",
    "specialization": "Medical Laboratory"
  },
  {
    "code": "246RP1900X",
    "classification": "Technician, Pathology",
    "specialization": "Phlebotomy"
  },
  {
    "code": "251B00000X",
    "classification": "Case Management",
    "specialization": ""
  },
  {
    "code": "251S00000X",
    "classification": "Community/Behavioral Health",
    "specialization": ""
  },
  {
    "code": "251C00000X",
    "classification": "Day Training, Developmentally Disabled Services",
    "specialization": ""
  },
  {
    "code": "252Y00000X",
    "classification": "Early Intervention Provider Agency",
    "specialization": ""
  },
  {
    "code": "253J00000X",
    "classification": "Foster Care Agency",
    "specialization": ""
  },
  {
    "code": "251E00000X",
    "classification": "Home Health",
    "specialization": ""
  },
  {
    "code": "251F00000X",
    "classification": "Home Infusion",
    "specialization": ""
  },
  {
    "code": "251G00000X",
    "classification": "Hospice Care, Community Based",
    "specialization": ""
  },
  {
    "code": "253Z00000X",
    "classification": "In Home Supportive Care",
    "specialization": ""
  },
  {
    "code": "251300000X",
    "classification": "Local Education Agency (LEA)",
    "specialization": ""
  },
  {
    "code": "251J00000X",
    "classification": "Nursing Care",
    "specialization": ""
  },
  {
    "code": "251T00000X",
    "classification": "Program of All-Inclusive Care for the Elderly (PACE) Provider Organization",
    "specialization": ""
  },
  {
    "code": "251K00000X",
    "classification": "Public Health or Welfare",
    "specialization": ""
  },
  {
    "code": "251X00000X",
    "classification": "Supports Brokerage",
    "specialization": ""
  },
  {
    "code": "251V00000X",
    "classification": "Voluntary or Charitable",
    "specialization": ""
  },
  {
    "code": "261Q00000X",
    "classification": "Clinic/Center",
    "specialization": ""
  },
  {
    "code": "261QM0855X",
    "classification": "Clinic/Center",
    "specialization": "Adolescent and Children Mental Health"
  },
  {
    "code": "261QA0600X",
    "classification": "Clinic/Center",
    "specialization": "Adult Day Care"
  },
  {
    "code": "261QM0850X",
    "classification": "Clinic/Center",
    "specialization": "Adult Mental Health"
  },
  {
    "code": "261QA0005X",
    "classification": "Clinic/Center",
    "specialization": "Ambulatory Family Planning Facility"
  },
  {
    "code": "261QA0006X",
    "classification": "Clinic/Center",
    "specialization": "Ambulatory Fertility Facility"
  },
  {
    "code": "261QA1903X",
    "classification": "Clinic/Center",
    "specialization": "Ambulatory Surgical"
  },
  {
    "code": "261QA0900X",
    "classification": "Clinic/Center",
    "specialization": "Amputee"
  },
  {
    "code": "261QA3000X",
    "classification": "Clinic/Center",
    "specialization": "Augmentative Communication"
  },
  {
    "code": "261QB0400X",
    "classification": "Clinic/Center",
    "specialization": "Birthing"
  },
  {
    "code": "261QC1500X",
    "classification": "Clinic/Center",
    "specialization": "Community Health"
  },
  {
    "code": "261QC1800X",
    "classification": "Clinic/Center",
    "specialization": "Corporate Health"
  },
  {
    "code": "261QC0050X",
    "classification": "Clinic/Center",
    "specialization": "Critical Access Hospital"
  },
  {
    "code": "261QD0000X",
    "classification": "Clinic/Center",
    "specialization": "Dental"
  },
  {
    "code": "261QD1600X",
    "classification": "Clinic/Center",
    "specialization": "Developmental Disabilities"
  },
  {
    "code": "261QE0002X",
    "classification": "Clinic/Center",
    "specialization": "Emergency Care"
  },
  {
    "code": "261QE0700X",
    "classification": "Clinic/Center",
    "specialization": "End-Stage Renal Disease (ESRD) Treatment"
  },
  {
    "code": "261QE0800X",
    "classification": "Clinic/Center",
    "specialization": "Endoscopy"
  },
  {
    "code": "261QF0050X",
    "classification": "Clinic/Center",
    "specialization": "Family Planning, Non-Surgical"
  },
  {
    "code": "261QF0400X",
    "classification": "Clinic/Center",
    "specialization": "Federally Qualified Health Center (FQHC)"
  },
  {
    "code": "261QG0250X",
    "classification": "Clinic/Center",
    "specialization": "Genetics"
  },
  {
    "code": "261QH0100X",
    "classification": "Clinic/Center",
    "specialization": "Health Service"
  },
  {
    "code": "261QH0700X",
    "classification": "Clinic/Center",
    "specialization": "Hearing and Speech"
  },
  {
    "code": "261QI0500X",
    "classification": "Clinic/Center",
    "specialization": "Infusion Therapy"
  },
  {
    "code": "261QL0400X",
    "classification": "Clinic/Center",
    "specialization": "Lithotripsy"
  },
  {
    "code": "261QM1200X",
    "classification": "Clinic/Center",
    "specialization": "Magnetic Resonance Imaging (MRI)"
  },
  {
    "code": "261QM2500X",
    "classification": "Clinic/Center",
    "specialization": "Medical Specialty"
  },
  {
    "code": "261QM3000X",
    "classification": "Clinic/Center",
    "specialization": "Medically Fragile Infants and Children Day Care"
  },
  {
    "code": "261QM0801X",
    "classification": "Clinic/Center",
    "specialization": "Mental Health (Including Community Mental Health Center)"
  },
  {
    "code": "261QM2800X",
    "classification": "Clinic/Center",
    "specialization": "Methadone"
  },
  {
    "code": "261QM1000X",
    "classification": "Clinic/Center",
    "specialization": "Migrant Health"
  },
  {
    "code": "261QM1103X",
    "classification": "Clinic/Center",
    "specialization": "Military Ambulatory Procedure Visits Operational (Transportable)"
  },
  {
    "code": "261QM1101X",
    "classification": "Clinic/Center",
    "specialization": "Military and U.S. Coast Guard Ambulatory Procedure"
  },
  {
    "code": "261QM1102X",
    "classification": "Clinic/Center",
    "specialization": "Military Outpatient Operational (Transportable) Component"
  },
  {
    "code": "261QM1100X",
    "classification": "Clinic/Center",
    "specialization": "Military/U.S. Coast Guard Outpatient"
  },
  {
    "code": "261QM1300X",
    "classification": "Clinic/Center",
    "specialization": "Multi-Specialty"
  },
  {
    "code": "261QX0100X",
    "classification": "Clinic/Center",
    "specialization": "Occupational Medicine"
  },
  {
    "code": "261QX0200X",
    "classification": "Clinic/Center",
    "specialization": "Oncology"
  },
  {
    "code": "261QX0203X",
    "classification": "Clinic/Center",
    "specialization": "Oncology, Radiation"
  },
  {
    "code": "261QS0132X",
    "classification": "Clinic/Center",
    "specialization": "Ophthalmologic Surgery"
  },
  {
    "code": "261QS0112X",
    "classification": "Clinic/Center",
    "specialization": "Oral and Maxillofacial Surgery"
  },
  {
    "code": "261QP3300X",
    "classification": "Clinic/Center",
    "specialization": "Pain"
  },
  {
    "code": "261QP2000X",
    "classification": "Clinic/Center",
    "specialization": "Physical Therapy"
  },
  {
    "code": "261QP1100X",
    "classification": "Clinic/Center",
    "specialization": "Podiatric"
  },
  {
    "code": "261QP2300X",
    "classification": "Clinic/Center",
    "specialization": "Primary Care"
  },
  {
    "code": "261QP2400X",
    "classification": "Clinic/Center",
    "specialization": "Prison Health"
  },
  {
    "code": "261QP0904X",
    "classification": "Clinic/Center",
    "specialization": "Public Health, Federal"
  },
  {
    "code": "261QP0905X",
    "classification": "Clinic/Center",
    "specialization": "Public Health, State or Local"
  },
  {
    "code": "261QR0200X",
    "classification": "Clinic/Center",
    "specialization": "Radiology"
  },
  {
    "code": "261QR0206X",
    "classification": "Clinic/Center",
    "specialization": "Radiology, Mammography"
  },
  {
    "code": "261QR0208X",
    "classification": "Clinic/Center",
    "specialization": "Radiology, Mobile"
  },
  {
    "code": "261QR0207X",
    "classification": "Clinic/Center",
    "specialization": "Radiology, Mobile Mammography"
  },
  {
    "code": "261QR0800X",
    "classification": "Clinic/Center",
    "specialization": "Recovery Care"
  },
  {
    "code": "261QR0400X",
    "classification": "Clinic/Center",
    "specialization": "Rehabilitation"
  },
  {
    "code": "261QR0404X",
    "classification": "Clinic/Center",
    "specialization": "Rehabilitation, Cardiac Facilities"
  },
  {
    "code": "261QR0401X",
    "classification": "Clinic/Center",
    "specialization": "Rehabilitation, Comprehensive Outpatient Rehabilitation Facility (CORF)"
  },
  {
    "code": "261QR0405X",
    "classification": "Clinic/Center",
    "specialization": "Rehabilitation, Substance Use Disorder"
  },
  {
    "code": "261QR1100X",
    "classification": "Clinic/Center",
    "specialization": "Research"
  },
  {
    "code": "261QR1300X",
    "classification": "Clinic/Center",
    "specialization": "Rural Health"
  },
  {
    "code": "261QS1200X",
    "classification": "Clinic/Center",
    "specialization": "Sleep Disorder Diagnostic"
  },
  {
    "code": "261QS1000X",
    "classification": "Clinic/Center",
    "specialization": "Student Health"
  },
  {
    "code": "261QU0200X",
    "classification": "Clinic/Center",
    "specialization": "Urgent Care"
  },
  {
    "code": "261QV0200X",
    "classification": "Clinic/Center",
    "specialization": "VA"
  },
  {
    "code": "273100000X",
    "classification": "Epilepsy Unit",
    "specialization": ""
  },
  {
    "code": "275N00000X",
    "classification": "Medicare Defined Swing Bed Unit",
    "specialization": ""
  },
  {
    "code": "273R00000X",
    "classification": "Psychiatric Unit",
    "specialization": ""
  },
  {
    "code": "273Y00000X",
    "classification": "Rehabilitation Unit",
    "specialization": ""
  },
  {
    "code": "276400000X",
    "classification": "Rehabilitation, Substance Use Disorder Unit",
    "specialization": ""
  },
  {
    "code": "287300000X",
    "classification": "Christian Science Sanitorium",
    "specialization": ""
  },
  {
    "code": "281P00000X",
    "classification": "Chronic Disease Hospital",
    "specialization": ""
  },
  {
    "code": "281PC2000X",
    "classification": "Chronic Disease Hospital",
    "specialization": "Children"
  },
  {
    "code": "282N00000X",
    "classification": "General Acute Care Hospital",
    "specialization": ""
  },
  {
    "code": "282NC2000X",
    "classification": "General Acute Care Hospital",
    "specialization": "Children"
  },
  {
    "code": "282NC0060X",
    "classification": "General Acute Care Hospital",
    "specialization": "Critical Access"
  },
  {
    "code": "282NR1301X",
    "classification": "General Acute Care Hospital",
    "specialization": "Rural"
  },
  {
    "code": "282NW0100X",
    "classification": "General Acute Care Hospital",
    "specialization": "Women"
  },
  {
    "code": "282E00000X",
    "classification": "Long Term Care Hospital",
    "specialization": ""
  },
  {
    "code": "286500000X",
    "classification": "Military Hospital",
    "specialization": ""
  },
  {
    "code": "2865C1500X",
    "classification": "Military Hospital",
    "specialization": "Community Health"
  },
  {
    "code": "2865M2000X",
    "classification": "Military Hospital",
    "specialization": "Military General Acute Care Hospital"
  },
  {
    "code": "2865X1600X",
    "classification": "Military Hospital",
    "specialization": "Military General Acute Care Hospital. Operational (Transportable)"
  },
  {
    "code": "283Q00000X",
    "classification": "Psychiatric Hospital",
    "specialization": ""
  },
  {
    "code": "283X00000X",
    "classification": "Rehabilitation Hospital",
    "specialization": ""
  },
  {
    "code": "283XC2000X",
    "classification": "Rehabilitation Hospital",
    "specialization": "Children"
  },
  {
    "code": "282J00000X",
    "classification": "Religious Nonmedical Health Care Institution",
    "specialization": ""
  },
  {
    "code": "284300000X",
    "classification": "Special Hospital",
    "specialization": ""
  },
  {
    "code": "291U00000X",
    "classification": "Clinical Medical Laboratory",
    "specialization": ""
  },
  {
    "code": "292200000X",
    "classification": "Dental Laboratory",
    "specialization": ""
  },
  {
    "code": "291900000X",
    "classification": "Military Clinical Medical Laboratory",
    "specialization": ""
  },
  {
    "code": "293D00000X",
    "classification": "Physiological Laboratory",
    "specialization": ""
  },
  {
    "code": "302F00000X",
    "classification": "Exclusive Provider Organization",
    "specialization": ""
  },
  {
    "code": "302R00000X",
    "classification": "Health Maintenance Organization",
    "specialization": ""
  },
  {
    "code": "305S00000X",
    "classification": "Point of Service",
    "specialization": ""
  },
  {
    "code": "305R00000X",
    "classification": "Preferred Provider Organization",
    "specialization": ""
  },
  {
    "code": "311500000X",
    "classification": "Alzheimer Center (Dementia Center)",
    "specialization": ""
  },
  {
    "code": "310400000X",
    "classification": "Assisted Living Facility",
    "specialization": ""
  },
  {
    "code": "3104A0630X",
    "classification": "Assisted Living Facility",
    "specialization": "Assisted Living, Behavioral Disturbances"
  },
  {
    "code": "3104A0625X",
    "classification": "Assisted Living Facility",
    "specialization": "Assisted Living, Mental Illness"
  },
  {
    "code": "317400000X",
    "classification": "Christian Science Facility",
    "specialization": ""
  },
  {
    "code": "311Z00000X",
    "classification": "Custodial Care Facility",
    "specialization": ""
  },
  {
    "code": "311ZA0620X",
    "classification": "Custodial Care Facility",
    "specialization": "Adult Care Home"
  },
  {
    "code": "315D00000X",
    "classification": "Hospice, Inpatient",
    "specialization": ""
  },
  {
    "code": "315P00000X",
    "classification": "Intermediate Care Facility, Intellectual Disabilities",
    "specialization": ""
  },
  {
    "code": "310500000X",
    "classification": "Intermediate Care Facility, Mental Illness",
    "specialization": ""
  },
  {
    "code": "313M00000X",
    "classification": "Nursing Facility/Intermediate Care Facility",
    "specialization": ""
  },
  {
    "code": "314000000X",
    "classification": "Skilled Nursing Facility",
    "specialization": ""
  },
  {
    "code": "3140N1450X",
    "classification": "Skilled Nursing Facility",
    "specialization": "Nursing Care, Pediatric"
  },
  {
    "code": "177F00000X",
    "classification": "Lodging",
    "specialization": ""
  },
  {
    "code": "174200000X",
    "classification": "Meals",
    "specialization": ""
  },
  {
    "code": "320800000X",
    "classification": "Community Based Residential Treatment Facility, Mental Illness",
    "specialization": ""
  },
  {
    "code": "320900000X",
    "classification": "Community Based Residential Treatment Facility, Intellectual and/or Developmental Disabilities",
    "specialization": ""
  },
  {
    "code": "323P00000X",
    "classification": "Psychiatric Residential Treatment Facility",
    "specialization": ""
  },
  {
    "code": "322D00000X",
    "classification": "Residential Treatment Facility, Emotionally Disturbed Children",
    "specialization": ""
  },
  {
    "code": "320600000X",
    "classification": "Residential Treatment Facility, Intellectual and/or Developmental Disabilities",
    "specialization": ""
  },
  {
    "code": "320700000X",
    "classification": "Residential Treatment Facility, Physical Disabilities",
    "specialization": ""
  },
  {
    "code": "324500000X",
    "classification": "Substance Abuse Rehabilitation Facility",
    "specialization": ""
  },
  {
    "code": "3245S0500X",
    "classification": "Substance Abuse Rehabilitation Facility",
    "specialization": "Substance Abuse Treatment, Children"
  },
  {
    "code": "385H00000X",
    "classification": "Respite Care",
    "specialization": ""
  },
  {
    "code": "385HR2050X",
    "classification": "Respite Care",
    "specialization": "Respite Care Camp"
  },
  {
    "code": "385HR2055X",
    "classification": "Respite Care",
    "specialization": "Respite Care, Mental Illness, Child"
  },
  {
    "code": "385HR2060X",
    "classification": "Respite Care",
    "specialization": "Respite Care, Intellectual and/or Developmental Disabilities, Child"
  },
  {
    "code": "385HR2065X",
    "classification": "Respite Care",
    "specialization": "Respite Care, Physical Disabilities, Child"
  },
  {
    "code": "331L00000X",
    "classification": "Blood Bank",
    "specialization": ""
  },
  {
    "code": "332100000X",
    "classification": "Department of Veterans Affairs (VA) Pharmacy",
    "specialization": ""
  },
  {
    "code": "332B00000X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": ""
  },
  {
    "code": "332BC3200X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": "Customized Equipment"
  },
  {
    "code": "332BD1200X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": "Dialysis Equipment & Supplies"
  },
  {
    "code": "332BN1400X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": "Nursing Facility Supplies"
  },
  {
    "code": "332BX2000X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": "Oxygen Equipment & Supplies"
  },
  {
    "code": "332BP3500X",
    "classification": "Durable Medical Equipment & Medical Supplies",
    "specialization": "Parenteral & Enteral Nutrition"
  },
  {
    "code": "333300000X",
    "classification": "Emergency Response System Companies",
    "specialization": ""
  },
  {
    "code": "332G00000X",
    "classification": "Eye Bank",
    "specialization": ""
  },
  {
    "code": "332H00000X",
    "classification": "Eyewear Supplier",
    "specialization": ""
  },
  {
    "code": "332S00000X",
    "classification": "Hearing Aid Equipment",
    "specialization": ""
  },
  {
    "code": "332U00000X",
    "classification": "Home Delivered Meals",
    "specialization": ""
  },
  {
    "code": "332800000X",
    "classification": "Indian Health Service/Tribal/Urban Indian Health (I/T/U) Pharmacy",
    "specialization": ""
  },
  {
    "code": "335G00000X",
    "classification": "Medical Foods Supplier",
    "specialization": ""
  },
  {
    "code": "332000000X",
    "classification": "Military/U.S. Coast Guard Pharmacy",
    "specialization": ""
  },
  {
    "code": "332900000X",
    "classification": "Non-Pharmacy Dispensing Site",
    "specialization": ""
  },
  {
    "code": "335U00000X",
    "classification": "Organ Procurement Organization",
    "specialization": ""
  },
  {
    "code": "333600000X",
    "classification": "Pharmacy",
    "specialization": ""
  },
  {
    "code": "3336C0002X",
    "classification": "Pharmacy",
    "specialization": "Clinic Pharmacy"
  },
  {
    "code": "3336C0003X",
    "classification": "Pharmacy",
    "specialization": "Community/Retail Pharmacy"
  },
  {
    "code": "3336C0004X",
    "classification": "Pharmacy",
    "specialization": "Compounding Pharmacy"
  },
  {
    "code": "3336H0001X",
    "classification": "Pharmacy",
    "specialization": "Home Infusion Therapy Pharmacy"
  },
  {
    "code": "3336I0012X",
    "classification": "Pharmacy",
    "specialization": "Institutional Pharmacy"
  },
  {
    "code": "3336L0003X",
    "classification": "Pharmacy",
    "specialization": "Long Term Care Pharmacy"
  },
  {
    "code": "3336M0002X",
    "classification": "Pharmacy",
    "specialization": "Mail Order Pharmacy"
  },
  {
    "code": "3336M0003X",
    "classification": "Pharmacy",
    "specialization": "Managed Care Organization Pharmacy"
  },
  {
    "code": "3336N0007X",
    "classification": "Pharmacy",
    "specialization": "Nuclear Pharmacy"
  },
  {
    "code": "3336S0011X",
    "classification": "Pharmacy",
    "specialization": "Specialty Pharmacy"
  },
  {
    "code": "335V00000X",
    "classification": "Portable X-ray and/or Other Portable Diagnostic Imaging Supplier",
    "specialization": ""
  },
  {
    "code": "335E00000X",
    "classification": "Prosthetic/Orthotic Supplier",
    "specialization": ""
  },
  {
    "code": "344800000X",
    "classification": "Air Carrier",
    "specialization": ""
  },
  {
    "code": "341600000X",
    "classification": "Ambulance",
    "specialization": ""
  },
  {
    "code": "3416A0800X",
    "classification": "Ambulance",
    "specialization": "Air Transport"
  },
  {
    "code": "3416L0300X",
    "classification": "Ambulance",
    "specialization": "Land Transport"
  },
  {
    "code": "3416S0300X",
    "classification": "Ambulance",
    "specialization": "Water Transport"
  },
  {
    "code": "347B00000X",
    "classification": "Bus",
    "specialization": ""
  },
  {
    "code": "341800000X",
    "classification": "Military/U.S. Coast Guard Transport",
    "specialization": ""
  },
  {
    "code": "3418M1120X",
    "classification": "Military/U.S. Coast Guard Transport",
    "specialization": "Military or U.S. Coast Guard Ambulance, Air Transport"
  },
  {
    "code": "3418M1110X",
    "classification": "Military/U.S. Coast Guard Transport",
    "specialization": "Military or U.S. Coast Guard Ambulance, Ground Transport"
  },
  {
    "code": "3418M1130X",
    "classification": "Military/U.S. Coast Guard Transport",
    "specialization": "Military or U.S. Coast Guard Ambulance, Water Transport"
  },
  {
    "code": "343900000X",
    "classification": "Non-emergency Medical Transport (VAN)",
    "specialization": ""
  },
  {
    "code": "347C00000X",
    "classification": "Private Vehicle",
    "specialization": ""
  },
  {
    "code": "343800000X",
    "classification": "Secured Medical Transport (VAN)",
    "specialization": ""
  },
  {
    "code": "344600000X",
    "classification": "Taxi",
    "specialization": ""
  },
  {
    "code": "347D00000X",
    "classification": "Train",
    "specialization": ""
  },
  {
    "code": "347E00000X",
    "classification": "Transportation Broker",
    "specialization": ""
  },
  {
    "code": "342000000X",
    "classification": "Transportation Network Company",
    "specialization": ""
  }
];

export const documentSectionsMap = {
  "Care Plan": {
    required: ["Title", "Health Concerns Section", "Goals Section"],
    recommended: ["Health Status Evaluations and Outcomes Section"],
    additional: ["Activities Section", "Advance Directives Section"]
  },
  "Consultation Note": {
    required: [
      ["Reason for Referral Section", "Reason for Visit Section"],
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "History of Present Illness Section",
      "Allergies And Intolerances Section",
      "Problem Section"
    ],
    recommended: [
      "Medications Section",
      "Physical Exam Section",
      "Results Section"
    ],
    additional: [
      "Chief Complaint Section",
      "Chief Complaint and Reason for Visit Section",
      "Family History Section",
      "General Status Section",
      "Past Medical History",
      "Immunizations Section",
      "Procedures Section",
      "Social History Section",
      "Vital Signs Section",
      "Advance Directives Section",
      "Functional Status Section",
      "Review of Systems Section",
      "Medical Equipment Section",
      "Mental Status Section",
      "Nutrition Section"
    ]
  },
  "Continuity of Care Document (CCD)": {
    required: [
      "Allergies And Intolerances Section",
      "Medications Section",
      "Problem Section",
      "Results Section",
      "Social History Section",
      "Vital Signs Section"
    ],
    recommended: [
      "Procedures Section",
      "Plan of Treatment Section"
    ],
    additional: [
      "Advance Directives Section",
      "Encounters Section",
      "Family History Section",
      "Functional Status Section",
      "Immunizations Section",
      "Medical Equipment Section",
      "Payers Section",
      "Mental Status Section",
      "Nutrition Section"
    ]
  },
  "Discharge Summary": {
    required: [
      "Allergies And Intolerances Section",
      "Hospital Course Section",
      "Discharge Diagnosis Section",
      "Plan of Treatment Section",
      "Hospital Consultations Section",
      "Procedures Section",
      "Results Section"
    ],
    recommended: [
      "Discharge Medications Section"
    ],
    additional: [
      "Chief Complaint Section",
      "Chief Complaint and Reason for Visit Section",
      "Nutrition Section",
      "Family History Section",
      "Functional Status Section",
      "Past Medical History",
      "History of Present Illness Section",
      "Admission Diagnosis Section",
      "Admission Medications Section Entries Optional",
      "Hospital Discharge Instructions Section",
      "Hospital Discharge Physical Section",
      "Hospital Discharge Studies Summary Section",
      "Immunizations Section",
      "Problem Section",
      "Reason for Visit Section",
      "Review of Systems Section",
      "Social History Section",
      "Vital Signs Section",
      "Advance Directives Section"
    ]
  },
  "History and Physical (H&P)": {
    required: [
      ["Chief Complaint and Reason for Visit Section", ["Chief Complaint Section", "Reason for Visit Section"]],
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "Allergies And Intolerances Section",
      "Family History Section",
      "General Status Section",
      "Past Medical History",
      "Medications Section",
      "Physical Exam Section",
      "Results Section",
      "Review of Systems Section",
      "Social History Section",
      "Vital Signs Section"
    ],
    recommended: [
      "History of Present Illness Section"
    ],
    additional: [
      "Immunizations Section",
      "Instructions Section",
      "Problem Section",
      "Procedures Section",
      "Advance Directives Section"
    ]
  },
  "Operative Note": {
    required: [
      ["Chief Complaint and Reason for Visit Section", ["Chief Complaint Section", "Reason for Visit Section"]],
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "Allergies And Intolerances Section",
      "Family History Section",
      "General Status Section",
      "Past Medical History",
      "Medications Section",
      "Physical Exam Section",
      "Results Section",
      "Review of Systems Section",
      "Social History Section",
      "Vital Signs Section"
    ],
    recommended: [
      "History of Present Illness Section"
    ],
    additional: [
      "Immunizations Section",
      "Instructions Section",
      "Problem Section",
      "Procedures Section",
      "Advance Directives Section"
    ]
  },
  "Procedure Note": {
    required: [
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "Complications Section",
      "Procedure Description Section",
      "Procedure Indications Section",
      "Postprocedure Diagnosis Section"
    ],
    recommended: [],
    additional: [
      "Allergies And Intolerances Section",
      "Anesthesia Section",
      "Chief Complaint Section",
      "Chief Complaint and Reason for Visit Section",
      "Family History Section",
      "Past Medical History",
      "History of Present Illness Section",
      "Medical General History Section",
      "Medications Section",
      "Medications Administered Section",
      "Physical Exam Section",
      "Planned Procedure Section",
      "Procedure Disposition Section",
      "Procedure Estimated Blood Loss Section",
      "Procedure Findings Section",
      "Procedure Implants Section",
      "Procedure Specimens Taken Section",
      "Procedures Section",
      "Reason for Visit Section",
      "Review of Systems Section",
      "Social History Section",
      "Advance Directives Section"
    ]
  },
  "Progress Note": {
    required: [
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]]
    ],
    recommended: [],
    additional: [
      "Allergies And Intolerances Section",
      "Chief Complaint Section",
      "Activities Section",
      "Instructions Section",
      "Medications Section",
      "Objective Section",
      "Physical Exam Section",
      "Problem Section",
      "Results Section",
      "Review of Systems Section",
      "Subjective Section",
      "Vital Signs Section",
      "Nutrition Section",
      "Mental Status Section",
      "Advance Directives Section"
    ]
  },
  "Referral Note": {
    required: [
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "Problem Section",
      "Allergies And Intolerances Section",
      "Medications Section",
      "Reason for Referral Section"
    ],
    recommended: [
      "Plan of Treatment Section",
      "Results Section",
      "Functional Status Section",
      "Mental Status Section",
      "Nutrition Section"
    ],
    additional: [
      "Advance Directives Section",
      "History of Present Illness Section",
      "Family History Section",
      "Immunizations Section",
      "Procedures Section",
      "Review of Systems Section",
      "Social History Section",
      "Vital Signs Section",
      "Physical Exam Section",
      "Medical Equipment Section",
      "Past Medical History",
      "General Status Section",
      "Payers Section"
    ]
  },
  "Transfer Summary": {
    required: [
      ["Assessment and Plan Section", ["Assessment Section", "Plan of Treatment Section"]],
      "Allergies And Intolerances Section",
      "Medications Section",
      "Problem Section",
      "Results Section",
      "Vital Signs Section",
      "Reason for Referral Section"
    ],
    recommended: [
      "Advance Directives Section",
      "Functional Status Section",
      "Discharge Diagnosis Section",
      "Procedures Section",
      "Social History Section",
      "Mental Status Section",
      "Nutrition Section",
      "History of Present Illness Section"
    ],
    additional: [
      "Physical Exam Section",
      "Encounters Section",
      "Family History Section",
      "Immunizations Section",
      "Medical Equipment Section",
      "Payers Section",
      "General Status Section",
      "Review of Systems Section",
      "Past Medical History",
      "Admission Medications Section Entries Optional",
      "Admission Diagnosis Section",
      "Course of Care Section"
    ]
  }
};
