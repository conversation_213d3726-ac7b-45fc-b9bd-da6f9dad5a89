import type { PatientResponse } from '../api/api-reference'

export interface SelectListItem {
  text?: string | null
  value?: string | null
}

export interface Measurement {
  type: string
  textLong: string
  textShort: string
  unit: string
  valueType: string
  selected: boolean
}

export interface EncounterBox {
  type: string
  isOpen: boolean
  height: number
  width: number
  x: number | undefined
  y: number | undefined
  z: number
  isExpanded: boolean
  oldX: number | undefined
  oldY: number | undefined
  key: string
}

export interface ExtendedPatientResponse extends PatientResponse {
  fullName: string
}
