export function pascalToSpaced(str: string) {
  return str.replace(/([A-Z])/g, ' $1').trim()
}

export function generateGuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function getFileNameFromUrl(url: string | null): string {
  if (!url) return ''

  const parts = url.split('/')
  return parts[parts.length - 1]
}

export function formatPhoneNumber(phoneNumber: string | null | undefined): string {
  if (!phoneNumber) return ''

  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '')

  // Return original if less than 6 digits
  if (digits.length < 6) return phoneNumber

  // Format based on length
  if (digits.length <= 7) {
    // Format as xxx-xxxx
    return `${digits.slice(0, 3)}-${digits.slice(3)}`
  } else {
    // Format as xxx-xxx-xxxx+ (10+ digits)
    return `${digits.slice(0, 3)}-${digits.slice(3, 6)}-${digits.slice(6)}`
  }
}
