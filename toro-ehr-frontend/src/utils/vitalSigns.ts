import type { Measurement } from './interfaces'

export const getDefaultMeasurements = (): Measurement[] => [
  {
    type: 'BodyWeight',
    textShort: 'Weight',
    textLong: 'Body weight',
    unit: 'kg',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'BloodPressure',
    textShort: 'BP',
    textLong: 'Blood pressure',
    unit: 'mmHg',
    valueType: 'bloodPressure',
    selected: true,
  },
  {
    type: 'BodyTemperature',
    textShort: 'Temp',
    textLong: 'Body temperature',
    unit: 'C',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'InhaledOxygenConcentration',
    textShort: 'IOC',
    textLong: 'Inhaled oxygen concentration',
    unit: '%',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'InhaledOxygenFlowRate',
    textShort: 'IOFR',
    textLong: 'Inhaled oxygen flow rate',
    unit: 'L/min',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'HeartRate',
    textShort: 'HR',
    textLong: 'Heart rate',
    unit: 'bpm',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'RespiratoryRate',
    textShort: 'RR',
    textLong: 'Respiratory rate',
    unit: 'bpm',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'OxygenSaturation',
    textShort: 'OS',
    textLong: 'Oxygen saturation in Arterial blood',
    unit: '%',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'BodyHeight',
    textShort: 'Height',
    textLong: 'Body height',
    unit: 'cm',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'HeadCircumference',
    textShort: 'HC',
    textLong: 'Head Occipital-frontal circumference',
    unit: 'cm',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'BmiRatio',
    textShort: 'BMI',
    textLong: 'Body mass index (BMI) [Ratio]',
    unit: 'kg/m²',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'PainScale',
    textShort: 'Pain',
    textLong: 'Pain scale',
    unit: '',
    valueType: 'number',
    selected: true,
  },
]
