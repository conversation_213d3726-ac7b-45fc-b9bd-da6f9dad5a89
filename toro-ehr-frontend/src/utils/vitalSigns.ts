import type { Measurement } from './interfaces'

export const getDefaultMeasurements = (): Measurement[] => [
  {
    type: 'HeadOccipital',
    textShort: 'HO',
    textLong: 'Pediatric Head Occipital Frontal Circumference Percentile',
    unit: 'cm',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'PediatricBmi',
    textShort: 'BMI',
    textLong: 'Pediatric BMI for Age Observation',
    unit: 'kg/m²',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'PediatricWeightHeight',
    textShort: 'WH',
    textLong: 'Pediatric Weight for Height Observation',
    unit: '%',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'BloodPressure',
    textShort: 'BP',
    textLong: 'Blood Pressure',
    unit: 'mmHg',
    valueType: 'bloodPressure',
    selected: true,
  },
  {
    type: 'BmiRatio',
    textShort: 'BMI',
    textLong: 'BMI',
    unit: 'kg/m²',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'BodyHeight',
    textShort: 'Height',
    textLong: ' Body Height',
    unit: 'cm',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'BodyTemperature',
    textShort: 'Temp',
    textLong: 'Body Temperature',
    unit: 'C',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'BodyWeight',
    textShort: 'Weight',
    textLong: 'Body Weight',
    unit: 'kg',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'HeadCircumference',
    textShort: 'HC',
    textLong: 'Head Circumference',
    unit: 'cm',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'HeartRate',
    textShort: 'HR',
    textLong: 'Heart Rate',
    unit: 'bpm',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'PulseOximetry',
    textShort: 'Pulse',
    textLong: 'Pulse Oximetry',
    unit: '%',
    valueType: 'number',
    selected: false,
  },
  {
    type: 'RespiratoryRate',
    textShort: 'RR',
    textLong: 'Respiratory Rate',
    unit: 'bpm',
    valueType: 'number',
    selected: true,
  },
  {
    type: 'PainScale',
    textShort: 'Pain',
    textLong: 'Pain scale',
    unit: '',
    valueType: 'number',
    selected: true,
  },
]
