import * as yup from 'yup'
import { parsePhoneNumberFromString, type CountryCode } from 'libphonenumber-js'

const makeEmailSchema = (required: boolean) =>
  yup
    .string()
    .transform((v) => (v === '' ? null : v)) // treat empty string as null
    .nullable()
    [required ? 'required' : 'notRequired']('Email is required')
    .test('is-email', 'Invalid email format', (value) => {
      if (value == null) return true // allow empty when not required
      return yup.string().trim().lowercase().email().isValidSync(value)
    })
    // optional normalization: trim + lowercase
    .transform((v) => (typeof v === 'string' ? v.trim().toLowerCase() : v))
    // optional: cap total length to common limits
    .test('len', 'Email is too long', (v) => (v ? v.length <= 254 : true))

export async function validateEmail(
  email: string | null | undefined,
  options?: { required?: boolean },
): Promise<{ valid: boolean; value: string | null; error?: string }> {
  const schema = makeEmailSchema(Boolean(options?.required))
  try {
    const value = await schema.validate(email)
    return { valid: true, value: value ?? null }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    return { valid: false, value: null, error: e.message as string }
  }
}

export async function validatePhone(
  input: string | null | undefined,
  opts?: { required?: boolean; region?: CountryCode },
): Promise<{ valid: boolean; value: string | null; error?: string }> {
  const region = opts?.region || 'US'
  const required = Boolean(opts?.required)

  const raw = (input ?? '').trim()

  // Handle required check
  if (!raw) {
    if (required) {
      return { valid: false, value: null, error: 'Phone number is required' }
    }
    return { valid: true, value: null }
  }

  // Try parsing with libphonenumber
  const phone = parsePhoneNumberFromString(raw, region)

  if (!phone || !phone.isValid()) {
    return { valid: false, value: null, error: 'Invalid phone number' }
  }

  // Return normalized number in E.164 format (+381641234567)
  return { valid: true, value: phone.number }
}
