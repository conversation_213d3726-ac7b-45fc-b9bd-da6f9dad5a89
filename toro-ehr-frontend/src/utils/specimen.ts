import type { SelectListItem } from './interfaces'

export const getSpecimenTypes = (): SelectListItem[] => [
  { text: 'Whole blood', value: 'BLD' },
  { text: 'Serum', value: 'SER' },
  { text: 'Plasma', value: 'PLAS' },
  { text: 'Urine', value: 'UR' },
  { text: 'Cerebrospinal fluid', value: 'CSF' },
  { text: 'Sweat', value: 'SWT' },
  { text: 'Sputum', value: 'SPT' },
  { text: 'Tissue', value: 'TISS' },
  { text: 'Stool', value: 'STL' },
  { text: 'Amniotic fluid', value: 'AMN' },
  { text: 'Aspirate', value: 'ASP' },
  { text: 'Blood bag', value: 'BBL' },
  { text: 'Arterial blood', value: 'BLDA' },
  { text: 'Capillary blood', value: 'BLDC' },
  { text: 'Cord blood', value: 'BLDCO' },
  { text: 'Bile fluid', value: 'BIFL' },
  { text: 'Whole body', value: 'BDY' },
]
