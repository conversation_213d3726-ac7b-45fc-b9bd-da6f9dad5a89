import { generateGuid } from './stringUtils'

export const getDefaultEncounterBoxes = (
  isTablet: boolean,
  isMobile: boolean,
  parentContainerHeight: number,
  parentContainerWidth: number,
) => {
  return [
    {
      type: 'active-patients',
      isOpen: !isTablet && !isMobile,
      height: parentContainerHeight,
      width: 250,
      x: 0,
      y: 0,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'summary',
      isOpen: !isTablet && !isMobile,
      height: parentContainerHeight,
      width: 300,
      x: parentContainerWidth - 300,
      y: 0,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'vitals',
      isOpen: false,
      height: isMobile ? parentContainerHeight : 400,
      width: isMobile ? parentContainerWidth : 700,
      x: isMobile ? 0 : undefined,
      y: isMobile ? 0 : undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'scratch',
      isOpen: false,
      height: isMobile ? parentContainerHeight : 400,
      width: isMobile ? parentContainerWidth : 700,
      x: isMobile ? 0 : undefined,
      y: isMobile ? 0 : undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'care',
      isOpen: false,
      height: isMobile ? parentContainerHeight : 400,
      width: isMobile ? parentContainerWidth : 700,
      x: isMobile ? 0 : undefined,
      y: isMobile ? 0 : undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'questionnaires',
      isOpen: false,
      height: isMobile ? parentContainerHeight : 400,
      width: isMobile ? parentContainerWidth : 700,
      x: isMobile ? 0 : undefined,
      y: isMobile ? 0 : undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'labs',
      isOpen: false,
      height: 400,
      width: 700,
      x: undefined,
      y: undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'imaging',
      isOpen: false,
      height: 400,
      width: 700,
      x: undefined,
      y: undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'comms',
      isOpen: false,
      height: 400,
      width: 700,
      x: undefined,
      y: undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'orders',
      isOpen: false,
      height: 400,
      width: 700,
      x: undefined,
      y: undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'notes',
      isOpen: false,
      height: 400,
      width: 700,
      x: undefined,
      y: undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
    {
      type: 'info',
      isOpen: false,
      height: isMobile ? parentContainerHeight : 400,
      width: isMobile ? parentContainerWidth : 700,
      x: isMobile ? 0 : undefined,
      y: isMobile ? 0 : undefined,
      z: 1,
      isExpanded: false,
      oldX: undefined,
      oldY: undefined,
      key: generateGuid(),
    },
  ]
}
