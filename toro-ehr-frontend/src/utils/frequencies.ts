import type { SelectListItem } from './interfaces'

export const getFrequencies = (): SelectListItem[] => [
  { text: 'Once daily (q24h)', value: 'OnceDaily' },
  { text: 'Twice daily (q12h)', value: 'TwiceDaily' },
  { text: 'Three times daily (q8h)', value: 'ThreeTimesDaily' },
  { text: 'Every 6 hours (q6h)', value: 'Every6Hours' },
  { text: 'Every 4 hours (q4h)', value: 'Every4Hours' },
  { text: 'Every 8 hours PRN (as needed)', value: 'Every8HoursPrn' },
  { text: 'Once stat (single dose now)', value: 'OnceStat' },
  { text: 'Custom', value: 'Custom' },
]
