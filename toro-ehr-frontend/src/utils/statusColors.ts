/**
 * Centralized status color utility for consistent styling across the application
 */

export interface StatusColorConfig {
  background: string
  text: string
  dot: string
}

export type StatusType = 'appointment' | 'payment' | 'encounter' | 'generic'

/**
 * Status color mappings for different status types
 */
const STATUS_COLORS: Record<StatusType, Record<string, StatusColorConfig>> = {
  // Appointment statuses (Pending, Confirmed, Checked In, Completed, Missed, Canceled, CanceledLate)
  appointment: {
    pending: {
      background: 'bg-yellow-100',
      text: 'text-yellow-800',
      dot: 'bg-yellow-400'
    },
    confirmed: {
      background: 'bg-green-100',
      text: 'text-green-800',
      dot: 'bg-green-400'
    },
    'checked in': {
      background: 'bg-blue-100',
      text: 'text-blue-800',
      dot: 'bg-blue-400'
    },
    completed: {
      background: 'bg-emerald-100',
      text: 'text-emerald-800',
      dot: 'bg-emerald-500'
    },
    missed: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    },
    canceled: {
      background: 'bg-gray-100',
      text: 'text-gray-800',
      dot: 'bg-gray-400'
    },
    canceledlate: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    }
  },

  // Payment transaction statuses (Approved, Pending, Declined, Voided)
  payment: {
    approved: {
      background: 'bg-green-100',
      text: 'text-green-800',
      dot: 'bg-green-400'
    },
    pending: {
      background: 'bg-yellow-100',
      text: 'text-yellow-800',
      dot: 'bg-yellow-400'
    },
    declined: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    },
    voided: {
      background: 'bg-purple-100',
      text: 'text-purple-800',
      dot: 'bg-purple-400'
    }
  },

  // Encounter statuses - using enum names for consistency
  encounter: {
    // Primary mappings using enum names (preferred approach)
    planned: {
      background: 'bg-blue-100',
      text: 'text-blue-800',
      dot: 'bg-blue-400'
    },
    checkedin: {
      background: 'bg-indigo-100',
      text: 'text-indigo-800',
      dot: 'bg-indigo-400'
    },
    arrived: {
      background: 'bg-purple-100',
      text: 'text-purple-800',
      dot: 'bg-purple-400'
    },
    inprogress: {
      background: 'bg-yellow-100',
      text: 'text-yellow-800',
      dot: 'bg-yellow-400'
    },
    completed: {
      background: 'bg-green-100',
      text: 'text-green-800',
      dot: 'bg-green-400'
    },
    missed: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    },
    canceled: {
      background: 'bg-gray-100',
      text: 'text-gray-800',
      dot: 'bg-gray-400'
    },
    canceledlate: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    }
  },

  // Generic fallback colors for unknown statuses
  generic: {
    success: {
      background: 'bg-green-100',
      text: 'text-green-800',
      dot: 'bg-green-400'
    },
    warning: {
      background: 'bg-yellow-100',
      text: 'text-yellow-800',
      dot: 'bg-yellow-400'
    },
    error: {
      background: 'bg-red-100',
      text: 'text-red-800',
      dot: 'bg-red-400'
    },
    info: {
      background: 'bg-blue-100',
      text: 'text-blue-800',
      dot: 'bg-blue-400'
    },
    neutral: {
      background: 'bg-gray-100',
      text: 'text-gray-800',
      dot: 'bg-gray-400'
    }
  }
}

/**
 * Default fallback color configuration
 */
const DEFAULT_COLOR: StatusColorConfig = {
  background: 'bg-gray-100',
  text: 'text-gray-800',
  dot: 'bg-gray-400'
}

/**
 * Convert enum values to enum keys for consistent mapping
 * e.g., 'Checked In' -> 'checkedin', 'In Progress' -> 'inprogress'
 */
function normalizeStatusToKey(status: string): string {
  return status.toLowerCase().replace(/\s+/g, '').trim()
}

/**
 * Convert enum values to enum names for consistent status handling
 * This maps the API response values to enum keys for better consistency
 */
export function getEnumNameFromValue(enumValue: string | undefined): string | undefined {
  if (!enumValue) return undefined

  // Map enum values to enum names
  const valueToNameMap: Record<string, string> = {
    'Planned': 'Planned',
    'Checked In': 'CheckedIn',
    'Arrived': 'Arrived',
    'In Progress': 'InProgress',
    'Completed': 'Completed',
    'Missed': 'Missed',
    'Canceled': 'Canceled',
    'Canceled Late': 'CanceledLate'
  }

  return valueToNameMap[enumValue] || enumValue
}

/**
 * Get enum key (name) from enum value for sending to backend
 * This function finds the key that corresponds to the given enum value
 */
export function getEnumKey<T extends Record<string, string>>(enumObject: T, enumValue: T[keyof T]): keyof T | undefined {
  return Object.keys(enumObject).find(key => enumObject[key] === enumValue) as keyof T | undefined
}

/**
 * Get status color configuration for a specific status and type
 */
export function getStatusColor(status: string | undefined, type: StatusType = 'generic'): StatusColorConfig {
  if (!status) return DEFAULT_COLOR

  // For encounter statuses, convert enum values to enum names for consistent mapping
  let processedStatus = status
  if (type === 'encounter') {
    const enumName = getEnumNameFromValue(status)
    if (enumName) {
      processedStatus = enumName
    }
  }

  const normalizedStatus = processedStatus.toLowerCase().trim()
  const statusMap = STATUS_COLORS[type]

  // Try exact match first
  if (statusMap[normalizedStatus]) {
    return statusMap[normalizedStatus]
  }

  // Try normalized key match (remove spaces)
  const keyMatch = normalizeStatusToKey(processedStatus)
  return statusMap[keyMatch] || DEFAULT_COLOR
}

/**
 * Get background and text classes for status badges
 */
export function getStatusClasses(status: string | undefined, type: StatusType = 'generic'): string {
  const config = getStatusColor(status, type)
  return `${config.background} ${config.text}`
}

/**
 * Get dot/indicator color class for status badges
 */
export function getStatusDotClass(status: string | undefined, type: StatusType = 'generic'): string {
  const config = getStatusColor(status, type)
  return config.dot
}

/**
 * Legacy compatibility functions for existing code
 */

// For appointment statuses
export function getAppointmentStatusClasses(status: string | undefined): string {
  return getStatusClasses(status, 'appointment')
}

export function getAppointmentStatusDotClass(status: string | undefined): string {
  return getStatusDotClass(status, 'appointment')
}

// For payment statuses
export function getPaymentStatusClasses(status: string | undefined): string {
  return getStatusClasses(status, 'payment')
}

export function getPaymentStatusDotClass(status: string | undefined): string {
  return getStatusDotClass(status, 'payment')
}

// For encounter statuses
export function getEncounterStatusClasses(status: string | undefined): string {
  return getStatusClasses(status, 'encounter')
}

export function getEncounterStatusDotClass(status: string | undefined): string {
  return getStatusDotClass(status, 'encounter')
}

// Legacy compatibility - keep business functions for backward compatibility
export function getBusinessStatusClasses(status: string | undefined): string {
  return getStatusClasses(status, 'encounter')
}

export function getBusinessStatusDotClass(status: string | undefined): string {
  return getStatusDotClass(status, 'encounter')
}
