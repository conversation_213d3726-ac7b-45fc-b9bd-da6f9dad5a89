import type { NotificationResponse, NotificationType } from '@/api/api-reference'
import { useRouter } from 'vue-router'
import { api } from '@/api'
import { useToast } from 'vue-toastification'

export interface NotificationAction {
  label: string
  icon: string
  variant: 'primary' | 'secondary' | 'success' | 'danger' | 'warning'
  action: () => Promise<void> | void
}

export function getNotificationActions(notification: NotificationResponse): NotificationAction[] {
  const router = useRouter()
  const toast = useToast()

  const actions: NotificationAction[] = []

  // if action is already completed, don't show any action buttons
  if (notification.actionCompleted) {
    return actions
  }

  switch (notification.notificationType) {
    case 'Appointment Confirmed':
      // for patients - view appointment details
      actions.push({
        label: 'View',
        icon: 'pi pi-eye',
        variant: 'primary',
        action: () => {
          if (notification.relatedEntityId) {
            router.push(`/patient-appointments`)
          }
        }
      })
      break

    case 'Appointment Requested':
      // for employees - confirm or cancel appointment
      actions.push({
        label: 'Confirm',
        icon: 'pi pi-check',
        variant: 'success',
        action: async () => {
          try {
            if (notification.relatedEntityId) {
              // call confirm appointment API
              await api.appointments.appointmentConfirm({
                id: notification.relatedEntityId,
                timestamp: new Date().toISOString()
              })
              toast.success('Appointment confirmed successfully')

              // note: the notification should be updated/removed by the backend
              // the frontend will refresh to get the updated state
            }
          } catch (error) {
            toast.error('Failed to confirm appointment')
            throw error // re-throw to handle in component
          }
        }
      })

      actions.push({
        label: 'Cancel',
        icon: 'pi pi-times',
        variant: 'danger',
        action: async () => {
          try {
            if (notification.relatedEntityId) {
              // call cancel appointment API
              await api.appointments.appointmentCancelAppointment({
                id: notification.relatedEntityId,
                timestamp: new Date().toISOString()
              })
              toast.success('Appointment cancelled successfully')

              // note: the notification should be updated/removed by the backend
              // the frontend will refresh to get the updated state
            }
          } catch (error) {
            toast.error('Failed to cancel appointment')
            throw error // re-throw to handle in component
          }
        }
      })

      actions.push({
        label: 'View Details',
        icon: 'pi pi-info-circle',
        variant: 'secondary',
        action: () => {
          router.push('/appointments')
        }
      })
      break

    case 'Appointment Rescheduled':
      // for employees - view updated appointment
      actions.push({
        label: 'View',
        icon: 'pi pi-calendar',
        variant: 'primary',
        action: () => {
          router.push('/appointments')
        }
      })
      break

    case 'Appointment Missed':
      // for employees - view missed appointment only
      actions.push({
        label: 'View',
        icon: 'pi pi-exclamation-triangle',
        variant: 'warning',
        action: () => {
          router.push('/appointments')
        }
      })
      break

    case 'Appointment Canceled':
      // get user role to determine appropriate actions
      const userRole = localStorage.getItem('user') ?
        JSON.parse(localStorage.getItem('user')!).selectedUserRole : null

      if (userRole === 'Patient') {
        // for patients - view their appointments
        actions.push({
          label: 'View Appointments',
          icon: 'pi pi-calendar',
          variant: 'primary',
          action: () => {
            router.push('/patient-appointments')
          }
        })
      } else {
        // for employees - view canceled appointment details
        actions.push({
          label: 'View',
          icon: 'pi pi-times-circle',
          variant: 'secondary',
          action: () => {
            router.push('/appointments')
          }
        })
      }
      break

    case 'Complete Profile':
      // for patients - go to profile completion
      actions.push({
        label: 'Complete Profile',
        icon: 'pi pi-user-edit',
        variant: 'primary',
        action: () => {
          router.push('/patient-profile/personal-info')
        }
      })
      break

    case 'Complete Questionnaires':
      // for patients - go to questionnaires
      actions.push({
        label: 'Complete Questionnaires',
        icon: 'pi pi-file-edit',
        variant: 'primary',
        action: () => {
          router.push('/patient/questionnaires')
        }
      })
      break

    case 'Questionnaire Updated':
      // for both patients and employees - view the updated questionnaire
      actions.push({
        label: 'View',
        icon: 'pi pi-eye',
        variant: 'primary',
        action: () => {
          const userRole = localStorage.getItem('user') ?
            JSON.parse(localStorage.getItem('user')!).selectedUserRole : null

          if (userRole === 'Patient') {
            // patients go to the specific questionnaire
            if (notification.relatedEntityId) {
              router.push({
                name: 'patient-questionnaires',
                params: { section: `questionnaire-${notification.relatedEntityId}` }
              })
            } else {
              // fallback to questionnaire list if no specific ID
              router.push('/patient/questionnaires')
            }
          } else {
            // employees go to questionnaire management
            if (notification.relatedEntityId) {
              // if we have a specific questionnaire ID, go to edit page
              router.push({ name: 'edit-questionnaire', params: { id: notification.relatedEntityId } })
            } else {
              // otherwise go to questionnaire list
              router.push('/questionnaires')
            }
          }
        }
      })
      break

    case 'Message Received':
      // for both patients and employees - view the message
      actions.push({
        label: 'View Message',
        icon: 'pi pi-envelope',
        variant: 'primary',
        action: () => {
          const userRole = localStorage.getItem('user') ?
            JSON.parse(localStorage.getItem('user')!).selectedUserRole : null

          if (userRole === 'Patient') {
            // patients go to communication page
            if (notification.relatedEntityId) {
              router.push(`/communication/${notification.relatedEntityId}`)
            } else {
              // fallback to patient appointments if no encounter ID
              router.push('/patient-appointments')
            }
          } else {
            // practitioners go to encounters page
            if (notification.relatedEntityId) {
              router.push(`/encounters/${notification.relatedEntityId}`)
            } else {
              // fallback to encounters list
              router.push('/encounters')
            }
          }
        }
      })
      break

    default:
      // default action - just view
      actions.push({
        label: 'View',
        icon: 'pi pi-eye',
        variant: 'primary',
        action: () => {
          // navigate to appropriate page based on user role
          const userRole = localStorage.getItem('user') ?
            JSON.parse(localStorage.getItem('user')!).selectedUserRole : null

          if (userRole === 'Patient') {
            router.push('/patient-appointments')
          } else {
            router.push('/appointments')
          }
        }
      })
      break
  }

  return actions
}

export function getNotificationTypeColor(type: NotificationType): string {
  switch (type) {
    case 'Appointment Confirmed':
      return 'text-green-600 bg-green-100'
    case 'Appointment Requested':
      return 'text-blue-600 bg-blue-100'
    case 'Appointment Rescheduled':
      return 'text-yellow-600 bg-yellow-100'
    case 'Appointment Missed':
      return 'text-red-600 bg-red-100'
    case 'Appointment Canceled':
      return 'text-orange-600 bg-orange-100'
    case 'Complete Profile':
      return 'text-purple-600 bg-purple-100'
    case 'Complete Questionnaires':
      return 'text-indigo-600 bg-indigo-100'
    case 'Questionnaire Updated':
      return 'text-teal-600 bg-teal-100'
    case 'Message Received':
      return 'text-cyan-600 bg-cyan-100'
    default:
      return 'text-gray-600 bg-gray-100'
  }
}

export function getNotificationTypeIcon(type: NotificationType): string {
  switch (type) {
    case 'Appointment Confirmed':
      return 'pi pi-check-circle'
    case 'Appointment Requested':
      return 'pi pi-clock'
    case 'Appointment Rescheduled':
      return 'pi pi-calendar'
    case 'Appointment Missed':
      return 'pi pi-exclamation-triangle'
    case 'Appointment Canceled':
      return 'pi pi-times-circle'
    case 'Complete Profile':
      return 'pi pi-user-edit'
    case 'Complete Questionnaires':
      return 'pi pi-file-edit'
    case 'Questionnaire Updated':
      return 'pi pi-refresh'
    case 'Message Received':
      return 'pi pi-envelope'
    default:
      return 'pi pi-info-circle'
  }
}
