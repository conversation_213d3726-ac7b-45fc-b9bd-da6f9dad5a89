<template>
  <div class="flex flex-col h-screen overflow-hidden">
    <!-- Fixed Header -->
    <AppNavigation v-if="isNavbarVisible()" class="flex-shrink-0" />

    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
      <RouterView />
    </div>

    <!-- Fixed Footer -->
    <AppFooter class="flex-shrink-0" />
  </div>
  <GlobalLoadingOverlay />
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import AppFooter from './components/layout/AppFooter.vue'
import AppNavigation from './components/layout/AppNavigation.vue'
import GlobalLoadingOverlay from './components/GlobalLoadingOverlay.vue'
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api } from './api'

// Get the current route
const route = useRoute()

//todo: group all routes where header should be hidden
// Check if the current page is the login page or set password
const isNavbarVisible = () => route.name !== 'login' && route.name !== 'set-password-employee'
/*const isNavbarVisible = computed(
  () => route.name !== 'login' && route.name !== 'set-password-employee',
)*/

const authStore = useAuthStore()

onMounted(() => {
  const storedToken = localStorage.getItem('token')
  if (storedToken) {
    api.setSecurityData(storedToken)
  }

  const storedUser = JSON.parse(localStorage.getItem('user') || 'null')
  if (storedUser) {
    authStore.setUser(storedUser)
  }
})
</script>
