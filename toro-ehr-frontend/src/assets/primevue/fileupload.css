@import './button';
@import './message';
@import './progressbar';

.p-fileupload input[type="file"] {
    @apply hidden
}

.p-fileupload-advanced {
    @apply border border-surface-200 dark:border-surface-700 rounded-md 
        bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0
}

.p-fileupload-header {
    @apply flex items-center p-[1.125rem] gap-2 text-surface-700 dark:text-surface-0
}

.p-fileupload-content {
    @apply border border-transparent relative transition-colors duration-200 pt-0 px-[1.125rem] pb-[1.125rem]
}

.p-fileupload-content .p-progressbar {
    @apply w-full absolute top-0 left-0 h-1
}

.p-fileupload-file-list {
    @apply flex flex-col gap-2 mt-[1.125rem]
}

.p-fileupload-file {
    @apply flex flex-wrap items-center p-4 border-b border-surface-200 dark:border-surface-700 gap-2 last:border-b-0
}

.p-fileupload-file-info {
    @apply flex flex-col gap-2
}

.p-fileupload-file-thumbnail {
    @apply flex-shrink-0
}

.p-fileupload-file-actions {
    @apply ml-auto
}

.p-fileupload-highlight {
    @apply border border-dashed border-primary
}

.p-fileupload-advanced .p-message {
    @apply mt-0
}

.p-fileupload-basic {
    @apply flex flex-wrap items-center justify-center gap-2
}