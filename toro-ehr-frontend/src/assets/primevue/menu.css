.p-menu {
    @apply bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0 
        border border-surface-200 dark:border-surface-700
        rounded-md min-w-52
}

.p-menu-list {
    @apply m-0 p-1 list-none outline-none flex flex-col gap-[2px]
}

.p-menu-item-content {
    @apply transition-colors duration-200 rounded-sm text-surface-700 dark:text-surface-0
}

.p-menu-item-link {
    @apply cursor-pointer flex items-center no-underline overflow-hidden relative text-inherit
        px-3 py-2 gap-2 select-none outline-none
}

.p-menu-item-icon {
    @apply text-surface-400 dark:text-surface-500
}

.p-menu-item.p-focus .p-menu-item-content {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0 
}

.p-menu-item.p-focus .p-menu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-menu-item:not(.p-disabled) .p-menu-item-content:hover .p-menu-item-icon {
    @apply text-surface-500 dark:text-surface-400
}

.p-menu-overlay {
    @apply shadow-[0_4px_6px_-1px_rgba(0,0,0,0.1),0_2px_4px_-2px_rgba(0,0,0,0.1)]
}

.p-menu-submenu-label {
    @apply bg-transparent px-3 py-2 text-surface-500 dark:text-surface-400 font-semibold
}

.p-menu-separator {
    @apply border-t border-surface-200 dark:border-surface-700
}