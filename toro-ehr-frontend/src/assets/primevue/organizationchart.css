.p-organizationchart-table {
    @apply border-spacing-0 border-separate my-0 mx-auto
}

.p-organizationchart-table > tbody > tr > td {
    @apply text-center align-top py-0 px-3
}

.p-organizationchart-node {
    @apply inline-block relative py-3 px-4 rounded-md
        border border-surface-200 dark:border-surface-700
        bg-surface-0 dark:bg-surface-900 
        text-surface-700 dark:text-surface-0
        transition-colors duration-200
}

.p-organizationchart-node:has(.p-organizationchart-node-toggle-button) {
    @apply pt-3 px-4 pb-5
}

.p-organizationchart-node.p-organizationchart-node-selectable:not(.p-organizationchart-node-selected):hover {
    @apply bg-surface-100 text-surface-800 dark:bg-surface-800 dark:text-surface-0
}

.p-organizationchart-node-selected {
    @apply bg-highlight
}

.p-organizationchart-node-toggle-button {
    @apply absolute -bottom-3 -ml-3 z-20 left-1/2 select-none cursor-pointer w-6 h-6 no-underline rounded-full
        inline-flex items-center justify-center transition-colors duration-200 
        border border-surface-200 dark:border-surface-700
        bg-surface-0 text-surface-500 hover:bg-surface-100 hover:text-surface-700 
        dark:bg-surface-900 dark:text-surface-400 dark:hover:bg-surface-800 dark:hover:text-surface-0 
        focus-visible:outline focus-visible:outline-1 focus-visible:outline-offset-2 focus-visible:outline-primary
}
.p-organizationchart-node-toggle-button-icon {
    @apply relative top-px
}

.p-organizationchart-connector-down {
    @apply my-0 mx-auto h-6 w-px bg-surface-200 dark:bg-surface-700
}

.p-organizationchart-connector-right {
    @apply rounded-none
}

.p-organizationchart-connector-left {
    @apply rounded-none border-r border-surface-200 dark:border-surface-700
}

.p-organizationchart-connector-top {
    @apply border-t border-surface-200 dark:border-surface-700
}

.p-organizationchart-node-selectable {
    @apply cursor-pointer
}

.p-organizationchart-connectors :nth-child(1 of .p-organizationchart-connector-left) {
    @apply border-r-0
}

.p-organizationchart-connectors :nth-last-child(1 of .p-organizationchart-connector-left) {
    @apply rounded-tr-md
}

.p-organizationchart-connectors :nth-child(1 of .p-organizationchart-connector-right) {
    @apply border-l border-surface-200 dark:border-surface-700 rounded-tl-md
}