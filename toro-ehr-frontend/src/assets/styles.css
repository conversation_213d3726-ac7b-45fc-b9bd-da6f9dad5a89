/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* @import 'tailwindcss/base';
@import 'tailwindcss/components';
@import './primevue/tailwind.css';
@import 'tailwindcss/utilities'; */

/* Primary and Surface Palettes */
:root {
  --p-primary-50: #edf2fb;
  --p-primary-100: #dbe4f7;
  --p-primary-200: #b7c9ef;
  --p-primary-300: #93afe7;
  --p-primary-400: #6f94df;
  --p-primary-500: #245494;
  --p-primary-600: #1d4377;
  --p-primary-700: #17325a;
  --p-primary-800: #11213c;
  --p-primary-900: #0a111f;
  --p-primary-950: #050910;
  --p-surface-0: #ffffff;
  --p-surface-50: #fafafa;
  --p-surface-100: #f4f4f5;
  --p-surface-200: #e4e4e7;
  --p-surface-300: #d4d4d8;
  --p-surface-400: #a1a1aa;
  --p-surface-500: #71717a;
  --p-surface-600: #52525b;
  --p-surface-700: #3f3f46;
  --p-surface-800: #27272a;
  --p-surface-900: #18181b;
  --p-surface-950: #09090b;
  --p-content-border-radius: 6px;
}

/* Light */
:root {
  --p-primary-color: var(--p-primary-500);
  --p-primary-contrast-color: var(--p-surface-0);
  --p-primary-hover-color: var(--p-primary-600);
  --p-primary-active-color: var(--p-primary-700);
  --p-content-border-color: var(--p-surface-200);
  --p-content-hover-background: var(--p-surface-100);
  --p-content-hover-color: var(--p-surface-800);
  --p-highlight-background: var(--p-primary-50);
  --p-highlight-color: var(--p-primary-700);
  --p-highlight-focus-background: var(--p-primary-100);
  --p-highlight-focus-color: var(--p-primary-800);
  --p-text-color: var(--p-surface-700);
  --p-text-hover-color: var(--p-surface-800);
  --p-text-muted-color: var(--p-surface-500);
  --p-text-hover-muted-color: var(--p-surface-600);
}

/* 
 * Dark Mode
 * Defaults to system, change the selector to match the darkMode in tailwind.config.
 * For example; 
 * darkMode: ['selector', '[class*="app-dark"]'] 
 * should be;
 * :root[class="app-dark"] {
*/
/*@media (prefers-color-scheme: dark) {
  :root {
    --p-primary-color: var(--p-primary-400);
    --p-primary-contrast-color: var(--p-surface-900);
    --p-primary-hover-color: var(--p-primary-300);
    --p-primary-active-color: var(--p-primary-200);
    --p-content-border-color: var(--p-surface-700);
    --p-content-hover-background: var(--p-surface-800);
    --p-content-hover-color: var(--p-surface-0);
    --p-highlight-background: color-mix(in srgb, var(--p-primary-400), transparent 84%);
    --p-highlight-color: rgba(255, 255, 255, 0.87);
    --p-highlight-focus-background: color-mix(in srgb, var(--p-primary-400), transparent 76%);
    --p-highlight-focus-color: rgba(255, 255, 255, 0.87);
    --p-text-color: var(--p-surface-0);
    --p-text-hover-color: var(--p-surface-0);
    --p-text-muted-color: var(--p-surface-400);
    --p-text-hover-muted-color: var(--p-surface-300);
  }
}*/

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: #dc2626; /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
