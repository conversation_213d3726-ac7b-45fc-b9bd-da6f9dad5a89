import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export type AppRouteNames = 'organizations' | 'dashboard'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const withMeta = (meta: any, routes: any[]) => {
  return routes.map((route) => ({
    ...route,
    meta: {
      ...meta,
      ...route.meta,
    },
  }))
}

const patientRoutes = withMeta({ requiresAuth: true, userRoles: ['Patient'] }, [
  {
    path: '/patient-appointments',
    name: 'patient-appointments',
    component: () => import('../pages/Appointment/PatientAppointmentList.vue'),
  },
  {
    path: '/my-records',
    name: 'my-records',
    component: () => import('../pages/Patient/Records.vue'),
  },

  {
    path: '/my-records/:id',
    name: 'record-details',
    component: () => import('../pages/Patient/RecordDetails.vue'),
    props: true,
  },
  {
    path: '/book-appointment',
    name: 'book-appointment',
    component: () => import('../pages/Appointment/Patient/BookAppointment.vue'),
  },
  {
    path: '/communication/:id',
    name: 'communication',
    component: () => import('../pages/Patient/EncounterCommunications.vue'),
  },
  {
    path: '/patient-profile/:section?',
    name: 'patient-profile',
    props: true,
    component: () => import('../pages/Patient/PatientProfile.vue'),
  },
  {
    path: '/patient/questionnaires/:section?',
    name: 'patient-questionnaires',
    props: true,
    component: () => import('../pages/Questionnaire/PatientQuestionnaireList.vue'),
  },
  {
    path: '/patient/checkin/:encounterId/:section?',
    name: 'checkin-questionnaires-patient',
    props: true,
    component: () => import('../pages/Questionnaire/CheckInQuestionnaireList.vue'),
  },
])

const employeeRoutes = withMeta({ requiresAuth: true, userRoles: ['Employee'] }, [
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../pages/Dashboard.vue'),
  },
  {
    path: '/patients',
    name: 'patients',
    component: () => import('../pages/Patient/PatientList.vue'),
  },

  {
    path: '/appointments',
    name: 'appointments',
    component: () => import('../pages/Appointment/AppointmentList.vue'),
  },
  {
    path: '/note-templates',
    name: 'note-templates',
    component: () => import('../pages/NoteTemplate/NoteTemplateList.vue'),
  },
  {
    path: '/note-templates/add',
    name: 'add-note-template',
    component: () => import('../pages/NoteTemplate/EditNoteTemplate.vue'),
  },
  {
    path: '/note-templates/edit/:id',
    name: 'edit-note-template',
    component: () => import('../pages/NoteTemplate/EditNoteTemplate.vue'),
    props: true,
  },
  {
    path: '/encounters/:id?',
    name: 'encounters',
    component: () => import('../pages/Encounter/EncounterMain.vue'),
  },
  {
    path: '/patients/:id',
    name: 'patient',
    component: () => import('../pages/Encounter/EncounterMain.vue'),
  },
  {
    path: '/patient/checkin/:encounterId/:section?',
    name: 'checkin-questionnaires',
    props: true,
    component: () => import('../pages/Questionnaire/CheckInQuestionnaireList.vue'),
  },
  {
    path: '/encounters/payments/:encounterId',
    name: 'encounter-payments',
    component: () => import('../pages/Encounter/EncounterPayments.vue'),
  },
  {
    path: '/checkout/:encounterId',
    name: 'checkout',
    component: () => import('../pages/Checkout/Checkout.vue'),
  },
  {
    path: '/payments',
    name: 'payments',
    component: () => import('../pages/Payments/PaymentsList.vue'),
  },
  {
    path: '/order-bundle-templates',
    name: 'order-bundle-templates',
    component: () => import('../pages/OrderBundleTemplate/OrderBundleTemplateList.vue'),
  },
  {
    path: '/edit-order-template/:id?',
    name: 'edit-order-template',
    component: () => import('../pages/OrderBundleTemplate/EditOrderBundleTemplate.vue'),
  },
])

const employeePractitionerRoutes = withMeta(
  {
    requiresAuth: true,
    isEmployee: true,
    userRoles: ['Employee'],
    employeeRoles: ['Practitioner'],
  },
  [
    {
      path: '/practitioner-profile',
      name: 'practitioner-profile',
      component: () => import('../pages/Practitioner/PractitionerProfile/PractitionerProfile.vue'),
    },
  ],
)

const employeeOrganizationAdminRoutes = withMeta(
  { requiresAuth: true, userRoles: ['Employee'], employeeRoles: ['OrganizationAdmin'] },
  [
    {
      path: '/locations',
      name: 'locations',
      component: () => import('../pages/Location/LocationList.vue'),
    },
  ],
)

const employeeAdminRoutes = withMeta(
  {
    requiresAuth: true,
    userRoles: ['Employee'],
    employeeRoles: ['OrganizationAdmin', 'LocationAdmin'],
  },
  [
    {
      path: '/employees',
      name: 'employees',
      component: () => import('../pages/Practitioner/PractitionerList.vue'),
    },
    {
      path: '/questionnaires',
      name: 'questionnaires',
      component: () => import('../pages/Questionnaire/QuestionnaireList.vue'),
    },
    {
      path: '/questionnaire/add',
      name: 'add-questionnaire',
      component: () => import('../pages/Questionnaire/EditQuestionnaire.vue'),
      props: true,
    },
    {
      path: '/questionnaire/edit/:id?',
      name: 'edit-questionnaire',
      component: () => import('../pages/Questionnaire/EditQuestionnaire.vue'),
      props: true,
    },
    {
      path: '/questionnaire/copy/:id?',
      name: 'copy-questionnaire',
      component: () => import('../pages/Questionnaire/EditQuestionnaire.vue'),
      props: true,
    },
  ],
)

const superAdminRoutes = withMeta({ requiresAuth: true, userRoles: ['SuperAdmin'] }, [
  {
    path: '/organizations',
    name: 'organizations',
    component: () => import('../pages/Organization/OrganizationList.vue'),
  },
])

const nonPatientRoutes = withMeta({ requiresAuth: true, userRoles: ['SuperAdmin', 'Employee'] }, [
  {
    path: '/medications',
    name: 'medications',
    component: () => import('../pages/Medication/MedicationList.vue'),
  },
  {
    path: '/snomed',
    name: 'snomed',
    component: () => import('../pages/SnomedCode/SnomedCodeList.vue'),
  },
  {
    path: '/allergies',
    name: 'allergies',
    component: () => import('../pages/Allergie/AllergieList.vue'),
  },
  {
    path: '/immunizations',
    name: 'immunizations',
    component: () => import('../pages/Immunization/ImmunizationsList.vue'),
  },
  {
    path: '/icd10',
    name: 'icd10',
    component: () => import('../pages/Icd10/Icd10List.vue'),
  },
  {
    path: '/cptcodes',
    name: 'cptcodes',
    component: () => import('../pages/CptCode/CptCodeList.vue'),
  },
  {
    path: '/loinccodes',
    name: 'loinccodes',
    component: () => import('../pages/LoincCode/LoincCodeList.vue'),
  },
])

// shared routes for both Patient and Employee roles
const sharedAuthenticatedRoutes = withMeta(
  { requiresAuth: true, userRoles: ['Patient', 'Employee'] },
  [
    {
      path: '/notifications',
      name: 'notifications',
      component: () => import('../pages/Notifications.vue'),
    },
  ],
)

const publicRoutes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('../pages/Authentication/Login.vue'),
  },
  {
    path: '/set-password-employee',
    name: 'set-password-employee',
    component: () => import('../pages/Authentication/SetPassword.vue'),
  },
  {
    path: '/set-password-patient',
    name: 'set-password-patient',
    component: () => import('../pages/Authentication/SetPassword.vue'),
  },
  {
    path: '/forbidden',
    name: 'forbidden',
    component: () => import('../pages/Authentication/Forbidden.vue'),
  },
]

// Combine all routes
const routes = [
  ...patientRoutes,
  ...employeeRoutes,
  ...employeePractitionerRoutes,
  ...employeeOrganizationAdminRoutes,
  ...employeeAdminRoutes,
  ...superAdminRoutes,
  ...nonPatientRoutes,
  ...sharedAuthenticatedRoutes,
  ...publicRoutes,
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
})

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  if (to.meta.requiresAuth) {
    if (!authStore.user) {
      return next('/login')
    }
    if (
      to.meta.userRoles &&
      !(to.meta.userRoles as string[]).includes(authStore.user.selectedUserRole!)
    ) {
      console.log('user fail')
      return next('/forbidden')
    }
    if (
      to.meta.employeeRoles &&
      !authStore.user?.locationEmployeeRoles?.some((role) =>
        (to.meta.employeeRoles as string[]).includes(role),
      )
    ) {
      return next('/forbidden')
    }
  }
  if (to.matched.length === 0) {
    // Handle unspecified routes
    if (!authStore.user) {
      return next('/login')
    }

    if (authStore.user?.patientId) {
      return next('/patient-appointments')
    }
    if (authStore.user?.employeeId) {
      return next('/dashboard')
    }
    return next('/organizations')
  }
  next()
})

export default router
