<template>
  <div class="flex flex-col items-center text-white text-sm">
    <div class="font-medium">{{ currentTime }}</div>
    <div class="text-xs opacity-75">{{ currentDate }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'

const currentTime = ref('')
const currentDate = ref('')
let intervalId: number | null = null

const updateTime = () => {
  const now = new Date()

  // format time (12-hour format with AM/PM, no seconds)
  currentTime.value = now.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })

  // format date
  currentDate.value = now.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  // update immediately
  updateTime()

  // update every minute
  intervalId = setInterval(updateTime, 60000)
})

onBeforeUnmount(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>
