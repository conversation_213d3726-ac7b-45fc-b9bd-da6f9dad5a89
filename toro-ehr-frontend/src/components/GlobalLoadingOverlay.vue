<template>
  <Teleport to="body">
    <!-- Full overlay for mutations (POST, PUT, DELETE) -->
    <div
      v-if="loadingStore.isOverlayLoading"
      class="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
    >
      <div class="flex flex-col items-center space-y-4">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    </div>

    <!-- Small spinner for GET requests (top-right corner) -->
    <div
      v-else-if="loadingStore.isSpinnerLoading"
      class="fixed top-4 right-4 z-[9999]"
    >
      <div class="bg-white rounded-full p-2 shadow-lg">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useLoadingStore } from '@/stores/loading'

const loadingStore = useLoadingStore()
</script>
