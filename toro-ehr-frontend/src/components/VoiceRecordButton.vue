<template>
  <Button
    :icon="isRecording ? 'pi pi-stop' : 'pi pi-microphone'"
    :class="[
      'p-2',
      isRecording ? 'p-button-danger' : 'p-button-outlined',
      isRecording ? 'animate-pulse' : '',
      !isSupported ? 'opacity-50' : ''
    ]"
    :disabled="isProcessing || !isSupported"
    @click="toggleRecording"
    :aria-label="isRecording ? 'Stop recording' : 'Start recording'"
    size="small"
    text
    v-tooltip.top="tooltipText"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Button from 'primevue/button'

interface Props {
  sectionName?: string
  fieldName?: string
}

const props = withDefaults(defineProps<Props>(), {
  sectionName: 'Unknown Section',
  fieldName: 'Unknown Field'
})

const emit = defineEmits<{
  transcriptionUpdate: [text: string, isFinal: boolean]
  transcriptionComplete: [finalText: string]
  recordingError: [error: string]
}>()

const isRecording = ref(false)
const isProcessing = ref(false)
const isSupported = ref(false)
const recognition = ref<SpeechRecognition | null>(null)
const finalTranscript = ref('')
const interimTranscript = ref('')
const sentences = ref<string[]>([])

const tooltipText = computed(() => {
  if (!isSupported.value) {
    return 'Web Speech - not supported in this browser'
  }
  return isRecording.value ? 'Web Speech - Stop recording' : 'Web Speech - Start recording'
})

onMounted(() => {
  checkSpeechRecognitionSupport()
  initializeSpeechRecognition()
})

const checkSpeechRecognitionSupport = () => {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  isSupported.value = !!SpeechRecognition
}

const initializeSpeechRecognition = () => {
  if (!isSupported.value) return

  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
  recognition.value = new SpeechRecognition()

  // Configure recognition settings for better sentence detection
  recognition.value.continuous = true
  recognition.value.interimResults = true
  recognition.value.lang = 'en-US'
  recognition.value.maxAlternatives = 1

  // Force offline/local speech recognition when available
  try {
    // Set serviceURI to empty string to force offline mode
    if ('serviceURI' in recognition.value) {
      (recognition.value as any).serviceURI = ''
    }

    // Alternative approach for some browsers
    if ('webkitSpeechRecognition' in window) {
      const webkitRecognition = recognition.value as any
      if ('serviceURI' in webkitRecognition) {
        webkitRecognition.serviceURI = ''
      }
    }
  } catch (error) {
    // Fallback to default behavior if offline mode not supported
  }



  // Chrome-specific settings that can influence pause sensitivity
  if ('webkitSpeechRecognition' in window) {
    const webkitRecognition = recognition.value as any

    // These are experimental settings that may affect pause detection
    // Note: These are not officially documented but may influence behavior
    try {
      // Shorter timeout might make it more sensitive to pauses
      webkitRecognition.serviceURI = undefined // Use default service

      // Some browsers support these experimental properties:
      if ('speechTimeout' in webkitRecognition) {
        webkitRecognition.speechTimeout = 2000
      }
      if ('speechTimeoutBuffer' in webkitRecognition) {
        webkitRecognition.speechTimeoutBuffer = 1000
      }
    } catch (error) {
      // Ignore experimental settings errors
    }
  }

  // Handle results - each final result is typically a sentence or phrase
  recognition.value.onresult = (event) => {
    let interim = ''

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript.trim()

      if (event.results[i].isFinal && transcript) {
        // Each final result is treated as a sentence segment
        sentences.value.push(transcript)
        finalTranscript.value = sentences.value.join(' ')
      } else if (transcript) {
        interim = transcript
      }
    }

    interimTranscript.value = interim

    // Show current progress: completed sentences + interim
    const currentText = finalTranscript.value + (interim ? ' ' + interim : '')
    emit('transcriptionUpdate', currentText, false)
  }

  // Handle errors
  recognition.value.onerror = (event) => {
    const errorMessage = `Speech recognition error: ${event.error}`
    emit('recordingError', errorMessage)
    console.error('Speech recognition error:', event.error)
    isRecording.value = false
  }

  // Handle end
  recognition.value.onend = () => {
    isRecording.value = false
    if (finalTranscript.value.trim()) {
      emit('transcriptionComplete', finalTranscript.value)
    }
  }
}

const toggleRecording = () => {
  if (!isSupported.value) return

  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

const startRecording = () => {
  if (!recognition.value) return

  try {
    finalTranscript.value = ''
    interimTranscript.value = ''
    sentences.value = []

    // Add a small delay to help with initial word capture
    setTimeout(() => {
      if (recognition.value) {
        recognition.value.start()
        isRecording.value = true
      }
    }, 100)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to start speech recognition'
    emit('recordingError', errorMessage)
    console.error('Error starting speech recognition:', error)
  }
}

const stopRecording = () => {
  if (recognition.value && isRecording.value) {
    recognition.value.stop()
  }
}



// Add type declarations for Speech Recognition API
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition
    webkitSpeechRecognition: typeof SpeechRecognition
    SpeechGrammarList: any
  }
}
</script>
