<template>
  <Button
    :icon="isRecording ? 'pi pi-stop' : 'pi pi-microphone'"
    :class="[
      'p-2',
      isRecording ? 'p-button-danger' : 'p-button-outlined',
      isRecording ? 'animate-pulse' : '',
      !isSupported ? 'opacity-50' : ''
    ]"
    :disabled="isProcessing || !isSupported"
    @click="toggleRecording"
    :aria-label="isRecording ? 'Stop recording' : 'Start recording'"
    size="small"
    text
    v-tooltip.top="tooltipText"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import Button from 'primevue/button'
import { api } from '@/api'
import type { TranscribeAudioResponse } from '@/api/api-reference'

interface Props {
  sectionName?: string
  fieldName?: string
  model: 'small' | 'turbo' | 'distil'
}

const props = withDefaults(defineProps<Props>(), {
  sectionName: 'Unknown Section',
  fieldName: 'Unknown Field',
  model: 'small'
})

const emit = defineEmits<{
  transcriptionComplete: [finalText: string]
  recordingError: [error: string]
}>()

const isRecording = ref(false)
const isProcessing = ref(false)
const isSupported = ref(false)
const mediaRecorder = ref<MediaRecorder | null>(null)
const audioChunks = ref<Blob[]>([])
const stream = ref<MediaStream | null>(null)

const tooltipText = computed(() => {
  if (!isSupported.value) {
    return `Whisper ${props.model} - not supported in this browser`
  }
  return isRecording.value ? `Whisper ${props.model} - Stop recording` : `Whisper ${props.model} - Start recording`
})

onMounted(() => {
  checkAudioRecordingSupport()
})

const checkAudioRecordingSupport = () => {
  isSupported.value = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.MediaRecorder)
}

const toggleRecording = async () => {
  if (!isSupported.value) return

  if (isRecording.value) {
    stopRecording()
  } else {
    await startRecording()
  }
}

const startRecording = async () => {
  try {
    // Get microphone access
    stream.value = await navigator.mediaDevices.getUserMedia({ audio: true })

    // Create MediaRecorder
    mediaRecorder.value = new MediaRecorder(stream.value, {
      mimeType: 'audio/webm;codecs=opus'
    })

    audioChunks.value = []

    // Handle data available
    mediaRecorder.value.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.value.push(event.data)
      }
    }

    // Handle recording stop
    mediaRecorder.value.onstop = () => {
      const audioBlob = new Blob(audioChunks.value, { type: 'audio/webm' })
      sendAudioToAPI(audioBlob)

      // Clean up stream
      if (stream.value) {
        stream.value.getTracks().forEach(track => track.stop())
        stream.value = null
      }
    }

    // Start recording
    mediaRecorder.value.start()
    isRecording.value = true

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to start audio recording'
    emit('recordingError', errorMessage)
    console.error('Error starting audio recording:', error)
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
  }
}

const sendAudioToAPI = async (audioBlob: Blob) => {
  isProcessing.value = true

  try {
    // Create FormData with audio file and model parameter
    const formData = new FormData()
    formData.append('AudioFile', audioBlob, `recording-${Date.now()}.webm`)
    formData.append('Model', props.model)

    // Call transcription API with model parameter
    const response = await api.textFormatting.textFormattingTranscribeAudio(formData)
    const rawTranscribedText = response.data.transcribedText || ''

    // Emit the raw transcribed text
    emit('transcriptionComplete', rawTranscribedText)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to transcribe audio'
    console.error('Error transcribing audio:', error)
    emit('recordingError', errorMessage)
  } finally {
    isProcessing.value = false
  }
}
</script>
