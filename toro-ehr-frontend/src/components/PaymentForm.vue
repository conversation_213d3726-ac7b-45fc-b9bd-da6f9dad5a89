<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">New Payment</h1>
    </div>

    <div class="p-6 space-y-6">
      <!-- Amount Input -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="text-gray-500 text-sm">$</span>
          </div>
          <input
            v-model="amount"
            type="number"
            step="0.01"
            placeholder="0.00"
            class="block w-full pl-7 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg"
          />
        </div>
        <p v-if="amount < 1 && amount !== 0" class="mt-1 text-sm text-red-600">
          Minimum payment amount is $1.00
        </p>
      </div>

      <!-- Payment Cards -->
      <div v-if="paymentCards.length > 0">
        <label class="block text-sm font-medium text-gray-700 mb-3">Select Payment Method</label>
        <div class="space-y-3">
          <div
            v-for="card in paymentCards"
            :key="card.id"
            @click="selectedCardId = card.id || null"
            class="relative flex items-center justify-between border rounded-lg px-4 py-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            :class="{
              'border-blue-500 ring-2 ring-blue-100 bg-blue-50': selectedCardId === card.id,
              'border-gray-200 hover:border-gray-300 hover:bg-gray-50': selectedCardId !== card.id
            }"
          >
            <!-- Radio Button -->
            <div class="flex-shrink-0">
              <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors"
                   :class="{
                     'border-blue-500 bg-blue-500': selectedCardId === card.id,
                     'border-gray-300': selectedCardId !== card.id
                   }">
                <div v-if="selectedCardId === card.id" class="w-1.5 h-1.5 rounded-full bg-white"></div>
              </div>
            </div>

            <!-- Card Info -->
            <div class="flex items-center space-x-3 flex-1 ml-4">
              <div class="flex-shrink-0">
                <i class="text-xl"
                   :class="{
                     'pi pi-credit-card text-blue-600': card.type === 'Visa',
                     'pi pi-credit-card text-red-600': card.type === 'Mastercard',
                     'pi pi-credit-card text-gray-400': !['Visa', 'Mastercard'].includes(card.type || '')
                   }">
                </i>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900">
                  {{ card.type }} •••• {{ card.lastFour }}
                </div>
                <div class="text-xs text-gray-500">
                  Expires {{ formatExpiration(card.expirationDate) }}
                </div>
              </div>
            </div>

            <!-- Selected Badge -->
            <div v-if="selectedCardId === card.id"
                 class="flex items-center text-blue-600 text-sm font-medium mr-2">
              <i class="pi pi-check text-sm"></i>
            </div>

            <!-- Delete Button - Far Right -->
            <div class="flex-shrink-0">
              <Button
                v-tooltip.top="'Delete Card'"
                icon="pi pi-trash"
                variant="text"
                severity="danger"
                size="small"
                @click.stop="confirmDialog?.open(card.id)"
                :disabled="isLoading"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- No Cards Message -->
      <div v-else class="text-center py-6 bg-gray-50 rounded-lg">
        <i class="pi pi-credit-card text-2xl text-gray-400 mb-2 block"></i>
        <p class="text-sm text-gray-600">No saved payment methods</p>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
        <Button
          @click="payWithCardOnFile"
          :label="isLoadingCardOnFile ? 'Processing...' : 'Pay with Card on File'"
          :disabled="isLoading || selectedCardId === null || amount < 1"
          :loading="isLoading"
          class="flex-1 py-3"
          severity=""
        />
        <Button
          @click="payWithNewCard"
          :label="isLoadingNewCard ? 'Processing...' : 'Pay with New Card'"
          :disabled="isLoading || amount < 1"
          class="flex-1 py-3"
        />
      </div>
    </div>
  </div>

  <!-- Confirm Dialog for Card Deletion -->
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="deletePaymentCard"
    title="Delete Payment Card"
    message="Are you sure you want to delete this payment card? This action cannot be undone."
  />
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {api} from '@/api'
import Button from 'primevue/button'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import type {PaymentCardResponse, ProcessPaymentResponse} from '@/api/api-reference'
import { useToast } from 'vue-toastification'

interface Props {
  encounterId: string
  patientId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  paymentSuccess: [result: ProcessPaymentResponse]
  paymentError: [error: string]
}>()

// Payment form state
const amount = ref(0)
const isLoading = ref(false)
const isLoadingCardOnFile = ref(false)
const isLoadingNewCard = ref(false)
const paymentCards = ref<PaymentCardResponse[]>([])
const selectedCardId = ref<string | null>(null)

// Confirm dialog and toast
const confirmDialog = ref<any | null>(null)
const toast = useToast()

const formatExpiration = (dateStr?: string): string => {
  if (!dateStr || dateStr.length !== 4) return dateStr ?? '';
  return `${dateStr.slice(0, 2)}/${dateStr.slice(2)}`;
};

onMounted(async () => {
  try {
    // Fetch payment cards
    paymentCards.value = (await api.patients.patientListPaymentCards(props.patientId)).data
  } catch (error) {
    console.error('Failed to fetch payment cards', error)
    emit('paymentError', 'Failed to load payment methods')
  }
})

const payWithCardOnFile = async () => {
  isLoading.value = true
  isLoadingCardOnFile.value = true
  try {
    const response = await api.encounter.encounterChargeEncounterIposCloud({
      encounterId: props.encounterId,
      amount: amount.value,
      cardId: selectedCardId.value ?? undefined
    });
    emit('paymentSuccess', response.data)
    // Reset form
    amount.value = 0
    selectedCardId.value = null
  } catch (error) {
    console.error('Payment failed', error)
    emit('paymentError', 'Payment failed. Please try again.')
  } finally {
    isLoading.value = false
    isLoadingCardOnFile.value = false
  }
}

const payWithNewCard = async () => {
  isLoading.value = true
  isLoadingNewCard.value = true
  try {
    const response = await api.encounter.encounterChargeEncounterIpos({
      encounterId: props.encounterId,
      amount: amount.value,
    });
    emit('paymentSuccess', response.data)
    // Reset form
    amount.value = 0
    selectedCardId.value = null
  } catch (error) {
    console.error('Payment failed', error)
    emit('paymentError', 'Payment failed. Please try again.')
  } finally {
    isLoading.value = false
    isLoadingNewCard.value = false
  }
}

const deletePaymentCard = async (cardId: string) => {
  try {
    await api.patients.patientDeletePaymentCard({ cardId })

    // Remove card from local list
    paymentCards.value = paymentCards.value.filter(card => card.id !== cardId)

    // Clear selection if deleted card was selected
    if (selectedCardId.value === cardId) {
      selectedCardId.value = null
    }

    toast.success('Payment card deleted successfully')
  } catch (error) {
    console.error('Failed to delete payment card', error)
    toast.error('Failed to delete payment card. Please try again.')
  }
}
</script>
