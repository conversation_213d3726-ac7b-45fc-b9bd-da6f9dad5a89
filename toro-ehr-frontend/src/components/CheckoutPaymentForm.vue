<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">Payment Options</h1>
    </div>

    <div class="p-6 space-y-6">
      <!-- Insurance Warning Banner -->
      <div v-if="insuranceInfo && !insuranceInfo.hasInsurance"
           class="p-4 bg-red-50 border-l-4 border-red-400 rounded-lg shadow-sm">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i class="pi pi-exclamation-triangle text-red-500 text-xl"></i>
          </div>
          <div class="ml-3">
            <p class="text-red-800 font-semibold text-lg">Patient reports no insurance policies.</p>
          </div>
        </div>
      </div>

      <!-- Payment Type Selection -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
        <div class="space-y-3">
          <div
            @click="paymentType = 'insurance'"
            class="relative flex items-center justify-between border rounded-lg px-4 py-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            :class="{
              'border-blue-500 ring-2 ring-blue-100 bg-blue-50': paymentType === 'insurance',
              'border-gray-200 hover:border-gray-300 hover:bg-gray-50': paymentType !== 'insurance'
            }"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors"
                     :class="{
                       'border-blue-500 bg-blue-500': paymentType === 'insurance',
                       'border-gray-300': paymentType !== 'insurance'
                     }">
                  <div v-if="paymentType === 'insurance'" class="w-1.5 h-1.5 rounded-full bg-white"></div>
                </div>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900">File with Insurance</div>
                <div class="text-xs text-gray-500">Payment with insurance copay</div>
              </div>
            </div>
          </div>

          <div
            @click="paymentType = 'outOfPocket'"
            class="relative flex items-center justify-between border rounded-lg px-4 py-4 cursor-pointer transition-all duration-200 hover:shadow-md"
            :class="{
              'border-blue-500 ring-2 ring-blue-100 bg-blue-50': paymentType === 'outOfPocket',
              'border-gray-200 hover:border-gray-300 hover:bg-gray-50': paymentType !== 'outOfPocket'
            }"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors"
                     :class="{
                       'border-blue-500 bg-blue-500': paymentType === 'outOfPocket',
                       'border-gray-300': paymentType !== 'outOfPocket'
                     }">
                  <div v-if="paymentType === 'outOfPocket'" class="w-1.5 h-1.5 rounded-full bg-white"></div>
                </div>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900">Pay Out of Pocket</div>
                <div class="text-xs text-gray-500">Payment without insurance</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Skip Payment Option -->
      <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
        <input
          id="skipPayment"
          v-model="skipPaymentSelected"
          type="checkbox"
          class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
        />
        <label for="skipPayment" class="text-sm font-medium text-gray-700 cursor-pointer">
          Skip payment for now
        </label>
        <span class="text-xs text-gray-500">(Payment can be processed later)</span>
      </div>

      <!-- Amount Input -->
      <div v-if="!skipPaymentSelected">
        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Amount</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="text-gray-500 text-sm">$</span>
          </div>
          <input
            v-model="amount"
            type="number"
            step="0.01"
            placeholder="0.00"
            class="block w-full pl-7 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-lg"
          />
        </div>
        <p v-if="paymentType === 'insurance' && insuranceAmount !== null && amount === insuranceAmount" class="mt-1 text-sm text-blue-600">
          Amount suggested from insurance copay
        </p>
        <p v-else-if="amount < 1 && amount !== 0" class="mt-1 text-sm text-red-600">
          Minimum payment amount is $1.00
        </p>
      </div>

      <!-- Payment Cards (show when amount > 0 and not skipping payment) -->
      <div v-if="!skipPaymentSelected && amount > 0">
        <div v-if="paymentCards.length > 0">
          <label class="block text-sm font-medium text-gray-700 mb-3">Select Payment Method</label>
          <div class="space-y-3">
            <div
              v-for="card in paymentCards"
              :key="card.id"
              @click="selectedCardId = card.id || null"
              class="relative flex items-center justify-between border rounded-lg px-4 py-4 cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="{
                'border-blue-500 ring-2 ring-blue-100 bg-blue-50': selectedCardId === card.id,
                'border-gray-200 hover:border-gray-300 hover:bg-gray-50': selectedCardId !== card.id
              }"
            >
              <!-- Radio Button -->
              <div class="flex-shrink-0">
                <div class="w-4 h-4 rounded-full border-2 flex items-center justify-center transition-colors"
                     :class="{
                       'border-blue-500 bg-blue-500': selectedCardId === card.id,
                       'border-gray-300': selectedCardId !== card.id
                     }">
                  <div v-if="selectedCardId === card.id" class="w-1.5 h-1.5 rounded-full bg-white"></div>
                </div>
              </div>

              <!-- Card Info -->
              <div class="flex items-center space-x-3 flex-1 ml-4">
                <div class="flex-shrink-0">
                  <i class="text-xl"
                     :class="{
                       'pi pi-credit-card text-blue-600': card.type === 'Visa',
                       'pi pi-credit-card text-red-600': card.type === 'Mastercard',
                       'pi pi-credit-card text-gray-400': !['Visa', 'Mastercard'].includes(card.type || '')
                     }">
                  </i>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ card.type }} •••• {{ card.lastFour }}
                  </div>
                  <div class="text-xs text-gray-500">
                    Expires {{ formatExpiration(card.expirationDate) }}
                  </div>
                </div>
              </div>

              <!-- Selected Badge -->
              <div v-if="selectedCardId === card.id"
                   class="flex items-center text-blue-600 text-sm font-medium mr-2">
                <i class="pi pi-check text-sm"></i>
              </div>

              <!-- Delete Button - Far Right -->
              <div class="flex-shrink-0">
                <Button
                  v-tooltip.top="'Delete Card'"
                  icon="pi pi-trash"
                  variant="text"
                  severity="danger"
                  size="small"
                  @click.stop="confirmDialog?.open(card.id)"
                  :disabled="isLoading"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- No Cards Message -->
        <div v-else class="text-center py-6 bg-gray-50 rounded-lg">
          <i class="pi pi-credit-card text-2xl text-gray-400 mb-2 block"></i>
          <p class="text-sm text-gray-600">No saved payment methods</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
          <Button
            @click="payWithCardOnFile"
            :label="isLoadingCardOnFile ? 'Processing...' : 'Pay with Card on File'"
            :disabled="isLoading || selectedCardId === null || amount < 1"
            :loading="isLoading"
            class="flex-1 py-3"
            severity=""
          />
          <Button
            @click="payWithNewCard"
            :label="isLoadingNewCard ? 'Processing...' : 'Pay with New Card'"
            :disabled="isLoading || amount < 1"
            class="flex-1 py-3"
          />
        </div>
      </div>

      <!-- Skip Payment Confirmation -->
      <div v-if="skipPaymentSelected" class="pt-4 border-t border-gray-200">
        <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="flex items-center">
            <i class="pi pi-info-circle text-blue-600 mr-2"></i>
            <span class="text-sm text-blue-800 font-medium">Payment will be skipped</span>
          </div>
          <p class="text-xs text-blue-600 mt-1">
            You can process payment later from the encounter payments page
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Confirm Dialog for Card Deletion -->
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="deletePaymentCard"
    title="Delete Payment Card"
    message="Are you sure you want to delete this payment card? This action cannot be undone."
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { api } from '@/api'
import Button from 'primevue/button'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import type { PaymentCardResponse, ProcessPaymentResponse, PatientInsuranceInfoResponse } from '@/api/api-reference'
import { useToast } from 'vue-toastification'

interface Props {
  encounterId: string
  patientId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  paymentSuccess: [result: ProcessPaymentResponse]
  paymentError: [error: string]
  paymentSkipped: []
}>()

// Payment form state
const paymentType = ref<'insurance' | 'outOfPocket'>('insurance')
const amount = ref(0)
const insuranceAmount = ref<number | null>(null)
const insuranceInfo = ref<PatientInsuranceInfoResponse | null>(null)
const skipPaymentSelected = ref(false)
const isLoading = ref(false)
const isLoadingCardOnFile = ref(false)
const isLoadingNewCard = ref(false)
const paymentCards = ref<PaymentCardResponse[]>([])
const selectedCardId = ref<string | null>(null)

// Confirm dialog and toast
const confirmDialog = ref<any | null>(null)
const toast = useToast()

const formatExpiration = (dateStr?: string): string => {
  if (!dateStr || dateStr.length !== 4) return dateStr ?? '';
  return `${dateStr.slice(0, 2)}/${dateStr.slice(2)}`;
};

// Watch payment type changes
watch(paymentType, (newType) => {
  if (newType === 'insurance' && insuranceAmount.value !== null) {
    amount.value = insuranceAmount.value
  } else if (newType === 'outOfPocket') {
    amount.value = 0
  }
})

// Watch skip payment changes
watch(skipPaymentSelected, (isSkipped) => {
  if (isSkipped) {
    emit('paymentSkipped')
  }
})

onMounted(async () => {
  try {
    // Fetch payment cards
    paymentCards.value = (await api.patients.patientListPaymentCards(props.patientId)).data

    // Fetch insurance info
    const insuranceResponse = await api.patients.patientGetInsuranceInfo({ patientId: props.patientId })
    insuranceInfo.value = insuranceResponse.data

    if (insuranceInfo.value.hasInsurance && insuranceInfo.value.primaryCopay) {
      insuranceAmount.value = insuranceInfo.value.primaryCopay
      amount.value = insuranceAmount.value
    } else {
      // If no insurance, default to out of pocket
      paymentType.value = 'outOfPocket'
    }
  } catch (error) {
    console.error('Failed to fetch payment data', error)
    emit('paymentError', 'Failed to load payment information')
  }
})

const payWithCardOnFile = async () => {
  isLoading.value = true
  isLoadingCardOnFile.value = true
  try {
    const response = await api.encounter.encounterChargeEncounterIposCloud({
      encounterId: props.encounterId,
      amount: amount.value,
      cardId: selectedCardId.value ?? undefined
    });
    emit('paymentSuccess', response.data)
    // Reset form
    amount.value = 0
    selectedCardId.value = null
  } catch (error) {
    console.error('Payment failed', error)
    emit('paymentError', 'Payment failed. Please try again.')
  } finally {
    isLoading.value = false
    isLoadingCardOnFile.value = false
  }
}

const payWithNewCard = async () => {
  isLoading.value = true
  isLoadingNewCard.value = true
  try {
    const response = await api.encounter.encounterChargeEncounterIpos({
      encounterId: props.encounterId,
      amount: amount.value,
    });
    emit('paymentSuccess', response.data)
    // Reset form
    amount.value = 0
    selectedCardId.value = null
  } catch (error) {
    console.error('Payment failed', error)
    emit('paymentError', 'Payment failed. Please try again.')
  } finally {
    isLoading.value = false
    isLoadingNewCard.value = false
  }
}

const deletePaymentCard = async (cardId: string) => {
  try {
    await api.patients.patientDeletePaymentCard({ cardId })

    // Remove card from local list
    paymentCards.value = paymentCards.value.filter(card => card.id !== cardId)

    // Clear selection if deleted card was selected
    if (selectedCardId.value === cardId) {
      selectedCardId.value = null
    }

    toast.success('Payment card deleted successfully')
  } catch (error) {
    console.error('Failed to delete payment card', error)
    toast.error('Failed to delete payment card. Please try again.')
  }
}

</script>
