<template>
  <div>
    <div>{{ formattedAddress }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  street: String,
  city: String,
  state: String,
  zipCode: String,
})
const formattedAddress = computed(() => {
  const addressArray = []
  if (props.street) {
    addressArray.push(props.street)
  }

  if (props.city) {
    addressArray.push(props.city)
  }

  if (props.state) {
    addressArray.push(props.state)
  }

  if (props.zipCode) {
    addressArray.push(props.zipCode)
  }
  return addressArray.join(', ')
})
</script>
