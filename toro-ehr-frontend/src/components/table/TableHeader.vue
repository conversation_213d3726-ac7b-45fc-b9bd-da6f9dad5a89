<template>
  <div
    class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200"
  >
    <!-- Left Side (Inputs) -->
    <div class="flex flex-wrap gap-4">
      <slot name="inputs"></slot>
    </div>

    <!-- Right Side (Buttons) -->
    <div class="flex gap-x-2">
      <slot name="buttons"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
// No specific logic needed; the component is purely for layout
</script>

<style scoped>
/* Add any additional styles if necessary */
</style>
