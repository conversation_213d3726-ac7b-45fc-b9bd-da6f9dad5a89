<template>
  <div
    class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200"
  >
    <div>
      <p class="text-sm text-gray-600">
        <span class="font-semibold text-gray-800">{{ totalItems }}</span>
        results
      </p>
    </div>

    <div>
      <div class="inline-flex gap-x-2">
        <button
          @click="$emit('prevPage')"
          :disabled="isFirstPage"
          type="button"
          class="py-1.5 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50"
        >
          <ChevronLeftIcon class="shrink-0 size-4" />
          Prev
        </button>

        <button
          @click="$emit('nextPage')"
          :disabled="isLastPage"
          type="button"
          class="py-1.5 px-2 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50"
        >
          Next
          <ChevronRightIcon class="shrink-0 size-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline'

defineProps({
  totalItems: Number,
  isFirstPage: Boolean,
  isLastPage: Boolean,
})

defineEmits(['prevPage', 'nextPage'])
</script>
