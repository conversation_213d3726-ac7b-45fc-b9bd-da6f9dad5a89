<template>
  <Button
    icon="pi pi-sparkles"
    :class="[
      'p-2',
      'p-button-outlined'
    ]"
    :disabled="isProcessing || !hasText"
    @click="formatText"
    :aria-label="'Format text'"
    size="small"
    text
    v-tooltip.top="tooltipText"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Button from 'primevue/button'
import { api } from '@/api'
import type { FormatTextCommand } from '@/api/api-reference'

interface Props {
  text: string
  sectionName?: string
  fieldName?: string
}

const props = withDefaults(defineProps<Props>(), {
  sectionName: 'Unknown Section',
  fieldName: 'Unknown Field'
})

const emit = defineEmits<{
  textFormatted: [formattedText: string]
  formatError: [error: string]
}>()

const isProcessing = ref(false)

const hasText = computed(() => {
  return props.text && props.text.trim().length > 0
})

const tooltipText = computed(() => {
  if (!hasText.value) {
    return 'Formatting - No text to format'
  }
  return isProcessing.value ? 'Formatting - Processing...' : 'Formatting - Format text with AI'
})

const formatText = async () => {
  if (!hasText.value) return

  isProcessing.value = true

  try {
    // Call the text formatting API
    const formatCommand: FormatTextCommand = {
      text: props.text,
      timestamp: new Date().toISOString()
    }

    const response = await api.textFormatting.textFormattingFormatText(formatCommand)
    const formattedText = response.data.formattedText || props.text

    // Emit the formatted text
    emit('textFormatted', formattedText)

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to format text'
    console.error('Error formatting text:', error)
    emit('formatError', errorMessage)
  } finally {
    isProcessing.value = false
  }
}
</script>
