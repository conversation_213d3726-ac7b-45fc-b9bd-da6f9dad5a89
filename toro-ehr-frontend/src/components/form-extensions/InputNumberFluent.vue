<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <FloatLabel variant="on">
        <InputNumber
          v-model="value"
          :inputId="id"
          :invalid="!!errorMessage"
          v-bind="attrs"
          :fluid="fluid"
        />
        <label :for="id">{{ label }}</label>
      </FloatLabel>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { useAttrs } from 'vue'
import InputNumber from 'primevue/inputnumber'
import FloatLabel from 'primevue/floatlabel'

const attrs = useAttrs()
defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: { type: String, required: true },
  label: { type: String, required: true },
  wrapperClass: { type: String, default: '' },
  autocomplete: { type: String, default: 'off' },
  fluid: { type: Boolean, default: true },
  modelValue: Number,
})

const { value, errorMessage } = useField<number | null>(() => props.id, undefined, {
  syncVModel: true,
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
