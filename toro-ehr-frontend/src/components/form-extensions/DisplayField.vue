<template>
  <div class="rounded-xl border border-gray-200 shadow-sm p-4 bg-white">
    <p class="text-xs uppercase font-semibold text-gray-500 tracking-wider mb-1">
      {{ label }}
    </p>
    <p class="text-base text-gray-800">
      {{ value || '—' }}
    </p>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  label: string
  value?: string | number | null
}>()
</script>
