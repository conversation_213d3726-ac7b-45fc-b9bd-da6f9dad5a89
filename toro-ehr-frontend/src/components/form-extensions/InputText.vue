<template>
  <div :class="['relative', wrapperClass]">
    <div class="relative">
      <input
        :id="id"
        v-model="value"
        :class="[
          'block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 border bg-transparent rounded-lg border-1 appearance-none focus:outline-none focus:ring-0 peer',
          errorMessage
            ? 'border-red-600 focus:border-red-600'
            : 'border-primary focus:border-toroblue-600',
          meta.valid ? 'valid' : '',
        ]"
        :placeholder="placeholder"
        :type="type || 'text'"
        v-on="validationListeners"
        @focus="$emit('focus', $event)"
      />
      <label
        :for="id"
        class="absolute text-sm text-gray-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white px-2 peer-focus:px-2 peer-focus:text-toroblue-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1"
      >
        {{ label }}
      </label>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
// import {reactive, watch} from "vue";

defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: '',
  },
  wrapperClass: {
    type: String,
    default: 'mb-8',
  },
  type: String,
  modelValue: String,
})

const { value, errorMessage, meta, handleChange, handleBlur } = useField(
  () => props.id,
  undefined,
  {
    syncVModel: true,
    validateOnValueUpdate: false,
  },
)

const validationListeners = {
  blur: (evt: Event | undefined) => handleBlur(evt, true),
  change: handleChange,
  input: (evt: Event | undefined) => handleChange(evt, !!errorMessage.value),
}
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
