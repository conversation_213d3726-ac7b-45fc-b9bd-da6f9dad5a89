<template>
  <div :class="['flex', wrapperClass]">
    <input
      type="checkbox"
      class="shrink-0 mt-0.5 border-gray-200 rounded text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-gray-800"
      :id="id"
      v-model="isChecked"
      :disabled="disabled"
    />
    <label :for="id" class="text-sm text-gray-500 ms-3 dark:text-neutral-400">{{ label }}</label>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    default: false, // Default value for the checkbox (unchecked)
  },
  disabled: {
    type: Boolean,
    default: false, // Optionally disable the checkbox
  },
  wrapperClass: {
    type: String,
    default: 'mb-8',
  },
})

const emits = defineEmits(['update:modelValue']) // Emit update for v-model binding

const isChecked = ref(props.modelValue) // Create internal state for checkbox

watch(isChecked, (newValue) => {
  emits('update:modelValue', newValue)
})
</script>
