<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <FloatLabel variant="on">
        <div class="flex gap-2 items-center">
          <InputText v-model="systolic" maxlength="3" class="w-16" />
          /
          <InputText v-model="diastolic" maxlength="3" class="w-16" />
        </div>
        <label :for="id">{{ label }}</label>
      </FloatLabel>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { computed, onMounted, ref, watch } from 'vue'
import FloatLabel from 'primevue/floatlabel'
import InputText from 'primevue/inputtext'

defineEmits(['update:modelValue', 'focus'])

const systolic = ref('')
const diastolic = ref('')
const bloodPressure = computed(() => `${systolic.value}/${diastolic.value}`)

const props = defineProps({
  id: { type: String, required: true },
  label: { type: String, required: true },
  wrapperClass: { type: String, default: '' },
  modelValue: String,
  initialSystolic: { type: String, default: '' },
  initialDiastolic: { type: String, default: '' },
})

const { value, errorMessage } = useField<string | undefined>(() => props.id, undefined, {
  syncVModel: true,
})

onMounted(() => {
  if (props.initialSystolic) {
    systolic.value = props.initialSystolic
  }
  if (props.initialDiastolic) {
    diastolic.value = props.initialDiastolic
  }
})

watch(
  () => props.initialSystolic,
  (newValue) => {
    systolic.value = newValue
  },
)
watch(
  () => props.initialDiastolic,
  (newValue) => {
    systolic.value = newValue
  },
)

watch(bloodPressure, (newValue) => {
  value.value = newValue
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
