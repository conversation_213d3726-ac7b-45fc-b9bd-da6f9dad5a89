<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <FloatLabel variant="on">
        <Textarea
          :id="id"
          v-model="value"
          :invalid="!!errorMessage"
          v-bind="attrs"
          v-on="validationListeners"
          :fluid="fluid"
          rows="5"
        />
        <label :for="id">{{ label }}</label>
      </FloatLabel>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { useAttrs, watch } from 'vue'
import Textarea from 'primevue/textarea'
import FloatLabel from 'primevue/floatlabel'

const attrs = useAttrs()
const emits = defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  wrapperClass: {
    type: String,
    default: '',
  },
  fluid: {
    type: Boolean,
    default: true,
  },
  modelValue: String,
})

const { value, errorMessage, handleChange, handleBlur } = useField<string | null>(
  () => props.id,
  undefined,
  {
    syncVModel: true,
    validateOnValueUpdate: false,
  },
)

const validationListeners = {
  blur: (evt: Event | undefined) => handleBlur(evt, true),
  change: handleChange,
  input: (evt: Event | undefined) => handleChange(evt, !!errorMessage.value),
}

watch(value, (newValue) => {
  emits('update:modelValue', newValue)
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
