<template>
  <div
    v-if="modelValue"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-30"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">{{ title }}</h2>
      <p class="text-gray-700 mb-6">
        {{ message }}
      </p>
      <div class="flex justify-end space-x-4">
        <button @click="confirm" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
          Confirm
        </button>
        <button
          @click="close"
          class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineExpose, defineEmits } from 'vue'

defineProps<{
  title: string
  message: string
}>()

const modelValue = defineModel<boolean>()
const selectedIndex = ref<number | null>(null)

const emit = defineEmits(['confirmedAction']) // ✅ Define emit properly

const open = (index: number) => {
  selectedIndex.value = index
  modelValue.value = true
}

const close = () => {
  modelValue.value = false
}

const confirm = () => {
  if (selectedIndex.value !== null) {
    emit('confirmedAction', selectedIndex.value) // ✅ No more ReferenceError
  }
  close()
}

defineExpose({ open }) // Expose the `open()` method to the parent
</script>
