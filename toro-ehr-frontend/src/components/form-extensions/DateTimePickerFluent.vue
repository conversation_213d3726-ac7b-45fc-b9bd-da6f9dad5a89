<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <FloatLabel variant="on">
        <DatePicker
          v-model="value"
          :inputId="id"
          showIcon
          iconDisplay="input"
          :invalid="!!errorMessage"
          v-bind="attrs"
          class="w-full"
        />
        <label :for="id">{{ label }}</label>
      </FloatLabel>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { useAttrs, watch } from 'vue'
import DatePicker from 'primevue/datepicker'
import FloatLabel from 'primevue/floatlabel'

const attrs = useAttrs()
const emits = defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  wrapperClass: {
    type: String,
    default: '',
  },
  autocomplete: {
    type: String,
    default: 'off',
  },
  fluid: {
    type: Boolean,
    default: true,
  },
  modelValue: Date,
})

const { value, errorMessage } = useField<Date | null>(() => props.id, undefined, {
  syncVModel: true,
})

watch(value, (newValue) => {
  emits('update:modelValue', newValue)
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
