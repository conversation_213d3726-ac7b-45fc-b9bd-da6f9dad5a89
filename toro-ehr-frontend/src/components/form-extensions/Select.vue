<template>
  <div :class="['relative', wrapperClass]">
    <div class="relative">
      <select
        :id="id"
        v-model="value"
        :class="[
          'block px-2.5 pb-2.5 pt-4 w-full text-sm text-gray-900 border bg-transparent rounded-lg border-1 appearance-none focus:outline-none focus:ring-0 peer',
          errorMessage
            ? 'border-red-600 focus:border-red-600'
            : 'border-primary focus:border-toroblue-600',
          meta.valid ? 'valid' : '',
          value || (!value && placeholder) ? 'checked' : '',
        ]"
        v-on="validationListeners"
      >
        <option v-if="placeholder" value="">{{ placeholder }}</option>
        <option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.text }}
        </option>
      </select>
      <label
        :for="id"
        class="absolute text-sm text-gray-500 duration-300 transform bg-white px-2 z-10 origin-[0] top-1/2 left-2 -translate-y-1/2 peer-focus:top-2 peer-focus:left-2 peer-focus:text-toroblue-600 peer-focus:scale-75 peer-focus:-translate-y-4 peer-placeholder-shown:top-1/2 peer-placeholder-shown:left-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1"
      >
        {{ label }}
      </label>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import type { PropType } from 'vue'
import type { SelectListItem } from '../../utils/interfaces'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: '',
  },
  wrapperClass: {
    type: String,
    default: 'mb-8',
  },
  options: {
    type: Array as PropType<SelectListItem[]>,
    required: true,
  },
  modelValue: String,
})

const { value, errorMessage, meta, handleChange, handleBlur } = useField(
  () => props.id,
  undefined,
  {
    syncVModel: true,
    validateOnValueUpdate: false,
  },
)

const validationListeners = {
  blur: (evt: Event | undefined) => handleBlur(evt, true),
  change: handleChange,
}
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Adjust label position based on the 'checked' class */
select.checked ~ label,
select[placeholder]:not(:placeholder-shown) ~ label {
  top: -0.5rem;
  left: 0.5rem;
  transform: scale(0.75);
  inset-inline-start: 0.25rem;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: theme('colors.red.600'); /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
