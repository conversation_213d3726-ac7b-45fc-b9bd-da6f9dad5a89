<template>
  <FloatLabel variant="on">
    <AutoComplete
      v-model="value"
      :suggestions="suggestions"
      @complete="searchAddresses"
      @option-select="onAddressSelect"
      :input-id="id"
      :placeholder="placeholder"
      option-label="formattedAddress"
      fluid
    />
    <label :for="id">{{ label }}</label>
  </FloatLabel>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AutoComplete from 'primevue/autocomplete'
import FloatLabel from 'primevue/floatlabel'
import debounce from 'lodash.debounce'
import Radar from 'radar-sdk-js'

interface RadarAddress {
  formattedAddress: string
  street: string
  city: string
  state: string
  postalCode: string
}

const value = defineModel<string>({
  default: '',
})

const emit = defineEmits<{
  addressSelected: [
    address: {
      street: string
      city: string
      state: string
      zipCode: string
    },
  ]
}>()

defineProps<{
  id: string
  label: string
  placeholder?: string
}>()

const suggestions = ref<RadarAddress[]>([])

onMounted(() => {
  Radar.initialize(import.meta.env.VITE_RADAR_API_KEY)
})

const searchAddresses = debounce(async (event: { query: string }) => {
  if (!event.query || event.query.length < 3) {
    suggestions.value = []
    return
  }

  try {
    const result = await Radar.autocomplete({
      query: event.query,
      limit: 10,
    })

    if (result.addresses && result.addresses.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      suggestions.value = result.addresses.map((addr: any) => ({
        formattedAddress: addr.formattedAddress,
        street: addr.formattedAddress.split(',')[0],
        city: addr.city,
        state: addr.stateCode,
        postalCode: addr.postalCode,
      }))
    } else {
      suggestions.value = []
    }
  } catch (error) {
    console.error('Address search failed:', error)
    suggestions.value = []
  }
}, 300)

const onAddressSelect = (event: { value: RadarAddress }) => {
  const selectedAddress = event.value
  emit('addressSelected', {
    street: selectedAddress.street,
    city: selectedAddress.city,
    state: selectedAddress.state,
    zipCode: selectedAddress.postalCode,
  })
}
</script>
