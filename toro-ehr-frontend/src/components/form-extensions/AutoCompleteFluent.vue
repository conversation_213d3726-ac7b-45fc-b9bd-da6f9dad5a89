<template>
  <FloatLabel variant="on">
    <AutoComplete
      v-model="value"
      :suggestions="options"
      @complete="search"
      :virtualScrollerOptions="vsOptions"
      :input-id="id"
      v-bind="attrs"
      fluid
    />
    <label :for="id">{{ label }}</label>
  </FloatLabel>
</template>

<script setup lang="ts">
import { ref, useAttrs, type PropType } from 'vue'
import AutoComplete, { type AutoCompleteCompleteEvent } from 'primevue/autocomplete'
import FloatLabel from 'primevue/floatlabel'
import debounce from 'lodash.debounce'
import type { SearchOrderEntryResponse } from '../../api/api-reference'

const attrs = useAttrs()
const value = defineModel<string | SearchOrderEntryResponse | null>({
  // runtime type guard so Vue doesn't warn
  type: [String, Object] as PropType<string | SearchOrderEntryResponse | null>,
  default: null,
})
const emits = defineEmits(['search'])

const page = ref(1)
const currentQuery = ref('')

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  options: {
    type: Array,
    required: true,
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
})

const search = debounce((event: AutoCompleteCompleteEvent) => {
  currentQuery.value = event.query ?? ''
  page.value = 1
  emits('search', currentQuery.value, page.value, true)
}, 300)

function onLazyLoad(e: { first: number; last: number; rows: number }) {
  const nearEnd = e.last >= props.options.length - 5
  if (nearEnd && props.hasMore) {
    page.value += 1
    emits('search', currentQuery.value, page.value, false)
  }
}

const vsOptions = {
  lazy: true,
  onLazyLoad,
  itemSize: 40,
  showLoader: false,
}
</script>
