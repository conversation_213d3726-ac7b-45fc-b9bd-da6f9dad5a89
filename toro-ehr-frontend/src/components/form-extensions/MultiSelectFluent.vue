<template>
  <div :class="['relative', wrapperClass]">
    <div class="my-3">
      <FloatLabel variant="on">
        <MultiSelect
          :inputId="id"
          v-model="value"
          optionLabel="text"
          option-value="value"
          :invalid="!!errorMessage"
          v-bind="attrs"
          class="w-full"
          filter
        />
        <label :for="id">{{ label }}</label>
      </FloatLabel>
    </div>
    <transition name="fade">
      <small v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </small>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useField } from 'vee-validate'
import { useAttrs, watch } from 'vue'
import MultiSelect from 'primevue/multiselect'
import FloatLabel from 'primevue/floatlabel'

const attrs = useAttrs()
const emits = defineEmits(['update:modelValue', 'focus'])

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  label: {
    type: String,
    required: true,
  },
  wrapperClass: {
    type: String,
    default: '',
  },
  modelValue: Array,
})

const { value, errorMessage } = useField<string[]>(() => props.id, undefined, {
  syncVModel: true,
})

watch(value, (newValue) => {
  emits('update:modelValue', newValue)
})
</script>

<style scoped>
/* Fade transition styles */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.error-message {
  position: absolute;
  bottom: -0.5rem;
  right: 0.5rem;
  transform: scale(0.75);
  color: #dc2626; /* Tailwind's red-600 */
  --tw-bg-opacity: 1;
  background-color: white;
  padding-left: 0.625rem;
  text-align: right;
  padding-right: 0.625rem;
}
</style>
