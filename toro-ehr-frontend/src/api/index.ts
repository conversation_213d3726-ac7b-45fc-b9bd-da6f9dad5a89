import { Api } from './api-reference'
import { useToast } from 'vue-toastification'
import { getRouterInstance } from '@/utils/routerUtils'
import { useLoadingStore } from '@/stores/loading'

// extend axios config to include metadata for request tracking
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      requestId: string
      silent?: boolean // flag to skip loaders and error toasts
    }
  }
}

const toast = useToast()

export const api = new Api({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  securityWorker: (token) => (token ? { headers: { Authorization: `${String(token)}` } } : {}),
  format: 'json',
  secure: true,
})

// request interceptor - show loading for all API calls
api.instance.interceptors.request.use(
  (config) => {
    // get loading store instance inside interceptor
    const loadingStore = useLoadingStore()

    // generate unique request ID for tracking
    const requestId = Math.random().toString(36).substr(2, 9)
    const silent = config.metadata?.silent || false
    config.metadata = { requestId, silent }

    // skip loading indicators for silent requests
    if (!silent) {
      // determine if this is a mutation (POST, PUT, DELETE) or query (GET)
      const method = config.method?.toUpperCase()
      const isMutation = method === 'POST' || method === 'PUT' || method === 'PATCH' || method === 'DELETE'

      if (isMutation) {
        // show full overlay for mutations (blocks user interaction)
        loadingStore.startLoading('', requestId)
      } else {
        // for GET requests, just show a small spinner (less intrusive)
        // we'll handle this differently in the store
        loadingStore.startLoading('', requestId, false) // false = no overlay
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

api.instance.interceptors.response.use(
  (response) => {
    // get loading store instance inside interceptor
    const loadingStore = useLoadingStore()

    // stop loading for successful responses
    const requestId = response.config.metadata?.requestId
    if (requestId) {
      loadingStore.stopLoading(requestId)
    }
    return response
  },
  async (error) => {
    // get loading store instance inside interceptor
    const loadingStore = useLoadingStore()

    // stop loading for error responses
    const requestId = error.config?.metadata?.requestId
    const silent = error.config?.metadata?.silent || false

    if (requestId) {
      loadingStore.stopLoading(requestId)
    }

    // Skip error toasts for silent requests
    if (!silent) {
      const router = getRouterInstance()
      if (error.response) {
        const { status, data } = error.response

        switch (status) {
          case 401: // Unauthorized
            // todo: implement refresh token attempt
            await router.push({name: 'login'})
            break

          case 403: // Forbidden
            toast.error('You do not have permission to access this resource.')
            router.push({ name: 'forbidden' })
            break

          case 400: // Bad Request
            // Pass errors to the component via rejection
            return Promise.reject(data)

          case 404: // Not Found
            toast.error(data.title || 'The requested resource was not found.')
            break

          case 500: // Internal Server Error
            toast.error('An unexpected error occurred. Please try again later.')
            break

          default: // Handle other status codes
            toast.error('An unexpected error occurred.')
            break
        }
      } else {
        // Handle network errors
        toast.error('Network error. Please check your internet connection.')
      }
    }

    return Promise.reject(error)
  },
)
