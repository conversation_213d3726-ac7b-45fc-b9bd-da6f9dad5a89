<template>
  <div class="p-4 sm:p-5">
    <Card>
      <template #title> {{ isEditMode ? 'Edit Note Template' : 'Create Note Template' }}</template>
      <template #content>
        <div class="p-fluid">
          <!-- Title & Location in one row -->
          <div class="flex flex-col sm:flex-row gap-4 mb-4">
            <div class="w-full sm:w-1/3">
              <InputText id="name" label="Name" class="w-full" />
            </div>
            <div class="w-full sm:w-1/3">
              <Select
                id="classification"
                :options="classifications"
                v-model="selectedClassification"
                optionLabel="label"
                label="Classification"
                optionValue="value"
                class="w-full"
              />
            </div>
            <div class="w-full sm:w-1/3">
              <Select
                id="specialization"
                v-model="selectedSpecialization"
                :options="specializations"
                label="Specialization"
                optionLabel="label"
                optionValue="value"
                class="w-full"
              />
            </div>
          </div>

          <div class="flex flex-col sm:flex-row gap-4 mb-6">
            <MultiSelect
              v-if="isOrganizationAdmin && locations.length > 0"
              id="locations"
              label="Locations"
              v-model="selectedLocations"
              :options="locations"
              optionLabel="text"
              optionValue="value"
              filter
              class="w-full"
            />

            <Select
              id="documentType"
              v-model="selectedDocumentType"
              :options="documentTypes.map((d) => ({ label: d, value: d }))"
              label="Document Type"
              optionLabel="label"
              optionValue="value"
              class="w-full"
            />
          </div>

          <SectionSelector
            v-if="selectedDocumentType"
            :required-sections="requiredSections"
            :recommended-sections="recommendedSections"
            :additional-sections="additionalSections"
            :selected-fields="selectedFields"
            @update:selected-fields="selectedFields = $event"
          />

          <div class="card flex flex-col sm:flex-row justify-end gap-2 sm:gap-4 mb-2 mt-6">
            <Button
              class="px-4 py-2"
              label="Close"
              severity="secondary"
              variant="text"
              @click="goToNoteTemplatesList()"
            />
            <Button label="Save" @click="submitForm" class="" />
          </div>
        </div>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { specialtiesCodes, documentSectionsMap } from '@/utils/specialties'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Select from '@/components/form-extensions/SelectFluent.vue'
import { ref, watch, watchEffect, computed, onBeforeMount } from 'vue'
import type {
  CreateNoteTemplateCommand,
  UpdateNoteTemplateCommand,
  SelectListItem,
  NoteTemplateFieldResponse,
} from '@/api/api-reference.ts'
import Button from 'primevue/button'
import { api } from '@/api'
import Card from 'primevue/card'
import { useToast } from 'vue-toastification'
import { useForm } from 'vee-validate'
import * as yup from 'yup'
import { useRoute, useRouter } from 'vue-router'
import MultiSelect from '@/components/form-extensions/MultiSelectFluent.vue'
import SectionSelector from './components/SectionSelector.vue'
import { useAuthStore } from '@/stores/auth.ts'

const toast = useToast()
const route = useRoute()
const router = useRouter()

const authStore = useAuthStore()
const isOrganizationAdmin = ref(
  authStore.user?.selectedUserRole == 'Employee' &&
    authStore.user?.locationEmployeeRoles?.includes('OrganizationAdmin'),
)
const isEditMode = ref(false)
const skipFieldsReset = ref(true)

onBeforeMount(async () => {
  if (isOrganizationAdmin.value) {
    await fetchLocations()
  }
})

const { handleSubmit, setValues } = useForm({
  validationSchema: yup.object({
    name: yup.string().required(),
    classification: yup.string().required(),
    documentType: yup.string().required(),
    locations: yup
      .array()
      .of(yup.string())
      .min(1, 'At least one location must be selected')
      .when([], {
        is: () => isOrganizationAdmin.value,
        then: (schema) => schema.required('At least one location must be selected'),
        otherwise: (schema) => schema.nullable(),
      }),
    customSections: yup.array().of(
      yup.object().shape({
        name: yup.string().required('name is required'),
      }),
    ),
  }),
})

const noteTemplate = ref<(CreateNoteTemplateCommand | UpdateNoteTemplateCommand) & { id?: string }>(
  {
    name: '',
    classification: '',
    specialityCode: '',
    documentType: '',
    fields: [],
  },
)

const classifications = [...new Set(specialtiesCodes.map((s) => s.classification))]
  .sort()
  .map((c) => ({ label: c, value: c }))
const specializations = ref<{ label: string; value: string }[]>([])
const selectedClassification = ref('')
const selectedSpecialization = ref('')

watch(selectedClassification, (newVal) => {
  specializations.value = specialtiesCodes
    .filter((s) => s.classification === newVal && s.specialization)
    .map((s) => ({ label: s.specialization!, value: s.specialization! }))
})

const documentTypes = [
  'Care Plan',
  'Consultation Note',
  'Continuity of Care Document (CCD)',
  'Discharge Summary',
  'History and Physical (H&P)',
  'Operative Note',
  'Procedure Note',
  'Progress Note',
  'Referral Note',
  'Transfer Summary',
  'Unstructured Document',
]

const customSections = ref<{ name: string; content: string; required: boolean }[]>([])

const selectedDocumentType = ref('')
const sectionTexts = ref<Record<string, string>>({})
const selectedOptionalSections = ref<string[]>([])
const selectedRequiredAlternatives = ref<Record<number, string>>({})

const locations = ref<SelectListItem[]>([])
const selectedLocations = ref<string[]>([])

const selectedFields = ref<NoteTemplateFieldResponse[]>([])

const requiredSections = computed(() =>
  selectedDocumentType.value
    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ((documentSectionsMap as any)[selectedDocumentType.value]?.required ?? [])
    : [],
)

const recommendedSections = computed(() =>
  selectedDocumentType.value
    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ((documentSectionsMap as any)[selectedDocumentType.value]?.recommended ?? [])
    : [],
)

const additionalSections = computed(() =>
  selectedDocumentType.value
    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ((documentSectionsMap as any)[selectedDocumentType.value]?.additional ?? [])
    : [],
)

watch(selectedDocumentType, (newVal) => {
  if (newVal && !skipFieldsReset.value) {
    selectedFields.value = []
    requiredSections.value.forEach((section: string | string[]) => {
      if (Array.isArray(section)) {
        selectedFields.value.push({
          name: section[0],
          value: '',
          isRequired: true,
        })
      } else {
        selectedFields.value.push({
          name: section,
          value: '',
          isRequired: true,
        })
      }
    })
  }
})

const goToNoteTemplatesList = async () => {
  await router.push({ name: 'note-templates' })
}

const loadNoteTemplate = async (id: string) => {
  try {
    const response = await api.noteTemplates.noteTemplateGetNoteTemplate(id)
    const data = response.data
    selectedFields.value = data.fields || []

    // Set basic form values
    setValues({
      name: data.name,
      classification: data.classification,
      specialization: data.specialization,
      documentType: data.documentType,
      locations: data.locations?.map((l) => l.id) || [],
    })

    // Set reactive variables
    selectedClassification.value = data.classification || ''
    selectedSpecialization.value = data.specialization || ''
    selectedDocumentType.value = data.documentType || ''
    selectedLocations.value = data.locations?.map((l) => l.id!) || []

    // Process fields to populate sections
    if (data.fields) {
      // Clear existing data
      sectionTexts.value = {}
      selectedOptionalSections.value = []
      selectedRequiredAlternatives.value = {}
      customSections.value = []

      // Categorize fields
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const docSections = data.documentType ? (documentSectionsMap as any)[data.documentType] : null
      const standardSections = new Set([
        ...(docSections?.required?.flat(2) || []),
        ...(docSections?.recommended || []),
        ...(docSections?.additional || []),
      ])

      data.fields.forEach((field) => {
        if (field.name) {
          sectionTexts.value[field.name] = field.value || ''

          if (standardSections.has(field.name) && !field.isRequired) {
            selectedOptionalSections.value.push(field.name)
          } else if (!standardSections.has(field.name)) {
            // Custom section
            customSections.value.push({
              name: field.name,
              content: field.value || '',
              required: field.isRequired || false,
            })
          }
        }
      })
    }

    // Store the ID for updates
    noteTemplate.value.id = data.id
  } catch (error) {
    console.error('Error loading note template:', error)
    toast.error('Failed to load note template')
  }
}

const submitForm = handleSubmit(async (values) => {
  try {
    const matchedSpecialty =
      specialtiesCodes.find(
        (s) =>
          s.classification === values.classification && s.specialization === values.specialization,
      ) ??
      specialtiesCodes.find((s) => s.classification === values.classification && !s.specialization)
    const specialityCode = matchedSpecialty?.code

    if (isEditMode.value && noteTemplate.value.id) {
      // Update existing note template
      const payload: UpdateNoteTemplateCommand = {
        id: noteTemplate.value.id,
        name: values.name,
        classification: values.classification,
        specialization: values.specialization,
        specialityCode,
        documentType: values.documentType,
        fields: selectedFields.value,
        locationIds: values.locations ? values.locations : [],
      }
      await api.noteTemplates.noteTemplateUpdateNoteTemplate(payload)
    } else {
      // Create new note template
      const payload: CreateNoteTemplateCommand = {
        name: values.name,
        classification: values.classification,
        specialization: values.specialization,
        specialityCode,
        documentType: values.documentType,
        fields: selectedFields.value,
        locationIds: values.locations ? values.locations : [],
      }
      await api.noteTemplates.noteTemplateCreateNoteTemplate(payload)
    }

    toast.success(
      isEditMode.value
        ? 'Note template updated successfully'
        : 'Note template created successfully',
    )
    await goToNoteTemplatesList()
  } catch (error) {
    console.error('Error submitting form:', error)
    toast.error('An error occurred while saving.')
  }
})

const fetchLocations = async () => {
  locations.value = (await api.locations.locationListLocationsLookup()).data
}

watchEffect(async () => {
  if (route.params.id) {
    isEditMode.value = true
    await loadNoteTemplate(route.params.id as string)
  }
  if (isOrganizationAdmin.value) {
    await fetchLocations()
  }
  skipFieldsReset.value = false
})
</script>
