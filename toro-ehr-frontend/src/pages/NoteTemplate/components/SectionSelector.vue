<template>
  <div class="space-y-6">
    <!-- Required Sections Configuration Modal -->
    <div
      v-if="showRequiredConfig"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <h4 class="font-semibold mb-4">
          Configure Required Section: {{ currentRequiredSection.label }}
        </h4>

        <!-- Case 2: Array of alternatives -->
        <div
          v-if="
            Array.isArray(currentRequiredSection.section) &&
            !Array.isArray(currentRequiredSection.section[1])
          "
        >
          <label class="font-medium block mb-2"
            >Select one: {{ currentRequiredSection.section.join(' / ') }}</label
          >
          <div class="flex flex-col gap-2">
            <div
              v-for="option in currentRequiredSection.section"
              :key="option"
              class="flex items-center gap-2"
            >
              <RadioButton
                :name="'required-alt-' + currentRequiredSection.index"
                :value="option"
                v-model="selectedOption"
                :inputId="'alt-' + currentRequiredSection.index + '-' + option"
              />
              <label :for="'alt-' + currentRequiredSection.index + '-' + option">{{
                option
              }}</label>
            </div>
          </div>
        </div>

        <!-- Case 3: One or both required group -->
        <div v-else-if="Array.isArray(currentRequiredSection.section[1])">
          <label class="font-medium block mb-2">
            Select one: {{ currentRequiredSection.section[0] }} or both
            {{ currentRequiredSection.section[1].join(' + ') }}
          </label>
          <div class="flex flex-col gap-2">
            <div class="flex items-center gap-2">
              <RadioButton
                :name="'required-combo-' + currentRequiredSection.index"
                :value="currentRequiredSection.section[0]"
                v-model="selectedOption"
                :inputId="'combo-' + currentRequiredSection.index + '-0'"
              />
              <label :for="'combo-' + currentRequiredSection.index + '-0'">{{
                currentRequiredSection.section[0]
              }}</label>
            </div>
            <div class="flex items-center gap-2">
              <RadioButton
                :name="'required-combo-' + currentRequiredSection.index"
                value="both"
                v-model="selectedOption"
                :inputId="'combo-' + currentRequiredSection.index + '-both'"
              />
              <label :for="'combo-' + currentRequiredSection.index + '-both'"
                >Both {{ currentRequiredSection.section[1].join(' + ') }}</label
              >
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 mt-4">
          <button
            @click="showRequiredConfig = false"
            class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="saveRequiredConfig"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Save
          </button>
        </div>
      </div>
    </div>

    <!-- Two-List Section Selection -->
    <div>
      <h4 class="font-semibold mb-4">Section Selection</h4>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Available Sections (Left List) -->
        <div class="border rounded-lg p-4">
          <h5 class="font-medium mb-3 text-gray-700">Available Sections</h5>
          <div class="space-y-2 min-h-[600px] max-h-[800px] overflow-y-auto">
            <div v-if="availableRecommendedSections.length > 0">
              <h6 class="text-sm font-medium text-blue-600 mb-2">Recommended</h6>
              <div
                v-for="section in availableRecommendedSections"
                :key="section"
                class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded cursor-pointer hover:bg-blue-100 transition-colors"
                @click="selectSection(section)"
              >
                <span class="text-sm font-medium">{{ section }}</span>
                <i class="pi pi-arrow-right text-blue-600"></i>
              </div>
            </div>

            <div v-if="availableAdditionalSections.length > 0" class="mt-4">
              <h6 class="text-sm font-medium text-gray-600 mb-2">Additional</h6>
              <div
                v-for="section in availableAdditionalSections"
                :key="section"
                class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded cursor-pointer hover:bg-gray-100 transition-colors"
                @click="selectSection(section)"
              >
                <span class="text-sm font-medium">{{ section }}</span>
                <i class="pi pi-arrow-right text-gray-600"></i>
              </div>
            </div>

            <div
              v-if="
                availableRecommendedSections.length === 0 &&
                availableAdditionalSections.length === 0
              "
              class="text-center text-gray-500 py-8"
            >
              All optional sections selected
            </div>
          </div>
        </div>

        <!-- Selected Sections (Right List) -->
        <div class="border rounded-lg p-4">
          <h5 class="font-medium mb-3 text-gray-700">Selected Sections</h5>
          <div class="space-y-3 min-h-[600px] max-h-[800px] overflow-y-auto">
            <!-- Render sections in order -->
            <div v-for="(field, index) in orderedFields" :key="index" class="relative">
              <!-- Required sections -->
              <div v-if="getSelectedRequiredSectionNames().includes(field.name!)" class="relative">
                <div class="flex items-center justify-between p-3 border rounded">
                  <div class="flex items-center gap-2">
                    <i class="pi pi-lock text-red-600 text-xs"></i>
                    <span class="text-sm font-medium">{{ field.name }}</span>
                    <span class="text-xs text-red-600">(Required)</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <Button
                      @click="moveSectionUp(index)"
                      :disabled="index === 0"
                      icon="pi pi-chevron-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move up"
                    />
                    <Button
                      @click="moveSectionDown(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-chevron-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move down"
                    />
                    <Button
                      @click="moveSectionToTop(index)"
                      :disabled="index === 0"
                      icon="pi pi-angle-double-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move to top"
                    />
                    <Button
                      @click="moveSectionToBottom(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-angle-double-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move to bottom"
                    />
                    <i
                      v-if="hasAlternatives(field.name!)"
                      class="pi pi-cog text-red-600 cursor-pointer hover:text-red-800 ml-2"
                      @click="configureRequiredSection(field)"
                      title="Configure alternatives"
                    ></i>
                  </div>
                </div>
                <Textarea
                  v-model="field.value"
                  autoResize
                  class="w-full mt-2"
                  placeholder="Enter content for this section..."
                />
              </div>

              <!-- Optional sections -->
              <div
                v-else-if="
                  recommendedSections.includes(field.name!) ||
                  additionalSections.includes(field.name!)
                "
                class="relative"
              >
                <div class="flex items-center justify-between p-3 border rounded">
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium">{{ field.name }}</span>
                    <span class="text-xs text-green-600">(Optional)</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <Button
                      @click="moveSectionUp(index)"
                      :disabled="index === 0"
                      icon="pi pi-chevron-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move up"
                    />
                    <Button
                      @click="moveSectionDown(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-chevron-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move down"
                    />
                    <Button
                      @click="moveSectionToTop(index)"
                      :disabled="index === 0"
                      icon="pi pi-angle-double-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move to top"
                    />
                    <Button
                      @click="moveSectionToBottom(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-angle-double-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move to bottom"
                    />
                    <i
                      class="pi pi-times text-red-500 cursor-pointer hover:text-red-700 ml-2"
                      @click="removeField(index)"
                    ></i>
                  </div>
                </div>
                <Textarea
                  v-model="field.value"
                  autoResize
                  class="w-full mt-2"
                  placeholder="Enter content for this section..."
                />
              </div>

              <!-- Custom sections -->
              <div v-else class="relative">
                <div class="flex items-center justify-between p-3 border rounded">
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-medium">{{
                      field.name || 'Unnamed Custom Section'
                    }}</span>
                    <span class="text-xs text-purple-600">(Custom)</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <Button
                      @click="moveSectionUp(index)"
                      :disabled="index === 0"
                      icon="pi pi-chevron-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move up"
                    />
                    <Button
                      @click="moveSectionDown(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-chevron-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move down"
                    />
                    <Button
                      @click="moveSectionToTop(index)"
                      :disabled="index === 0"
                      icon="pi pi-angle-double-up"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === 0 }"
                      title="Move to top"
                    />
                    <Button
                      @click="moveSectionToBottom(index)"
                      :disabled="index === orderedFields.length - 1"
                      icon="pi pi-angle-double-down"
                      size="small"
                      text
                      :class="{ 'opacity-50': index === orderedFields.length - 1 }"
                      title="Move to bottom"
                    />
                    <i
                      class="pi pi-times text-red-500 cursor-pointer hover:text-red-700 ml-2"
                      @click="removeField(index)"
                    ></i>
                  </div>
                </div>

                <!-- Custom section configuration -->
                <div class="mt-2 p-3 bg-purple-25 border border-purple-100 rounded">
                  <div class="flex flex-col sm:flex-row gap-4 mb-2">
                    <div class="w-full sm:w-1/3">
                      <label class="block text-sm font-medium mb-1"
                        >Field name <span class="text-red-500">*</span></label
                      >
                      <input
                        v-model="field.name"
                        type="text"
                        :class="[
                          'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 border-gray-300 focus:ring-purple-500',
                        ]"
                        placeholder="Enter field name"
                        required
                      />
                    </div>
                    <div class="flex items-center gap-2">
                      <input
                        v-model="field.isRequired"
                        type="checkbox"
                        :id="'custom-' + index"
                        class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <label :for="'custom-' + index" class="text-sm">Required</label>
                    </div>
                  </div>

                  <Textarea
                    v-model="field.value"
                    autoResize
                    class="w-full"
                    rows="3"
                    placeholder="Section Content"
                  />
                </div>
              </div>
            </div>

            <!-- Add custom section button -->
            <div class="flex justify-center pt-4">
              <button
                @click="addCustomSection"
                class="flex items-center gap-2 px-4 py-2 text-purple-600 border border-purple-300 rounded-md hover:bg-purple-50 transition-colors"
              >
                <i class="pi pi-plus-circle"></i>
                <span class="font-medium">Add Custom Section</span>
              </button>
            </div>

            <div v-if="orderedFields.length === 0" class="text-center text-gray-500 py-8">
              No sections selected
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import Textarea from 'primevue/textarea'
import RadioButton from 'primevue/radiobutton'
import Button from 'primevue/button'
import type { NoteTemplateFieldResponse } from '@/api/api-reference'

interface Props {
  requiredSections: (string | string[] | [string, string[]])[]
  recommendedSections: string[]
  additionalSections: string[]
  selectedFields: NoteTemplateFieldResponse[]
}

interface Emits {
  (e: 'update:selected-fields', value: NoteTemplateFieldResponse[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const orderedFields = ref<NoteTemplateFieldResponse[]>([...props.selectedFields])
const selectedRequiredField = ref<NoteTemplateFieldResponse>()
const selectedOption = ref('')

// Modal state for required section configuration
const showRequiredConfig = ref(false)
const currentRequiredSection = ref<{
  section: any
  index: number
  label: string
}>({ section: null, index: -1, label: '' })

// Section ordering functions
const moveSectionUp = (index: number) => {
  if (index <= 0 || index >= orderedFields.value.length) return
  ;[orderedFields.value[index - 1], orderedFields.value[index]] = [
    orderedFields.value[index],
    orderedFields.value[index - 1],
  ]
  emit('update:selected-fields', orderedFields.value)
}

const moveSectionDown = (index: number) => {
  if (index < 0 || index >= orderedFields.value.length) return
  ;[orderedFields.value[index], orderedFields.value[index + 1]] = [
    orderedFields.value[index + 1],
    orderedFields.value[index],
  ]
  emit('update:selected-fields', orderedFields.value)
}

const moveSectionToTop = (index: number) => {
  if (index <= 0 || index >= orderedFields.value.length) return
  const [item] = orderedFields.value.splice(index, 1)
  orderedFields.value.unshift(item)
  emit('update:selected-fields', orderedFields.value)
}

const moveSectionToBottom = (index: number) => {
  if (index < 0 || index >= orderedFields.value.length) return
  const [item] = orderedFields.value.splice(index, 1)
  orderedFields.value.push(item)
  emit('update:selected-fields', orderedFields.value)
}

// Get names of selected required sections
const getSelectedRequiredSectionNames = (): string[] => {
  const names: string[] = []

  props.requiredSections.forEach((section) => {
    if (typeof section === 'string') {
      names.push(section)
    } else if (Array.isArray(section)) {
      section.forEach((s) => {
        if (typeof s === 'string') {
          names.push(s)
        } else if (Array.isArray(s)) {
          names.push(...s)
        }
      })
    }
  })

  return names
}

// Check if a section has alternatives
const hasAlternatives = (sectionName: string): boolean => {
  return props.requiredSections.some((section, index) => {
    if (Array.isArray(section)) {
      if (Array.isArray(section[1])) {
        return section[0] === sectionName || section[1].includes(sectionName)
      } else {
        return section.includes(sectionName)
      }
    }
    return false
  })
}

const availableRecommendedSections = computed(() => {
  const selectedRequired = getSelectedRequiredSectionNames()
  return props.recommendedSections.filter(
    (section) =>
      !orderedFields.value.some((f) => f.name === section) && !selectedRequired.includes(section),
  )
})

const availableAdditionalSections = computed(() => {
  const selectedRequired = getSelectedRequiredSectionNames()
  return props.additionalSections.filter(
    (section) =>
      !orderedFields.value.some((f) => f.name === section) && !selectedRequired.includes(section),
  )
})

// Section management
const selectSection = (section: string) => {
  orderedFields.value.push({
    name: section,
    value: '',
    isRequired: false,
  })
  emit('update:selected-fields', orderedFields.value)
}

const removeField = (index: number) => {
  if (index < 0 || index >= orderedFields.value.length) return
  orderedFields.value.splice(index, 1)
  emit('update:selected-fields', orderedFields.value)
}

const addCustomSection = () => {
  orderedFields.value.push({
    name: '',
    value: '',
    isRequired: false,
  })
  emit('update:selected-fields', orderedFields.value)
}

const configureRequiredSection = (field: NoteTemplateFieldResponse) => {
  // Find the original required section definition
  selectedRequiredField.value = field
  const section = props.requiredSections
    .filter((s) => Array.isArray(s))
    .find((s) => Array.isArray(s[1]) && s[1].includes(field.name!))
  if (!section) {
    selectedOption.value = field.name!
  } else {
    selectedOption.value = 'both'
  }
  props.requiredSections.forEach((section, index) => {
    if (Array.isArray(section)) {
      if (Array.isArray(section[1])) {
        if (section[0] === field.name || section[1].includes(field.name!)) {
          currentRequiredSection.value = {
            section,
            index,
            label: field.name!,
          }
          showRequiredConfig.value = true
        }
      } else {
        if (section.includes(field.name!)) {
          currentRequiredSection.value = {
            section,
            index,
            label: field.name!,
          }
          showRequiredConfig.value = true
        }
      }
    }
  })
}

const saveRequiredConfig = () => {
  if (!selectedRequiredField.value) return
  const section = props.requiredSections
    .filter((s) => Array.isArray(s))
    .find(
      (s) =>
        s[0].includes(selectedRequiredField.value!.name!) ||
        s[1].includes(selectedRequiredField.value!.name!),
    )
  if (!section) return

  if (Array.isArray(section![1])) {
    if (selectedOption.value === 'both') {
      //check if both are already selected
      if (!section[1].includes(selectedRequiredField.value!.name!)) {
        const index = orderedFields.value.findIndex(
          (item) => item.name === selectedRequiredField.value!.name!,
        )
        if (index > -1) {
          if (Array.isArray(section![1])) {
            const newItems = section![1].map((s: string) => ({
              name: s,
              value: '',
              isRequired: true,
            }))
            orderedFields.value.splice(index, 1, ...newItems)
          }
        }
      }
    } else {
      if (section[1].includes(selectedRequiredField.value!.name!)) {
        const newItem = {
          name: selectedOption.value,
          value: '',
          isRequired: true,
        }
        const item1index = orderedFields.value.findIndex((item) => item.name === section[1][0])
        if (section[1][0] === selectedRequiredField.value!.name!) {
          orderedFields.value.splice(item1index, 1, newItem)
        } else {
          orderedFields.value.splice(item1index, 1)
        }

        const item2index = orderedFields.value.findIndex((item) => item.name === section[1][1])
        if (section[1][1] === selectedRequiredField.value!.name!) {
          orderedFields.value.splice(item2index, 1, newItem)
        } else {
          orderedFields.value.splice(item2index, 1)
        }
      }
    }
  } else {
    if (selectedOption.value !== selectedRequiredField.value.name) {
      selectedRequiredField.value.name = selectedOption.value
    }
  }
  selectedRequiredField.value = undefined
  selectedOption.value = ''
  showRequiredConfig.value = false
  emit('update:selected-fields', orderedFields.value)
}

watch(
  () => props.selectedFields,
  (newVal) => {
    orderedFields.value = [...newVal]
  },
)
</script>
