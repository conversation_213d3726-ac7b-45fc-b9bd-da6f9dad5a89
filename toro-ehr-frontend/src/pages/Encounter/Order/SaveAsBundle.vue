<template>
  <Teleport to="body">
    <div
      v-if="isModalOpen"
      class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
    >
      <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
        <h2 class="text-lg font-bold mb-4">Save as Bundle</h2>

        <!-- Form -->
        <form @submit.prevent="saveAsBundle">
          <div class="px-2">
            <InputText id="name" label="Name" />
          </div>
          <div class="px-2">
            <Select id="priority" label="Priority" :options="priorities" />
          </div>
          <div class="flex justify-end">
            <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
              Cancel
            </button>
            <button type="submit" class="bg-primary text-white px-4 py-2 rounded mr-2">Save</button>
          </div>
        </form>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import { useEncounterStore } from '@/stores/encounter'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import { getPriorities } from '@/utils/priority'
import * as yup from 'yup'

const encounterStore = useEncounterStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  selectedOrderIds: string[]
}>()

const priorities = getPriorities()

const initialValues = {
  name: '',
  priority: 'Routine',
}

const schema = yup.object({
  name: yup.string().required('Name is required'),
  priority: yup.string().required('Priority is required'),
})

const { handleSubmit } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const saveAsBundle = handleSubmit(async (values) => {
  try {
    await api.encounter.encounterSaveOrdersAsBundle(encounterStore.selectedEncounter!.id!, {
      orderIds: props.selectedOrderIds,
      name: values.name,
      priority: values.priority,
      encounterId: encounterStore.selectedEncounter!.id!,
    })

    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
