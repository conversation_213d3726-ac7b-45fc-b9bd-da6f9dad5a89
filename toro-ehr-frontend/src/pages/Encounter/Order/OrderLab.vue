<template>
  <Select id="specimen" label="Specimen" :options="specimenTypes" />
  <InputText id="note" label="Notes" />
  <Select id="fasting" label="Fasting" :options="yesNoOptions" />
  <Select id="repeat" label="Repeat" :options="yesNoOptions" />
  <Select id="priority" label="Priority" :options="priorities" />
  <div class="flex justify-end">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
      Cancel
    </button>
    <Button
      icon="pi pi-plus"
      label="Add to queue"
      @click="addToQueue"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { onMounted, ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter'
import { api } from '@/api'
import type { LabOrderResponse, SearchOrderEntryResponse } from '@/api/api-reference'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'
import { getSpecimenTypes } from '@/utils/specimen'
import { getPriorities } from '../../../utils/priority'

const encounterStore = useEncounterStore()

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
}>()

const emits = defineEmits(['close'])

const specimenTypes = getSpecimenTypes()
const priorities = getPriorities()

const yesNoOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const initialValues = {
  specimen: 'BLD',
  note: '',
  fasting: false,
  repeat: false,
  priority: 'Routine',
}

const schema = yup.object({
  specimen: yup.string().required('Specimen is required'),
  note: yup.string().nullable(),
  fasting: yup.bool().required('Fasting is required'),
  repeat: yup.bool().required('Repeat is required'),
  priority: yup.string().required('Priority is required'),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const addToQueue = handleSubmit(async (values) => {
  try {
    let orderId
    if (encounterStore.editingOrder) {
      orderId = encounterStore.editingOrder.id
      await api.encounter.encounterEditOrderLab(encounterStore.selectedEncounter!.id!, {
        ...values,
        id: orderId,
        loincCodeId: props.selectedOrderEntry!.id!,
        bundleId: encounterStore.editingBundleId,
      })
    } else {
      const response = await api.encounter.encounterCreateOrderLab(
        encounterStore.selectedEncounter!.id!,
        {
          ...values,
          loincCodeId: props.selectedOrderEntry!.id!,
          encounterId: encounterStore.selectedEncounter!.id!,
          patientId: encounterStore.selectedEncounter!.patientId!,
        },
      )
      orderId = response.data
    }
    await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    resetForm()
    encounterStore.setEditingOrder(null, null)
    emits('close', orderId)
  } catch (error) {
    console.log(error)
  }
})

onMounted(async () => {
  if (encounterStore.editingOrder) {
    const labEditingOrder = encounterStore.editingOrder as LabOrderResponse

    setValues({
      ...labEditingOrder,
      note: labEditingOrder.note ?? '',
    })
  }
})

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      const labEditingOrder = newValue as LabOrderResponse

      setValues({
        ...labEditingOrder,
        note: labEditingOrder.note ?? '',
      })
    }
  },
)
</script>
