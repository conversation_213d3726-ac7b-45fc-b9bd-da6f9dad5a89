<template>
  <ResizableBox title="ORDERS" type="orders">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <EditOrder :is-visible="editOrderOpen" @close="editOrderOpen = false" />
            <MedicationAdministrationRecord
              v-if="marOpen"
              :order-id="selectedRow?.id"
              :bundle-id="currentBundleId"
              :is-visible="marOpen"
              @close="closeMar"
            />
            <div
              v-if="!editOrderOpen && !marOpen && !isPatientView"
              class="bg-white border border-gray-200 rounded-xl shadow-sm"
            >
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <h3 class="font-semibold tracking-wide text-gray-800">Queue</h3>
                  </div>
                </template>
                <template #buttons>
                  <Button
                    icon="pi pi-plus"
                    label="Order entry"
                    @click="editOrderOpen = true"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                  />
                </template>
              </TableHeader>
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Type
                        </span>
                      </div>
                    </th>

                    <th scope="col" class="ps-6 py-3 text-start max-w-xs">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Name
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Priority
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Status
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Timing
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-end">
                      <div class="flex items-center gap-x-2"></div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <template v-for="(row, index) in queuedOrders" :key="row.id">
                    <tr class="relative hover:bg-gray-100 cursor-pointer">
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{ getTypeLabel(row) }}</span>
                        </div>
                      </td>

                      <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                        <div class="flex px-6 py-3">
                          <span class="block text-sm text-gray-800 truncate" v-tooltip="row.name">{{
                            row.name
                          }}</span>
                          <button
                            v-if="row.orderType == 'Bundle'"
                            class="ml-2 focus:outline-none transition-transform"
                            @click.stop="toggleRow(index)"
                          >
                            <ChevronDownIcon
                              class="w-5 h-5 text-gray-500 transform transition-transform duration-300"
                              :class="{ 'rotate-180': expandedRows.has(index) }"
                            />
                          </button>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span
                            v-if="row.orderType != 'Bundle'"
                            class="block text-sm text-gray-800"
                          >
                            {{ row.priority }}
                          </span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span
                            v-if="row.orderType != 'Bundle'"
                            class="block text-sm text-gray-800"
                          >
                            {{ row.status }}
                          </span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">
                            {{ getTimingLabel(row) }}
                          </span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">
                            <Button
                              icon="pi pi-ellipsis-v"
                              variant="link"
                              @click="(e) => toggleMenu(e, row, undefined)"
                              class="p-button-danger"
                            />
                          </span>
                        </div>
                      </td>
                    </tr>
                    <template v-if="row.orderType == 'Bundle' && expandedRows.has(index)">
                      <tr
                        v-for="order in row.orders"
                        :key="order.id"
                        class="relative hover:bg-gray-100 cursor-pointer"
                      >
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800 pl-6">{{
                              getTypeLabel(order)
                            }}</span>
                          </div>
                        </td>

                        <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                          <div class="px-6 py-3">
                            <span
                              class="block text-sm text-gray-800 truncate"
                              v-tooltip="order.name"
                              >{{ order.name }}</span
                            >
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span
                              v-if="order.orderType != 'Bundle'"
                              class="block text-sm text-gray-800"
                            >
                              {{ order.priority }}
                            </span>
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">
                              {{ order.status }}
                            </span>
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">
                              {{ getTimingLabel(order) }}
                            </span>
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">
                              <Button
                                icon="pi pi-ellipsis-v"
                                variant="link"
                                @click="(e) => toggleMenu(e, order, row.id)"
                                class="p-button-danger"
                              />
                            </span>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </template>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
            <div
              v-if="!editOrderOpen && !marOpen"
              class="bg-white border border-gray-200 rounded-xl shadow-sm mt-1"
            >
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <h3 class="font-semibold tracking-wide text-gray-800">Past</h3>
                  </div>
                </template>
              </TableHeader>
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr class="bg-gray-50">
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Date
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          End Time
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start max-w-xs">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Order name
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Status
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-end">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Actions
                        </span>
                      </div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <template v-for="(row, index) in finishedOrders" :key="row.id">
                    <tr class="relative hover:bg-gray-100 cursor-pointer">
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{
                            formatDateWithoutTime(row.completedAt)
                          }}</span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{
                            formatTime(row.completedAt)
                          }}</span>
                        </div>
                      </td>
                      <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                        <div class="flex px-6 py-3">
                          <span class="block text-sm text-gray-800 truncate" v-tooltip="row.name">{{
                            row.name
                          }}</span>
                          <button
                            v-if="row.orderType == 'Bundle'"
                            class="ml-2 focus:outline-none transition-transform"
                            @click.stop="toggleFinishedRow(index)"
                          >
                            <ChevronDownIcon
                              class="w-5 h-5 text-gray-500 transform transition-transform duration-300"
                              :class="{ 'rotate-180': expandedFinishedRows.has(index) }"
                            />
                          </button>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">
                            {{ row.status }}
                          </span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800"> </span>
                        </div>
                      </td>
                    </tr>
                    <template v-if="row.orderType == 'Bundle' && expandedFinishedRows.has(index)">
                      <tr
                        v-for="order in row.orders"
                        :key="order.id"
                        class="relative hover:bg-gray-100 cursor-pointer"
                      >
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">{{
                              formatDateWithoutTime(order.completedAt)
                            }}</span>
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">{{
                              formatTime(order.completedAt)
                            }}</span>
                          </div>
                        </td>
                        <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                          <div class="px-6 py-3">
                            <span
                              class="block text-sm text-gray-800 truncate"
                              v-tooltip="order.name"
                              >{{ order.name }}</span
                            >
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800">
                              {{ order.status }}
                            </span>
                          </div>
                        </td>
                        <td class="h-px w-72 whitespace-nowrap">
                          <div class="px-6 py-3">
                            <span class="block text-sm text-gray-800"> </span>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </template>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </ResizableBox>
  <Menu ref="menuRef" :model="menuItems" popup />
</template>
<script setup lang="ts">
import ResizableBox from '../../../components/resizable/ResizableBox.vue'
import { computed, ref } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import TableHeader from '@/components/table/TableHeader.vue'
import Button from 'primevue/button'
import Menu from 'primevue/menu'
import EditOrder from './EditOrder.vue'
import type { OrderResponse } from '@/api/api-reference.ts'
import { api } from '@/api'
import MedicationAdministrationRecord from './MedicationAdministrationRecord.vue'
import { formatDateWithoutTime, formatTime } from '@/utils/timeMethods.ts'
import type { MenuItem } from 'primevue/menuitem'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'
import { useRoute } from 'vue-router'

const encounterStore = useEncounterStore()
const route = useRoute()
const isPatientView = computed(() => route.name === 'patient')

const editOrderOpen = ref(false)
const marOpen = ref(false)
const menuItems = ref<MenuItem[]>([])
const queuedOrders = computed(() =>
  encounterStore.encounterOrders.filter(
    (order) => order.status === 'Active' || order.status === 'OnHold' || order.status === 'Draft',
  ),
)
const finishedOrders = computed(() =>
  encounterStore.encounterOrders.filter(
    (order) =>
      order.status === 'Completed' ||
      order.status === 'Revoked' ||
      order.status === 'EnteredInError',
  ),
)

const expandedRows = ref(new Set<number>())
function toggleRow(index: number) {
  if (expandedRows.value.has(index)) {
    expandedRows.value.delete(index) // close this one
  } else {
    expandedRows.value.add(index) // open this one
  }
}

const expandedFinishedRows = ref(new Set<number>())
function toggleFinishedRow(index: number) {
  if (expandedFinishedRows.value.has(index)) {
    expandedFinishedRows.value.delete(index) // close this one
  } else {
    expandedFinishedRows.value.add(index) // open this one
  }
}

const editOrder = () => {
  editOrderOpen.value = true
  if (currentRow.value) {
    selectedRow.value = currentRow.value
    encounterStore.setEditingOrder(currentRow.value, currentBundleId.value)
  }
}

const openMar = () => {
  marOpen.value = true
  if (currentRow.value) {
    selectedRow.value = currentRow.value
  }
}

const closeMar = () => {
  marOpen.value = false
  currentRow.value = null
  currentBundleId.value = null
  selectedRow.value = null
}

const changeStatus = async (status: string) => {
  await api.encounter.encounterChangeOrderStatus(encounterStore.selectedEncounter!.id!, {
    orderIds: [currentRow.value!.id!],
    status: status,
    bundleId: currentBundleId.value,
  })
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)
}

const changeTimingStatus = async (status: string) => {
  await api.encounter.encounterChangeOrderTimingStatus(encounterStore.selectedEncounter!.id!, {
    orderIds: [currentRow.value!.id!],
    status,
  })
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)
}

const getTimingLabel = (row: OrderResponse) => {
  if (row.orderType == 'Med') {
    switch (row.timingStatus) {
      case 'InProgress':
        return 'In progress'
      case 'Hold':
        return 'On hold'
      case 'NotDone':
        return 'Not Done'
      case 'Administered':
        return 'Administered'
      default: {
        const date = new Date(row.startTime!)
        const now = new Date()
        const difference = (now.getTime() - date.getTime()) / (1000 * 60) // Difference in minutes
        if (difference > 30) {
          return 'Overdue'
        } else if (difference < 30 && difference > -30) {
          return 'Due now'
        } else {
          return 'Due soon'
        }
      }
    }
  }
}

const getTypeLabel = (row: OrderResponse) => {
  switch (row.orderType) {
    case 'Med':
      return 'medicine'
    case 'Lab':
      return 'lab'
    case 'Procedure':
      return 'procedure'
    case 'Bundle':
      return 'bundle'
  }
}

const menuRef = ref()
const currentRow = ref<OrderResponse | null>(null)
const currentBundleId = ref<string | null>(null)
const selectedRow = ref<OrderResponse | null>(null)

const toggleMenu = (event: MouseEvent, row: OrderResponse, bundleId: string | undefined) => {
  menuItems.value = getActionMap(row)
  currentRow.value = row
  currentBundleId.value = bundleId ?? null
  menuRef.value.toggle(event)
}

const getActionMap = (row: OrderResponse) => {
  const actions = actionMap[row.orderType]
  if (row.status == 'Draft' && !actions.some((x) => x.label === 'View/Edit')) {
    actions.unshift({
      label: 'View/Edit',
      command: () => {
        editOrder()
      },
    })
  }
  return actions
}

const actionMap = {
  Med: [
    {
      label: 'Mark adminstered',
      command: () => {
        changeTimingStatus('Administered')
      },
    },
    {
      label: 'Mark in-progress',
      command: () => {
        changeTimingStatus('InProgress')
      },
    },
    {
      label: 'Hold',
      command: () => {
        changeTimingStatus('Hold')
      },
    },
    {
      label: 'Not Done',
      command: () => {
        changeTimingStatus('NotDone')
      },
    },
    {
      label: 'View MAR',
      command: () => {
        openMar()
      },
    },
    {
      label: 'Mark completed',
      command: () => {
        changeStatus('Completed')
      },
    },
    {
      label: 'Cancel',
      command: () => {
        changeStatus('Revoked')
      },
    },
  ],
  Lab: [
    {
      label: 'Mark completed',
      command: () => {
        changeStatus('Completed')
      },
    },
    {
      label: 'Cancel',
      command: () => {
        changeStatus('Revoked')
      },
    },
  ],
  Procedure: [
    {
      label: 'Mark completed',
      command: () => {
        changeStatus('Completed')
      },
    },
    {
      label: 'Cancel',
      command: () => {
        changeStatus('Revoked')
      },
    },
  ],
  Bundle: [
    {
      label: 'Cancel',
      command: () => {
        changeStatus('Revoked')
      },
    },
  ],
}
</script>
