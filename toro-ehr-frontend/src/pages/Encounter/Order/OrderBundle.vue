<template>
  <h2 class="uppercase text-lg font-semibold text-gray-800 mb-4">Bundle settings</h2>
  <Select id="priority" label="Priority" :options="priorities" v-model="priority" />
  <DateTimePicker
    v-if="isStartTimeVisible"
    id="startTime"
    label="Start time"
    showTime
    hourFormat="12"
    :stepMinute="15"
    v-model="startTime"
  />
  <Select id="fasting" label="Fasting" :options="yesNoOptions" v-model="fasting" />
  <Select id="repeat" label="Repeat" :options="yesNoOptions" v-model="repeat" />
  <InputText id="note" label="Notes" v-model="note" @change="editBundle" />
  <h2 class="uppercase text-lg font-semibold text-gray-800 mb-4">Individual items in bundle</h2>

  <div v-for="order in editingBundle.orders" :key="order.id" class="mb-2">
    <div
      v-if="order.orderType === 'Med'"
      class="p-4 bg-gray-50 rounded-xl shadow-sm border border-gray-200"
    >
      <h3 class="text-base font-bold text-gray-900 mb-3">{{ order.name }}</h3>

      <div class="space-y-2">
        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Frequency:</label>
          <p class="text-sm text-gray-900 font-semibold">
            {{
              order.frequency !== 'Custom'
                ? getFrequencyLabel(order.frequency)
                : order.customFrequency
            }}
          </p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Duration:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ getDurationLabel(order.duration) }}</p>
        </div>

        <div v-if="order.prn" class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">PRN Reason:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.prnReason }}</p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Start time:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ formatDate(order.startTime) }}</p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Priority:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.priority }}</p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Instructions:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.instructions }}</p>
        </div>
      </div>
    </div>
    <div
      v-if="order.orderType === 'Lab'"
      class="p-4 bg-gray-50 rounded-xl shadow-sm border border-gray-200"
    >
      <h3 class="text-base font-bold text-gray-900 mb-3">{{ order.name }}</h3>

      <div class="space-y-2">
        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Specimen:</label>
          <p class="text-sm text-gray-900 font-semibold">
            {{ getSpecimenLabel(order.specimen) }}
          </p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Notes:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.note }}</p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Fasting:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.fasting ? 'Yes' : 'No' }}</p>
        </div>

        <div class="flex items-start">
          <label class="w-28 text-sm font-medium text-gray-700">Repeat:</label>
          <p class="text-sm text-gray-900 font-semibold">{{ order.repeat ? 'Yes' : 'No' }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="flex justify-end">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
      Cancel
    </button>
  </div>
</template>
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useEncounterStore } from '../../../stores/encounter'
import { api } from '../../../api'
import type { BundleOrderResponse, SearchOrderEntryResponse } from '../../../api/api-reference'
import Select from '../../../components/form-extensions/SelectFluent.vue'
import InputText from '../../../components/form-extensions/InputTextFluent.vue'
import DateTimePicker from '../../../components/form-extensions/DateTimePickerFluent.vue'
import { getPriorities } from '../../../utils/priority'
import { formatDate } from '@/utils/timeMethods'
import { getDurations } from '../../../utils/duration'
import { getFrequencies } from '../../../utils/frequencies'
import { getSpecimenTypes } from '../../../utils/specimen'
const encounterStore = useEncounterStore()

defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
}>()

defineEmits(['close'])
const editingBundle = computed(() => encounterStore.editingOrder as BundleOrderResponse)

const isStartTimeVisible = computed(() =>
  editingBundle.value.orders?.some((x) => x.orderType == 'Med'),
)

const priority = ref<string | undefined>()
const startTime = ref<Date | undefined>()
const fasting = ref<string | undefined>()
const repeat = ref<string | undefined>()
const note = ref<string | undefined | null>()

const priorities = getPriorities()
const durations = getDurations()
const frequencies = getFrequencies()
const specimenTypes = getSpecimenTypes()

const getDurationLabel = (value: string | undefined) => {
  if (!value) return ''
  return durations.filter((x) => x.value == value)[0]?.text
}
const getFrequencyLabel = (value: string | undefined) => {
  if (!value) return ''
  return frequencies.filter((x) => x.value == value)[0]?.text
}
const getSpecimenLabel = (value: string | undefined) => {
  if (!value) return ''
  return specimenTypes.filter((x) => x.value == value)[0]?.text
}
const yesNoOptions = ref([
  { text: 'Yes', value: 'Yes' },
  { text: 'No', value: 'No' },
])

const editBundle = async () => {
  try {
    await api.encounter.encounterEditOrderBundle(encounterStore.selectedEncounter!.id!, {
      id: editingBundle.value.id,
      priority: priority.value ?? null,
      startTime: startTime.value?.toISOString() ?? null,
      fasting: fasting.value ? fasting.value === 'Yes' : null,
      repeat: repeat.value ? repeat.value === 'Yes' : null,
      note: note.value ?? null,
    })
    await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
    await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)

    encounterStore.setEditingOrder(
      encounterStore.encounterOrders.filter((x) => x.id == editingBundle.value.id)[0],
      editingBundle.value.id!,
    )
  } catch (error) {
    console.log(error)
  }
}

watch(
  () => priority.value,
  () => {
    editBundle()
  },
)
watch(
  () => startTime.value,
  () => {
    editBundle()
  },
)
watch(
  () => fasting.value,
  () => {
    editBundle()
  },
)
watch(
  () => repeat.value,
  () => {
    editBundle()
  },
)
watch(
  () => note.value,
  () => {
    editBundle()
  },
)

onMounted(async () => {
  if (encounterStore.editingOrder) {
    const bundleOrder = encounterStore.editingOrder as BundleOrderResponse

    const firstOrder = bundleOrder.orders ? bundleOrder.orders[0] : null

    if (firstOrder) {
      if (bundleOrder.orders?.every((x) => x.priority == firstOrder.priority)) {
        priority.value = firstOrder.priority
      }
      if (bundleOrder.orders?.every((x) => x.orderType == 'Lab' || x.orderType == 'Procedure')) {
        const firstOrderFasting = bundleOrder.orders[0].fasting
        if (bundleOrder.orders?.every((x) => x.fasting == firstOrderFasting)) {
          fasting.value = firstOrderFasting ? 'Yes' : 'No'
        }
        const firstOrderRepeat = bundleOrder.orders[0].repeat
        if (bundleOrder.orders?.every((x) => x.repeat == firstOrderRepeat)) {
          repeat.value = firstOrderRepeat ? 'Yes' : 'No'
        }
      }
      if ('note' in firstOrder) {
        if (bundleOrder.orders?.every((x) => 'note' in x && x.note == firstOrder.note)) {
          note.value = firstOrder.note
        }
      }
    }
  }
})

watch(
  () => encounterStore.editingOrder,
  (newValue) => {
    if (newValue) {
      const bundleOrder = encounterStore.editingOrder as BundleOrderResponse

      const firstOrder = bundleOrder.orders ? bundleOrder.orders[0] : null

      if (firstOrder) {
        if (bundleOrder.orders?.every((x) => x.priority == firstOrder.priority)) {
          priority.value = firstOrder.priority
        }
        if (bundleOrder.orders?.every((x) => x.orderType == 'Lab' || x.orderType == 'Procedure')) {
          const firstOrderFasting = bundleOrder.orders[0].fasting
          if (bundleOrder.orders?.every((x) => x.fasting == firstOrderFasting)) {
            fasting.value = firstOrderFasting ? 'Yes' : 'No'
          }
          const firstOrderRepeat = bundleOrder.orders[0].repeat
          if (bundleOrder.orders?.every((x) => x.repeat == firstOrderRepeat)) {
            repeat.value = firstOrderRepeat ? 'Yes' : 'No'
          }
        }
        if ('note' in firstOrder) {
          if (bundleOrder.orders?.every((x) => 'note' in x && x.note == firstOrder.note)) {
            note.value = firstOrder.note
          }
        }
      }
    }
  },
)
</script>
