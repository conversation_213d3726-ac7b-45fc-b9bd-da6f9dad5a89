<template>
  <div>
    <div class="flex items-center w-full">
      <Button variant="link" @click="emit('close')">
        <ArrowUturnLeftIcon class="size-4" />
      </Button>
      <h2 class="text-lg font-bold">Medication Administration Record</h2>
    </div>
    <div class="flex items-center gap-2 my-2">
      <label class="block text-sm font-medium text-gray-700">Medication:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.medication }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Status:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.status }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Dose:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.dose }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Route:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.route }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Frequency:</label>
      <p class="text-gray-900 font-semibold">{{ formatFrequency(medicationRecord?.frequency) }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Duration:</label>
      <p class="text-gray-900 font-semibold">{{ formatDurations(medicationRecord?.duration) }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">PRN:</label>
      <p class="text-gray-900 font-semibold">{{ formatPrn(medicationRecord?.prn) }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">PRN reason:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.prnReason }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Start time:</label>
      <p class="text-gray-900 font-semibold">{{ formatDate(medicationRecord?.startTime) }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Instructions:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.instructions }}</p>
    </div>
    <div class="flex items-center gap-2 mb-2">
      <label class="block text-sm font-medium text-gray-700">Requester:</label>
      <p class="text-gray-900 font-semibold">{{ medicationRecord?.requester }}</p>
    </div>

    <Textarea id="note" label="Note" v-model="note" cols="8" rows="5" />
    <div class="flex items-center gap-2">
      <Button label="Administered" @click="changeTimingStatus('Administered')" />
      <Button label="In progress" @click="changeTimingStatus('InProgress')" />
      <Button label="Hold" @click="changeTimingStatus('Hold')" />
      <Button label="Not Done" @click="changeTimingStatus('NotDone')" />
      <Button label="Cancel" @click="changeStatus('Revoked')" />
    </div>
    <div class="bg-white border border-gray-200 rounded-xl shadow-sm mt-2">
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-sm">
            <h3 class="font-semibold tracking-wide text-gray-800">
              Past Medication Administations
            </h3>
          </div>
        </template>
      </TableHeader>
      <table class="min-w-full divide-y divide-gray-200">
        <tbody class="divide-y divide-gray-200">
          <tr
            v-for="(row, index) in medicationRecord?.administations"
            :key="index"
            class="relative hover:bg-gray-100 cursor-pointer"
          >
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-800">{{ index + 1 }}</span>
              </div>
            </td>
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-800">{{ formatDate(row.date) }}</span>
              </div>
            </td>
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-800">{{ row.administredByName }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { api } from '@/api'
import Button from 'primevue/button'
import { ArrowUturnLeftIcon } from '@heroicons/vue/24/outline'
import { useEncounterStore } from '@/stores/encounter.ts'
import { formatDate } from '@/utils/timeMethods'
import { getFrequencies } from '@/utils/frequencies.ts'
import { getDurations } from '@/utils/duration.ts'
import Textarea from '../../../components/form-extensions/TextareaFluent.vue'
import TableHeader from '@/components/table/TableHeader.vue'
import debounce from 'lodash.debounce'

const encounterStore = useEncounterStore()
const medicationRecord = ref()
const note = ref('')
const frequencies = getFrequencies()
const durations = getDurations()

const formatDurations = (duration: string) => {
  const durationItem = durations.filter((x) => x.value == duration)
  if (durations) {
    return durationItem[0]?.text
  }
}
const formatFrequency = (frequency: string) => {
  const frequencyItem = frequencies.filter((x) => x.value == frequency)
  if (frequencyItem) {
    return frequencyItem[0]?.text
  }
}
const formatPrn = (prn: boolean) => (prn ? 'Yes' : 'No')

const emit = defineEmits(['close'])
const props = defineProps<{
  orderId: string | undefined
  bundleId: string | null
}>()

onMounted(async () => {
  if (props.orderId) {
    const response = await api.encounter.encounterGetMedicationRecordByOrderId(
      encounterStore.selectedEncounter!.id!,
      props.orderId,
      { bundleId: props.bundleId },
    )
    medicationRecord.value = response.data
    note.value = medicationRecord.value.note
  }
})

watch(
  () => props.orderId,
  async (newValue) => {
    if (newValue) {
      const response = await api.encounter.encounterGetMedicationRecordByOrderId(
        encounterStore.selectedEncounter!.id!,
        newValue,
        { bundleId: props.bundleId },
      )
      medicationRecord.value = response.data
      note.value = medicationRecord.value.note
    }
  },
)

const updateNote = async (note: string) => {
  console.log(note)

  await api.encounter.encounterChangeOrderNote(encounterStore.selectedEncounter!.id!, {
    orderId: props.orderId!,
    note,
    bundleId: props.bundleId,
  })
}

const debouncedUpdateNote = debounce(updateNote, 500)

watch(
  () => note.value,
  (newValue) => {
    debouncedUpdateNote(newValue)
  },
)

const changeTimingStatus = async (status: string) => {
  await api.encounter.encounterChangeOrderTimingStatus(encounterStore.selectedEncounter!.id!, {
    orderIds: [props.orderId!],
    status,
  })
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  await encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)

  note.value = ''
  emit('close')
}

const changeStatus = async (status: string) => {
  await api.encounter.encounterChangeOrderStatus(encounterStore.selectedEncounter!.id!, {
    orderIds: [props.orderId!],
    status: status,
  })
  await encounterStore.getEncounterOrders(encounterStore.selectedEncounter!.id!)
  encounterStore.getPatientActiveMedications(encounterStore.selectedEncounter!.patientId!)

  note.value = ''
  emit('close')
}

watch(
  () => encounterStore.selectedEncounter?.patientId,
  () => {
    emit('close')
  },
)
</script>
