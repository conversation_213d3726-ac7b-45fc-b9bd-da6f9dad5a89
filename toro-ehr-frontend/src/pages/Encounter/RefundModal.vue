<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
  >
    <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">
        {{ refundType === 'total' ? 'Total Refund' : 'Partial Refund' }}
      </h2>

      <div class="overflow-y-auto max-h-[70vh] space-y-4">
        <!-- Transaction Details -->
        <div v-if="transaction" class="bg-gray-50 p-4 rounded-lg">
          <h3 class="text-sm font-semibold text-gray-700 mb-2">Transaction Details</h3>
          <div class="space-y-1 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Date:</span>
              <span class="font-medium">{{ formatDate(transaction.date) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Amount:</span>
              <span class="font-medium">${{ transaction.amount?.toFixed(2) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Payment Method:</span>
              <span class="font-medium">
                {{ transaction.paymentMethod === 'PosTerminal' ? 'Terminal' :
                   transaction.paymentMethod === 'SavedCard' ? 'Card On File' : 'N/A' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Card:</span>
              <span class="font-medium">
                {{ transaction.cardType ?? 'Card' }} •••• {{ transaction.last4 ?? '----' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Status:</span>
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                    :class="getPaymentStatusClasses(transaction.status)">
                {{ transaction.status }}
              </span>
            </div>
          </div>
        </div>

        <!-- Refund Amount Input -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Refund Amount
          </label>
          <InputNumber
            v-model="refundAmount"
            :disabled="refundType === 'total'"
            :min="0.01"
            :max="transaction?.amount || 0"
            mode="currency"
            currency="USD"
            locale="en-US"
            :minFractionDigits="2"
            :maxFractionDigits="2"
            :class="{
              'opacity-60': refundType === 'total'
            }"
            :invalid="hasValidationError"
            fluid
          />
          <p v-if="hasValidationError" class="mt-1 text-sm text-red-600">
            {{ validationError }}
          </p>
          <p v-if="refundType === 'total'" class="mt-1 text-sm text-gray-500">
            Full transaction amount will be refunded
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
        <Button
          @click="$emit('close')"
          :disabled="isLoading"
          variant="outlined"
          severity="secondary"
          class="px-4 py-2"
        >
          Cancel
        </Button>
        <Button
          @click="handleRefund"
          :disabled="isLoading || hasValidationError"
          :loading="isLoading"
          severity="danger"
          class="px-4 py-2"
        >
          {{ isLoading ? 'Processing...' : 'Refund' }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { EncounterTransactionResponse } from '@/api/api-reference'
import { formatDate } from '@/utils/timeMethods'
import { getPaymentStatusClasses } from '@/utils/statusColors'
import InputNumber from 'primevue/inputnumber'
import Button from 'primevue/button'

interface Props {
  isOpen: boolean
  transaction: EncounterTransactionResponse | null
  refundType: 'total' | 'partial'
  isLoading: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  refund: [amount: number]
}>()

const refundAmount = ref(0)

// Watch for transaction changes to set initial refund amount
watch(() => props.transaction, (newTransaction) => {
  if (newTransaction?.amount) {
    refundAmount.value = newTransaction.amount
  }
}, { immediate: true })

// Watch for refund type changes
watch(() => props.refundType, (newType) => {
  if (newType === 'total' && props.transaction?.amount) {
    refundAmount.value = props.transaction.amount
  }
})

// Validation
const validationError = computed(() => {
  if (!props.transaction?.amount) return ''

  if (refundAmount.value <= 0) {
    return 'Refund amount must be greater than $0.00'
  }

  if (refundAmount.value > props.transaction.amount) {
    return `Refund amount cannot exceed $${props.transaction.amount.toFixed(2)}`
  }

  return ''
})

const hasValidationError = computed(() => {
  return validationError.value !== ''
})



const handleRefund = () => {
  if (!hasValidationError.value && refundAmount.value > 0) {
    emit('refund', refundAmount.value)
  }
}
</script>
