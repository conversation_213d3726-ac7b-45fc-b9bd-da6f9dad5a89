<template>
  <ResizableBox title="Questionnaires" type="questionnaires">
    <!-- Title -->
    <h3 class="text-lg font-semibold mb-4">
      {{ encounterStore.selectedEncounter?.fullName }} responses
    </h3>

    <Accordion :value="['0']" multiple>
      <AccordionPanel
        v-for="(tab, index) in encounterStore.questionnaires"
        :key="tab.title"
        :value="index"
      >
        <AccordionHeader>{{ tab.title }}</AccordionHeader>
        <AccordionContent>
          <div v-for="(question, index) in tab.questions" :key="index" class="pl-2">
            <p>
              {{ question.text }}
              <span class="font-semibold">{{ question.answers?.join(', ') }}</span>
            </p>
          </div>
        </AccordionContent>
      </AccordionPanel>
    </Accordion>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../components/resizable/ResizableBox.vue'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionHeader from 'primevue/accordionheader'
import AccordionContent from 'primevue/accordioncontent'
import { useEncounterStore } from '@/stores/encounter'

const encounterStore = useEncounterStore()
</script>
