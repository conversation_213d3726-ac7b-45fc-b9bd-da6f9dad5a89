<template>
  <ResizableBox title="INFO" type="info">
    <div class="flex justify-between">
      <div>
        <h2 class="text-2xl font-semibold">{{ fullName }}</h2>
      </div>
      <div class="flex mb-2 gap-2 justify-end">
        <Button
          @click="activeTab = 'personal-info'"
          label="Personal Info"
          variant="link"
          :outlined="activeTab !== 'personal-info'"
        />
        <Button
          @click="activeTab = 'contact-info'"
          rounded
          label="Contact Info"
          variant="link"
          :outlined="activeTab !== 'contact-info'"
        />
        <Button
          @click="activeTab = 'documents'"
          rounded
          label="Documents"
          variant="link"
          :outlined="activeTab !== 'documents'"
        />
        <Button
          @click="activeTab = 'insurance'"
          rounded
          label="Insurance"
          variant="link"
          :outlined="activeTab !== 'insurance'"
        />
      </div>
    </div>
    <!-- Patient Overview Card -->
    <div v-if="activeTab == 'personal-info'" class="bg-white p-6 space-y-4">
      <div>
        <h2 class="text-xl font-semibold mb-4">Personal Information</h2>
      </div>
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <label class="font-semibold text-gray-700">Birthday</label>
          <p class="text-gray-900">{{ formattedBirthday }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Birth Sex</label>
          <p class="text-gray-900">{{ patient?.birthSex }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Gender Identity</label>
          <p class="text-gray-900">{{ patient?.genderIdentity || 'N/A' }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Sexual Orientation</label>
          <p class="text-gray-900">{{ patient?.sexualOrientation || 'N/A' }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Preferred Language</label>
          <p class="text-gray-900">{{ getLanguage(patient?.preferredLanguage) }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Race</label>
          <p class="text-gray-900">{{ patient?.race || 'N/A' }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Ethnicity</label>
          <p class="text-gray-900">{{ patient?.ethnicity || 'N/A' }}</p>
        </div>
        <div>
          <label class="font-semibold text-gray-700">Tribal Affiliation</label>
          <p class="text-gray-900">{{ patient?.tribalAffiliation || 'N/A' }}</p>
        </div>
      </div>
    </div>

    <!-- Documents Card -->
    <div v-if="activeTab == 'documents'" class="bg-white p-6 mr-4">
      <h2 class="text-xl font-semibold mb-4">Uploaded Documents</h2>
      <div v-if="documents.length === 0" class="text-gray-500 text-sm">
        No documents uploaded yet.
      </div>

      <div v-for="(doc, index) in documents" :key="index" class="border p-2 rounded-lg mb-2">
        <div class="flex flex-col sm:flex-row items-center gap-2">
          <div
            v-for="(file, fileIndex) in doc.filePaths"
            :key="fileIndex"
            class="flex flex-col sm:flex-row items-center p-2 rounded-lg"
          >
            <div
              class="w-12 h-12 sm:w-16 sm:h-16 border rounded-lg overflow-hidden flex justify-center items-center bg-gray-100"
            >
              <a v-if="file" :href="file" target="_blank" rel="noopener noreferrer">
                <img
                  alt="document"
                  v-if="isImageExisting(file)"
                  :src="file"
                  class="w-full h-full object-cover"
                />
                <span v-else class="text-gray-500 text-sm">PDF</span>
              </a>
            </div>
          </div>
          <div class="mt-2 sm:mt-0 sm:ml-4 flex-1">
            <p class="font-semibold">{{ doc.documentType }}</p>
            <p class="text-gray-500 text-sm truncate max-w-xs">
              {{ formatDateTime(doc.createdAt ?? '').formattedDate }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeTab == 'contact-info'" class="bg-white p-6">
      <h2 class="text-xl font-semibold mb-4">Contact Information</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <label class="font-medium text-gray-700">Email(s)</label>
          <p class="text-gray-900">{{ contactInfo.emails }}</p>
        </div>
        <div>
          <label class="font-medium text-gray-700">Phone(s)</label>
          <p class="text-gray-900">{{ contactInfo.phones }}</p>
        </div>
        <div>
          <label class="font-medium text-gray-700">Address</label>
          <p class="text-gray-900">{{ contactInfo.address }}</p>
        </div>
        <div v-if="contactInfo.previousAddress">
          <label class="font-medium text-gray-700">Previous Address</label>
          <p class="text-gray-900">{{ contactInfo.previousAddress }}</p>
        </div>
        <div>
          <label class="font-medium text-gray-700">Social Security Number</label>
          <p class="text-gray-900">{{ contactInfo.ssn }}</p>
        </div>
        <div v-if="contactInfo.preferredMethod">
          <label class="font-medium text-gray-700">Preferred Contact Method</label>
          <p class="text-gray-900">{{ contactInfo.preferredMethod }}</p>
        </div>
        <div v-if="contactInfo.emergencyContacts">
          <label class="font-medium text-gray-700">Emergency Contacts</label>
          <p class="text-gray-900">{{ contactInfo.emergencyContacts }}</p>
        </div>
      </div>
    </div>

    <div
      v-if="activeTab == 'insurance'"
      class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden mt-6 mr-4"
    >
      <TableHeader>
        <template #inputs>
          <h2 class="text-xl font-semibold mb-4">Insurance Information</h2>
        </template>
      </TableHeader>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Order
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Issuer
                </span>
              </div>
            </th>

            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Group/Member number
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Start/End
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Coverage type
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Relation
                </span>
              </div>
            </th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in patientInsurances" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <Tag :severity="row.order === 'Primary' ? 'success' : 'info'">{{ row.order }}</Tag>
              </div>
            </td>
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{ row.issuer }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{ row.groupId }}</span>
                <span class="block text-sm font-semibold text-gray-500">{{ row.memberId }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  row.start ? formatDateTime(row.start).formattedDate : 'N/A'
                }}</span>
                <span class="block text-sm text-gray-500">{{
                  row.end ? formatDateTime(row.end).formattedDate : 'N/A'
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{ row.type }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{
                  row.relationship
                }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- </div> -->
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '@/components/resizable/ResizableBox.vue'
import { computed, onMounted, ref } from 'vue'
import { usePatientStore } from '@/stores/patient'
import { useEncounterStore } from '@/stores/encounter'
import { getLanguages } from '@/utils/languages'
import { formatDateTime } from '@/utils/timeMethods'
import TableHeader from '@/components/table/TableHeader.vue'
import type { PatientInsuranceResponse } from '@/api/api-reference'
import { api } from '@/api'
import Button from 'primevue/button'

const patientStore = usePatientStore()
const encounterStore = useEncounterStore()

const activeTab = ref('personal-info')
const patientInsurances = ref<PatientInsuranceResponse[]>([])

onMounted(async () => {
  await patientStore.getPatientProfile(encounterStore.selectedEncounter!.patientId!)
  await fetchInsurances()
})

const fetchInsurances = async () => {
  try {
    const response = await api.patients.patientListInsurances({
      patientId: encounterStore.selectedEncounter!.patientId!,
    })
    // API returns single object, but we need array for UI
    patientInsurances.value = Array.isArray(response.data)
      ? response.data
      : [response.data].filter(Boolean)
  } catch (error) {
    console.log(error)
  }
}

const patient = computed(() => patientStore.patientProfile)

const fullName = computed(() => {
  const p = patient.value
  if (!p) return 'N/A'

  const first = p.firstName || ''
  const middle = p.middleName || ''
  const last = p.lastName || ''
  const suffix = p.suffix ? ` ${p.suffix}` : ''
  const previousFirst = p.previousFirstName || ''
  const previousLast = p.previousLastName || ''
  const previous =
    previousFirst || previousLast
      ? ` (ex. ${[previousFirst, previousLast].filter(Boolean).join(' ')})`
      : ''
  const preferred = p.preferredName ? ` - ${p.preferredName}` : ''

  return `${first} ${middle} ${last}${suffix}${previous}${preferred}`.trim()
})

const formattedBirthday = computed(() => {
  const p = patient.value
  if (!p?.birthday) return 'N/A'
  const date = new Date(p.birthday)
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  })
})
const languages = getLanguages()
const getLanguage = (language: string | undefined | null) => {
  if (!language) return 'N/A'
  const lang = languages.filter((x) => x.value == language)[0]
  return lang?.text ?? 'N/A'
}

const documents = computed(() => patient.value?.documents || [])

const isImageExisting = (filePath: string) => {
  return filePath.endsWith('.jpg') || filePath.endsWith('.png') || filePath.endsWith('.jpeg')
}

const contactInfo = computed(() => {
  const emails = patient.value?.emails?.map((e) => e.email).join(', ') || 'N/A'
  const phones =
    patient.value?.phones
      ?.map((p) => `${p.number}${p.type ? ' (' + p.type + ')' : ''}`)
      .join(', ') || 'N/A'
  const address = patient.value?.address
    ? `${patient.value.address.street || ''}, ${patient.value.address.city || ''}, ${patient.value.address.state || ''} ${patient.value.address.zipCode || ''}`
        .replace(/, ,/g, ',')
        .trim()
    : 'N/A'
  const previousAddress = patient.value?.previousAddress
    ? `${patient.value.previousAddress.street || ''}, ${patient.value.previousAddress.city || ''}, ${patient.value.previousAddress.state || ''} ${patient.value.previousAddress.zipCode || ''}`
        .replace(/, ,/g, ',')
        .trim()
    : ''
  const ssn = patient.value?.socialSecurityNumber || 'N/A'
  const preferredMethod = patient.value?.preferredContactMethod
  const preferredContactName = patient.value?.preferredContactName || 'N/A'
  const emergencyContacts =
    (patient.value?.emergencyContacts || []).length > 0 &&
    patient.value!.emergencyContacts![0].phoneNumber
      ? patient
          .value!.emergencyContacts!.map((c) => `${c.name} (${c.relationship}): ${c.phoneNumber}`)
          .join(', ')
      : 'N/A'

  return {
    emails,
    phones,
    address,
    previousAddress,
    ssn,
    preferredMethod,
    preferredContactName,
    emergencyContacts,
  }
})
</script>

<style scoped>
label {
  display: block;
  margin-bottom: 0.25rem;
}
</style>
