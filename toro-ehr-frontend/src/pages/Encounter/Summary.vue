<template>
  <ResizableBox title="SUMMARY" type="summary" layout="right">
    <div class="space-y-4 p-4">
      <!-- Top Stats -->
      <div class="flex flex-col md:flex-row md:flex-wrap gap-2 text-sm text-gray-700">
        <div class="flex gap-1" v-if="lastHeight">
          <span class="font-semibold">HEIGHT:</span>
          {{ lastHeight?.cm }}cm | {{ lastHeight?.feet }}'{{ lastHeight?.inches }}"
        </div>
        <div class="flex gap-1" v-if="lastWeight">
          <span class="font-semibold">WEIGHT:</span>
          {{ lastWeight?.kg }}kg | {{ lastWeight?.lbs }}lbs
        </div>
        <div class="flex gap-1" v-if="bmi">
          <span class="font-semibold">BMI =</span>
          {{ bmi }}
        </div>
      </div>

      <div class="flex flex-col md:flex-row md:flex-wrap gap-2 text-sm text-gray-700">
        <div class="flex gap-1">
          <span class="font-semibold">Last encounter:</span>
          {{ formatDate(encounterStore.selectedEncounter?.previouesEncounterDate) }}
        </div>
        <div v-if="reasonForVisit" class="flex gap-1">
          <span class="font-semibold">Patient reason for visit:</span>
          {{ reasonForVisit }}
        </div>
      </div>

      <!-- Accordion -->
      <Accordion :value="['0']" multiple>
        <!-- Latest Vitals -->
        <AccordionPanel value="0">
          <AccordionHeader class="p-2 -ml-4 font-semibold text-sm text-gray-700"
            ><div v-html="latestVitalsDate"
          /></AccordionHeader>
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in latestMeasurementsFormatted" :key="index">
                {{ item }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Problem List / Care -->
        <AccordionPanel value="1">
          <AccordionHeader class="p-2 -ml-4 font-semibold text-sm text-gray-700"
            >Problem List / Care</AccordionHeader
          >
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="item in encounterStore.patientProblems" :key="item.id">
                {{ item.conditionDescription }} | {{ item.clinicalStatus }} |
                {{ item.verificationStatus }} | Diagnosed
                {{ formatDateWithoutTime(item.diagnosisDate) }}
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Medications -->
        <AccordionPanel value="2">
          <AccordionHeader class="p-2 -ml-4 font-semibold text-sm text-gray-700"
            >Medications</AccordionHeader
          >
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientProfile?.medications" :key="index">
                <div v-for="(part, i) in splitNameParts(item.displayName!)" :key="i">
                  {{ part.trim() }}
                </div>

                <!-- Keep practitioner info after -->
                <div class="mt-1 text-sm text-gray-600">patient specified</div>
                <Divider
                  v-if="
                    encounterStore.patientActiveMedicationOrders ||
                    index < encounterStore.patientProfile!.medications!.length - 1
                  "
                  class="my-2"
                />
              </li>
              <li
                v-for="(item, index) in encounterStore.patientActiveMedicationOrders"
                :key="index"
              >
                <!-- Split item.name by commas and show each part on new line -->
                <div v-for="(part, i) in splitNameParts(item.name!)" :key="i">
                  {{ part.trim() }}
                </div>

                <!-- Keep practitioner info after -->
                <div class="mt-1 text-sm text-gray-600">med request by {{ item.practitioner }}</div>
                <Divider
                  v-if="index < encounterStore.patientActiveMedicationOrders!.length - 1"
                  class="my-2"
                />
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Allergies -->
        <AccordionPanel value="3">
          <AccordionHeader class="p-2 -ml-4 font-semibold text-sm text-gray-700"
            >Allergies</AccordionHeader
          >
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientProfile?.allergies" :key="index">
                <div class="mt-1 text-sm text-gray-600">{{ item.displayName }}</div>
                <div class="mt-1 text-sm text-gray-600">{{ item.severity }}</div>
                <Divider
                  v-if="index < encounterStore.patientProfile!.allergies!.length - 1"
                  class="my-2"
                />
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>

        <!-- Immunizations -->
        <AccordionPanel value="4">
          <AccordionHeader class="p-2 -ml-4 font-semibold text-sm text-gray-700"
            >Immunizations</AccordionHeader
          >
          <AccordionContent>
            <ul class="text-sm text-gray-800 space-y-1">
              <li v-for="(item, index) in encounterStore.patientImmunizations" :key="index">
                <div v-for="(part, i) in splitNameParts(item.displayName!)" :key="i">
                  {{ part.trim() }}
                </div>
                <div class="mt-1 text-sm text-gray-600">{{ item.date }}</div>
                <Divider
                  v-if="index < encounterStore.patientImmunizations!.length - 1"
                  class="my-2"
                />
              </li>
            </ul>
          </AccordionContent>
        </AccordionPanel>
      </Accordion>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '@/components/resizable/ResizableBox.vue'
import { computed, ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import Accordion from 'primevue/accordion'
import AccordionPanel from 'primevue/accordionpanel'
import AccordionContent from 'primevue/accordioncontent'
import AccordionHeader from 'primevue/accordionheader'
import { formatDate, formatDateWithoutTime } from '@/utils/timeMethods'
import Divider from 'primevue/divider'
import { usePatientStore } from '../../stores/patient'

const encounterStore = useEncounterStore()
const patientStore = usePatientStore()
const selectedPatientId = ref<string | null>(null)

const reasonForVisit = computed(() => {
  const questionare = encounterStore.questionnaires?.filter((x) => x.locationId == null)
  if (
    !questionare ||
    questionare.length == 0 ||
    !questionare[0].questions ||
    !questionare[0].questions[0].answers
  )
    return ''
  return questionare[0].questions[0].answers[0]
})

const vitalSigns = computed(() => encounterStore.vitalSigns)
const lastWeight = computed(() => {
  let weight: number | null | undefined = undefined

  if (!vitalSigns.value || vitalSigns.value.length === 0) {
    weight = patientStore.patientProfile?.weightInKg
  } else {
    // Sort entries by date descending
    const sorted = [...vitalSigns.value].sort(
      (a, b) => new Date(b.recordedDate!).getTime() - new Date(a.recordedDate!).getTime(),
    )

    // Find first BodyWeight
    const weightMeasurement = sorted?.find((m) => m.type === 'BodyWeight')
    if (weightMeasurement) {
      weight = parseFloat(weightMeasurement.value!)
    } else {
      weight = patientStore.patientProfile?.weightInKg
    }
  }

  if (weight) {
    const lbs = weight * 2.20462
    return {
      kg: weight,
      lbs: lbs.toFixed(1), // round to 1 decimal place
    }
  }

  return null
})

const lastHeight = computed(() => {
  let height: number | null | undefined = undefined
  if (!vitalSigns.value || vitalSigns.value.length === 0) {
    height = patientStore.patientProfile?.heightInCm
  } else {
    // Sort entries by date descending
    const sorted = [...vitalSigns.value].sort(
      (a, b) => new Date(b.recordedDate!).getTime() - new Date(a.recordedDate!).getTime(),
    )

    // Find first BodyHeight
    const heightMeasurement = sorted?.find((m) => m.type === 'BodyHeight')
    if (heightMeasurement) {
      height = parseFloat(heightMeasurement.value!)
    } else {
      height = patientStore.patientProfile?.heightInCm
    }
  }

  if (height) {
    const totalInches = height * 0.393701
    const feet = Math.floor(totalInches / 12)
    const inches = Math.round(totalInches % 12)
    return {
      cm: height,
      feet,
      inches,
    }
  }
  return null
})

const bmi = computed(() => {
  if (!lastWeight.value || !lastHeight.value) {
    return null
  }

  const weightKg = lastWeight.value.kg
  const heightCm = lastHeight.value.cm
  const heightM = heightCm / 100

  if (heightM === 0) {
    return null
  }

  const bmiValue = weightKg / (heightM * heightM)
  return bmiValue.toFixed(1) // rounded to 1 decimal
})

const latestVitalRecord = computed(() => {
  const vitals = vitalSigns.value
  if (!vitals || vitals.length === 0) {
    return []
  }

  // Pronađi najnoviji datum
  const latestDate = vitals
    .filter((v) => v.recordedDate)
    .map((v) => new Date(v.recordedDate!))
    .sort((a, b) => b.getTime() - a.getTime())[0]

  if (!latestDate) return []

  // Vrati sve sa tim datumom
  return vitals.filter((v) => {
    const vDate = new Date(v.recordedDate!)
    return vDate.getTime() === latestDate.getTime()
  })
})

const latestMeasurementsFormatted = computed(() => {
  if (!latestVitalRecord.value) {
    return []
  }

  // Build a map for quick lookup (e.g., BP systolic and diastolic)
  const measurementsMap = Object.fromEntries(latestVitalRecord.value?.map((m) => [m.type, m.value]))

  // Compose formatted labels
  const formatted: string[] = []

  // Blood Pressure
  if (measurementsMap['SystolicBloodPressure'] && measurementsMap['DiastolicBloodPressure']) {
    formatted.push(
      `BP = ${measurementsMap['SystolicBloodPressure']}/${measurementsMap['DiastolicBloodPressure']} mmHg`,
    )
  }

  // Heart Rate
  if (measurementsMap['HeartRate']) {
    formatted.push(`HR = ${measurementsMap['HeartRate']} bpm`)
  }

  // Temperature
  if (measurementsMap['BodyTemperature']) {
    formatted.push(`Temp = ${measurementsMap['BodyTemperature']} °C`)
  }

  // Respiratory Rate
  if (measurementsMap['RespiratoryRate']) {
    formatted.push(`RR = ${measurementsMap['RespiratoryRate']} bpm`)
  }

  // Pain
  if (measurementsMap['PainScale']) {
    formatted.push(`Pain = ${measurementsMap['PainScale']}`)
  }

  // Height
  if (measurementsMap['BodyHeight']) {
    formatted.push(`Height = ${measurementsMap['BodyHeight']} cm`)
  }

  // Height
  if (measurementsMap['BodyWeight']) {
    formatted.push(`Weight = ${measurementsMap['BodyWeight']} kg`)
  }

  return formatted
})

const latestVitalsDate = computed(() => {
  if (!latestVitalRecord.value) {
    return 'Latest Vitals: <span class="font-normal">N/A</span>'
  }

  return `Latest Vitals: <span class="font-normal">${formatDate(latestVitalRecord.value[0]?.recordedDate)}</span>`
})

function splitNameParts(name: string): string[] {
  if (name.includes(',')) {
    // case 1: split by commas
    return name.split(',')
  } else {
    // case 2: split on first number
    const match = name.match(/(\d.*)/)
    if (match) {
      const beforeNumber = name.slice(0, match.index).trim()
      const fromNumber = name.slice(match.index!).trim()
      return [beforeNumber, fromNumber]
    }
    // fallback: return whole name as single line
    return [name]
  }
}

watch(
  () => encounterStore.selectedEncounter,
  (newEncounter) => {
    selectedPatientId.value = newEncounter!.patientId!
  },
)
</script>
