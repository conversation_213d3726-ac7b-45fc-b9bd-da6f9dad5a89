<template>
  <div v-if="isVisible" class="p-3 rounded space-y-3">
    <InputTextFluent id="subject" label="Subject" />

    <div class="flex items-center space-x-2">
      <FileUpload
        mode="basic"
        chooseLabel="Attachments"
        chooseIcon="pi pi-paperclip"
        custom-upload
        auto
        :multiple="true"
        @select="handleFileSelect($event)"
      />
    </div>
    <div v-for="file in selectedFiles" :key="file.name">
      <span
        >{{ file.name }}
        <Button variant="link" icon="pi pi-trash" @click="removeAttachment(file.name)"
      /></span>
    </div>
    <Textarea id="message" label="Message" rows="10" cols="20" />

    <Button
      label="Send"
      @click="sendEmail"
      class="w-full font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
    />
  </div>
</template>
<script setup lang="ts">
import type { FileUploadSelectEvent } from 'primevue/fileupload'
import { useForm } from 'vee-validate'
import { ref, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import InputTextFluent from '@/components/form-extensions/InputTextFluent.vue'
import Textarea from '@/components/form-extensions/TextareaFluent.vue'
import FileUpload from 'primevue/fileupload'
import Button from 'primevue/button'
import { useToast } from 'vue-toastification'
const toast = useToast()

const encounterStore = useEncounterStore()

defineProps<{ isVisible: boolean }>()
const emit = defineEmits(['close'])
const selectedFiles = ref<File[]>([])

const { handleSubmit, resetForm } = useForm()

const handleFileSelect = (event: FileUploadSelectEvent) => {
  selectedFiles.value = event.files
}

const removeAttachment = (name: string) => {
  selectedFiles.value = selectedFiles.value.filter((x: { name: string }) => x.name != name)
}

const sendEmail = handleSubmit(async (values) => {
  try {
    console.log(values)
    const data = {
      ...values,
      Attachments: selectedFiles.value,
      MessageType: 'Email',
      PatientId: encounterStore.selectedEncounter!.patientId!,
      EncounterId: encounterStore.selectedEncounter?.id,
    }
    await api.encounter.encounterSendEncounterMessage(encounterStore.selectedEncounter!.id!, data)
    resetForm()
    await api.encounter.encounterGetCommunications(encounterStore.selectedEncounter!.id!)
    toast.success('Email sent successfully')
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

watch(
  () => encounterStore.selectedEncounter,
  () => {
    resetForm()
    selectedFiles.value = []
    emit('close')
  },
)
</script>
