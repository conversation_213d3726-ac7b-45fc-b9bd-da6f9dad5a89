<template>
  <ResizableBox title="COMMS" type="comms">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <p
            v-if="encounterStore.selectedEncounter?.preferredContactMethod"
            class="font-semibold tracking-wide text-gray-800"
          >
            Preferred Contact:
            <span class="font-normal tracking-wide text-gray-600">{{
              encounterStore.selectedEncounter?.preferredContactMethod
            }}</span>
          </p>
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div class="bg-white border border-gray-200 rounded-xl shadow-sm">
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <div v-if="!editMode" class="flex items-center">
                      <FloatLabel variant="on">
                        <InputText
                          id="search"
                          v-model="search"
                          class="w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                        />
                        <label for="search">Search</label>
                      </FloatLabel>
                    </div>

                    <div v-else class="flex items-center">
                      <Button variant="link" @click="backToTableView">
                        <ArrowUturnLeftIcon class="size-4" />
                      </Button>
                      <h3 class="font-semibold tracking-wide text-gray-800">{{ title }}</h3>
                    </div>
                  </div>
                </template>
                <template #buttons>
                  <Button
                    v-if="encounterStore.selectedEncounter?.patientId"
                    icon="pi pi-plus"
                    label="New"
                    @click="toggleMenu"
                    class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
                  />
                  <Menu ref="menuRef" :model="items" popup />
                </template>
              </TableHeader>
              <SendEmail :isVisible="sendEmailOpen" @close="backToTableView" />
              <SendSms :isVisible="sendSmsOpen" @close="backToTableView" />
              <RecordCall :isVisible="recordCallOpen" @close="backToTableView" />
              <ViewMessage :isVisible="viewMessageOpen" :selectedMessage="selectedMessage" />

              <table v-if="!editMode" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Date
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Time
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Type
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Subject
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-end">
                      <div class="flex items-center gap-x-2"></div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <tr
                    v-for="result in filteredResults"
                    :key="result.id"
                    class="relative hover:bg-gray-100 cursor-pointer"
                  >
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          formatDateWithoutTime(result.sentAt)
                        }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          formatTime(result.sentAt)
                        }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">
                          {{ result.type }}
                        </span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">
                          {{ result.subject }}
                        </span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">
                          <Button
                            variant="link"
                            label="View"
                            @click="openMessage(result)"
                            class="p-button-danger"
                          />
                        </span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../../components/resizable/ResizableBox.vue'
import { computed, ref, type ComputedRef } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { formatTime, formatDateWithoutTime } from '@/utils/timeMethods'
import TableHeader from '@/components/table/TableHeader.vue'
import InputText from 'primevue/inputtext'
import FloatLabel from 'primevue/floatlabel'
import Button from 'primevue/button'
import type { EncounterCommunicationMessageResponse } from '@/api/api-reference.ts'
import Menu from 'primevue/menu'
import { ArrowUturnLeftIcon } from '@heroicons/vue/24/outline'
import SendEmail from './SendEmail.vue'
import ViewMessage from './ViewMessage.vue'
import SendSms from './SendSms.vue'
import RecordCall from './RecordCall.vue'

const encounterStore = useEncounterStore()

const search = ref('')
const sendEmailOpen = ref(false)
const sendSmsOpen = ref(false)
const recordCallOpen = ref(false)
const viewMessageOpen = ref(false)
const selectedMessage = ref<EncounterCommunicationMessageResponse>()

const editMode = computed(
  () => sendEmailOpen.value || sendSmsOpen.value || recordCallOpen.value || viewMessageOpen.value,
)
const title = computed(() => {
  if (sendEmailOpen.value) return 'Send email'
  if (sendSmsOpen.value) return 'Send text'
  if (viewMessageOpen.value) return selectedMessage.value?.subject
  else return 'Record call'
})

const filteredResults: ComputedRef<EncounterCommunicationMessageResponse[]> = computed(() => {
  const query = search.value.trim().toLowerCase()
  if (!query) return encounterStore.encounterMessages

  return (
    encounterStore.encounterMessages.filter((item) =>
      item?.subject?.toLowerCase().includes(query),
    ) ?? []
  )
})

const menuRef = ref()
const toggleMenu = (event: MouseEvent) => {
  menuRef.value.toggle(event)
}

const items = ref([
  {
    label: 'Email',
    command: () => {
      sendEmailOpen.value = true
      sendSmsOpen.value = false
      recordCallOpen.value = false
    },
  },
  {
    label: 'Text',
    command: () => {
      sendSmsOpen.value = true
      sendEmailOpen.value = false
      recordCallOpen.value = false
    },
  },
  {
    label: 'Call',
    command: () => {
      recordCallOpen.value = true
      sendEmailOpen.value = false
      sendSmsOpen.value = false
    },
  },
])

const openMessage = (msg: EncounterCommunicationMessageResponse) => {
  viewMessageOpen.value = true
  selectedMessage.value = msg
}

const backToTableView = async () => {
  await encounterStore.getEncounterMessages(encounterStore.selectedEncounter!.id!)
  sendEmailOpen.value = false
  sendSmsOpen.value = false
  recordCallOpen.value = false
  viewMessageOpen.value = false
}
</script>
