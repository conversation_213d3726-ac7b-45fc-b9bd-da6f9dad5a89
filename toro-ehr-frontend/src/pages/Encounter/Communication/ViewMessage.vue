<template>
  <div v-if="isVisible" class="p-3 rounded space-y-3">
    <div v-for="(file, index) in selectedMessage?.attachments" :key="index">
      <Button variant="link" :label="getFileNameFromUrl(file)" @click="openFileNewTab(file)" />
    </div>
    <Textarea
      disabled
      v-model="selectedMessage!.message"
      id="message"
      label="message"
      rows="10"
      cols="20"
    />
  </div>
</template>
<script setup lang="ts">
import type { EncounterCommunicationMessageResponse } from '@/api/api-reference.ts'
import { getFileNameFromUrl } from '@/utils/stringUtils'
import Textarea from '@/components/form-extensions/TextareaFluent.vue'
import Button from 'primevue/button'

defineProps<{
  isVisible: boolean
  selectedMessage: EncounterCommunicationMessageResponse | undefined
}>()

function openFileNewTab(url: string | null) {
  if (url) window.open(url, '_blank')
}
</script>
