<template>
  <div class="flex bg-gray-100 px-4 py-2 shadow-md items-center gap-2 h-12">
    <h1 class="text-sm font-bold">
      <Select
        v-model="encounterStore.selectedEncounter"
        :options="encounterStore.activeEncounters"
        option-label="fullName"
      />
    </h1>
    <p
      v-if="encounterStore.selectedEncounter?.patientBirthday && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">AGE: </span>{{ patientAge }} |
    </p>
    <p v-if="encounterStore.selectedEncounter?.patientBirthday" class="text-sm">
      <span class="font-bold">DOB: </span
      >{{ formatDateWithoutTime(encounterStore.selectedEncounter?.patientBirthday) }} |
    </p>
    <p
      v-if="encounterStore.selectedEncounter?.patientBirthday && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">Birth Sex: </span>Female |
    </p>
    <p
      v-if="encounterStore.selectedEncounter?.phoneNumber && !encounterStore.isMobile"
      class="text-sm"
    >
      <span class="font-bold">Phone: </span
      >{{ formatPhoneNumber(encounterStore.selectedEncounter?.phoneNumber) }}
    </p>
    <div v-if="!encounterStore.isMobile" class="ml-auto flex items-center gap-2">
      <div class="flex items-center gap-1">
        <label class="text-sm font-bold">Layout:</label>
        <Select
          v-model="selectedLayoutId"
          :options="availableLayouts"
          option-label="name"
          option-value="id"
          placeholder="Select layout"
          class="min-w-32"
          @change="onLayoutChange"
        />
      </div>
      <Button variant="link" @click="openSaveModal">Save</Button>
      <Button variant="link" @click="encounterStore.reset()">Default</Button>
    </div>
  </div>
  <!-- Visible buttons on large screens -->
  <div class="hidden justify-between items-center xl:flex gap-2 flex-wrap h-12">
    <Button
      variant="link"
      label="Patients"
      @click="encounterStore.setActiveBox('active-patients')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Care"
      @click="encounterStore.setActiveBox('care')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Questionnaires"
      @click="encounterStore.setActiveBox('questionnaires')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Vitals"
      @click="encounterStore.setActiveBox('vitals')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Orders"
      @click="encounterStore.setActiveBox('orders')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Notes"
      @click="encounterStore.setActiveBox('notes')"
      :class="{ history: isPatientView }"
    />
    <Button
      v-if="!isNonPrescribingOrganization"
      variant="link"
      label="Labs"
      @click="encounterStore.setActiveBox('labs')"
      :class="{ history: isPatientView }"
    />
    <Button
      v-if="!isNonPrescribingOrganization"
      variant="link"
      label="Imaging"
      @click="encounterStore.setActiveBox('imaging')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Comms"
      @click="encounterStore.setActiveBox('comms')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Scratch"
      @click="encounterStore.setActiveBox('scratch')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="Info"
      @click="encounterStore.setActiveBox('info')"
      :class="{ history: isPatientView }"
    />
    <Button
      variant="link"
      label="S"
      @click="encounterStore.setActiveBox('summary')"
      :class="{ history: isPatientView }"
    />
  </div>

  <!-- Dropdown for smaller screens -->
  <div class="flex block xl:hidden">
    <Button
      icon="pi pi-bars"
      @click="toggleMenu"
      severity="secondary"
      aria-label="Menu"
      class="ml-auto"
    />
    <Menu ref="menuRef" :model="menuOptions" :popup="true" />
  </div>
  <div class="flex">
    <!-- Main Content -->
    <main class="flex-1 bg-gray-50 relative overflow-hidden">
      <div class="parent" style="height: calc(100vh - 204px)">
        <ActivePatients />
        <Care v-if="encounterStore.selectedEncounter" />
        <Scratch v-if="encounterStore.selectedEncounter" />
        <VitalSignBox v-if="encounterStore.selectedEncounter" />
        <Questionnaires v-if="encounterStore.selectedEncounter" />
        <LabBox v-if="encounterStore.selectedEncounter && !isNonPrescribingOrganization" />
        <ImagingResultBox
          v-if="encounterStore.selectedEncounter && !isNonPrescribingOrganization"
        />
        <CommsTable v-if="encounterStore.selectedEncounter" />
        <OrderTable v-if="encounterStore.selectedEncounter" />
        <Notes v-if="encounterStore.selectedEncounter" />
        <Summary v-if="encounterStore.selectedEncounter" />
        <PatientInfo v-if="encounterStore.selectedEncounter" />
      </div>
    </main>
  </div>

  <!-- Save Layout Modal -->
  <Teleport to="body">
    <div
      v-if="isSaveModalOpen"
      class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
    >
      <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
        <h2 class="text-lg font-bold mb-4">Save Encounter Layout</h2>

        <!-- Form -->
        <form @submit.prevent="saveLayout">
          <div class="mb-4">
            <InputText id="layoutName" label="Layout Name" />
          </div>
          <div class="flex justify-end">
            <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="closeSaveModal">
              Cancel
            </button>
            <button type="submit" class="bg-primary text-white px-4 py-2 rounded">Save</button>
          </div>
        </form>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import 'vue-draggable-resizable/style.css'
import ActivePatients from './ActivePatients.vue'
import Button from 'primevue/button'
import Scratch from './Scratch.vue'
import VitalSignBox from './VitalSigns/VitalSignBox.vue'
import Questionnaires from './Questionnaires.vue'
import Summary from './Summary.vue'
import { formatDateWithoutTime } from '@/utils/timeMethods'
import Select from 'primevue/select'
import { useEncounterStore } from '@/stores/encounter.ts'
import Menu from 'primevue/menu'
import LabBox from './Labs/LabBox.vue'
import ImagingResultBox from './ImagingResultBox.vue'
import CommsTable from './Communication/CommsTable.vue'
import OrderTable from './Order/OrderTable.vue'
import Notes from '@/pages/Encounter/Notes.vue'
import Care from '@/pages/Encounter/CarePlan/CareBox.vue'
import { useRoute, useRouter } from 'vue-router'
import PatientInfo from './PatientInfo.vue'
import { formatPhoneNumber } from '@/utils/stringUtils'
import { useAuthStore } from '../../stores/auth'
import InputText from '../../components/form-extensions/InputText.vue'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import { api } from '@/api'
import type { SaveEncounterLayoutCommand, EncounterLayoutResponse } from '@/api/api-reference'
import { useToast } from 'vue-toastification'
const toast = useToast()

const encounterStore = useEncounterStore()
const authStore = useAuthStore()
const route = useRoute()
const router = useRouter()

// Save modal state
const isSaveModalOpen = ref(false)

// Layout state
const availableLayouts = ref<EncounterLayoutResponse[]>([])
const selectedLayoutId = ref<string>('')

// Form validation schema
const schema = yup.object({
  layoutName: yup.string().required('Layout name is required').min(1, 'Layout name is required'),
})

// Form setup
const { handleSubmit, resetForm } = useForm({
  validationSchema: schema,
})

// Modal functions
const openSaveModal = () => {
  isSaveModalOpen.value = true
  resetForm()
}

const closeSaveModal = () => {
  isSaveModalOpen.value = false
  resetForm()
  loadAvailableLayouts()
}

// Load available layouts
const loadAvailableLayouts = async () => {
  try {
    const response = await api.encounter.encounterLoadEncounterLayout()
    availableLayouts.value = response.data || []
  } catch (error) {
    console.error('Error loading layouts:', error)
  }
}

// Handle layout selection change
const onLayoutChange = async () => {
  if (!selectedLayoutId.value) return

  try {
    const selectedLayout = availableLayouts.value.find(
      (layout) => layout.id === selectedLayoutId.value,
    )

    if (!selectedLayout || !selectedLayout.encounterBoxes) {
      toast.error('Selected layout not found or invalid')
      return
    }

    // Get current and saved container dimensions
    const currentContainerWidth = encounterStore.parentContainerWidth
    const currentContainerHeight = encounterStore.parentContainerHeight
    const savedContainerWidth = selectedLayout.containerWidth || currentContainerWidth
    const savedContainerHeight = selectedLayout.containerHeight || currentContainerHeight

    // Calculate scaling ratios
    const widthRatio = currentContainerWidth / savedContainerWidth
    const heightRatio = currentContainerHeight / savedContainerHeight

    // Update encounter store with the loaded layout and apply proportional scaling
    encounterStore.encounterBoxes = selectedLayout.encounterBoxes.map((box) => ({
      type: box.type || '',
      isOpen: box.isOpen || false,
      height: Math.round((box.height || 400) * heightRatio),
      width: Math.round((box.width || 700) * widthRatio),
      x: box.x !== null && box.x !== undefined ? Math.round(box.x * widthRatio) : undefined,
      y: box.y !== null && box.y !== undefined ? Math.round(box.y * heightRatio) : undefined,
      z: box.z || 1,
      isExpanded: box.isExpanded || false,
      oldX:
        box.oldX !== null && box.oldX !== undefined ? Math.round(box.oldX * widthRatio) : undefined,
      oldY:
        box.oldY !== null && box.oldY !== undefined
          ? Math.round(box.oldY * heightRatio)
          : undefined,
      key: box.key || '',
    }))

    // Update localStorage
    localStorage.removeItem('encounterBoxes')
    localStorage.setItem('encounterBoxes', JSON.stringify(encounterStore.encounterBoxes))

    toast.success(`Layout "${selectedLayout.name}" loaded successfully!`)
  } catch (error) {
    console.error('Error loading layout:', error)
    toast.error('Error loading layout')
  } finally {
    // clear the select so placeholder shows again
    await nextTick()
    selectedLayoutId.value = ''
  }
}

// Save layout function
const saveLayout = handleSubmit(async (values) => {
  try {
    const layoutData: SaveEncounterLayoutCommand = {
      name: values.layoutName,
      encounterBoxes: encounterStore.encounterBoxes,
      containerHeight: encounterStore.parentContainerHeight,
      containerWidth: encounterStore.parentContainerWidth,
    }

    await api.encounter.encounterSaveEncounterLayout(layoutData)

    toast.success(`Layout "${values.layoutName}" saved successfully!`)

    closeSaveModal()
  } catch (error) {
    console.error('Error saving layout:', error)
  }
})

const isPatientView = computed(() => route.name === 'patient')
const isNonPrescribingOrganization = computed(
  () => authStore.user?.organizationType == 'NonPrescribingOrganization',
)

onBeforeMount(async () => {
  await reinitialize()
  await loadAvailableLayouts()
})

const patientAge = computed(() => {
  if (!encounterStore.selectedEncounter) return 'n/a'
  const birthDate = new Date(encounterStore.selectedEncounter.patientBirthday!)
  const today = new Date()

  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDifference = today.getMonth() - birthDate.getMonth()

  // If the birthday hasn't occurred yet this year, subtract one year
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  return age
})

watch(
  () => encounterStore.selectedEncounter,
  async (newEncounter) => {
    await encounterStore.getEncounter(newEncounter!)
    if (!isPatientView.value) {
      router.replace({ name: route.name!, params: { id: newEncounter?.id } })
    }
  },
)

watch(
  () => route.fullPath,
  async () => {
    if (!route.params.id) {
      await reinitialize()
    }
  },
)

const reinitialize = async () => {
  encounterStore.setEncounterBoxes()
  await encounterStore.getActiveEncounters()
  if (isPatientView.value) {
    const encounter = encounterStore.activeEncounters.find((x) => x.patientId === route.params.id)

    if (encounter) {
      encounterStore.setSelectedEncounterById(encounter.id!)
      router.replace({ name: 'encounters', params: { id: encounter.id! } })
    } else {
      await encounterStore.getLatestPatientEncounter(route.params.id as string)
    }
  } else {
    if (route.params.id) {
      encounterStore.setSelectedEncounterById(route.params.id as string)
    } else {
      encounterStore.setSelectedEncounterDefault(router, route)
    }
  }
}

const menuRef = ref()

const toggleMenu = (event: MouseEvent) => {
  menuRef.value?.toggle(event)
}
const menuOptions = [
  { label: 'Care', command: () => encounterStore.setActiveBox('care') },
  { label: 'Questionnaires', command: () => encounterStore.setActiveBox('questionnaires') },
  { label: 'Vitals', command: () => encounterStore.setActiveBox('vitals') },
  { label: 'Orders', command: () => encounterStore.toggleIsBoxOpen('orders') },
  { label: 'Notes', command: () => encounterStore.toggleIsBoxOpen('notes') },
  ...(!isNonPrescribingOrganization.value
    ? [{ label: 'Labs', command: () => encounterStore.toggleIsBoxOpen('labs') }]
    : []),
  ...(!isNonPrescribingOrganization.value
    ? [{ label: 'Imaging', command: () => encounterStore.toggleIsBoxOpen('imaging') }]
    : []),
  { label: 'Comms', command: () => encounterStore.setActiveBox('comms') },
  { label: 'Scratch', command: () => encounterStore.setActiveBox('scratch') },
  { label: 'Info', command: () => encounterStore.setActiveBox('info') },
  { label: 'S', command: () => encounterStore.setActiveBox('summary') },
]

const handleResize = () => {
  encounterStore.reset()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
<style scoped>
:deep(.history) {
  @apply text-history;
}
</style>
