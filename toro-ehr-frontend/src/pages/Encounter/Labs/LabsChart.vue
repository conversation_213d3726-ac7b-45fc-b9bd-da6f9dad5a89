<template>
  <v-chart
    v-if="chartVisible"
    :option="chartOptions"
    ref="chartRef"
    style="height: 300px"
    :style="{ width: contentWidth + 'px' }"
  />
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { use } from 'echarts/core'
import { LineChart } from 'echarts/charts'
import { CanvasRenderer } from 'echarts/renderers'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { useEncounterStore } from '../../../stores/encounter'
import type { LaboratoryResultResponse } from '../../../api/api-reference'
import { formatDate } from '../../../utils/timeMethods'

const encounterStore = useEncounterStore()
use([Line<PERSON><PERSON>, Canvas<PERSON>enderer, Grid<PERSON>omponent, Tooltip<PERSON>omponent, LegendComponent])

const chartRef = ref<InstanceType<typeof VChart> | null>(null)
const chartVisible = ref(false)
const props = defineProps<{
  selectedResult: Record<string, LaboratoryResultResponse>
}>()

onMounted(() => {
  if (box.value.isExpanded) {
    const parent = document.querySelector('.parent')
    contentWidth.value = parent!.clientWidth - 60
  }
})

const box = computed(() => encounterStore.encounterBoxes.filter((x) => x.type == 'labs')[0])
const contentWidth = ref(box.value.width - 60)

watch(
  () => box.value.width,
  (newValue) => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentWidth.value = parent!.clientWidth - 60
    } else {
      contentWidth.value = newValue - 60
    }
    nextTick(() => {
      chartRef.value!.chart!.resize()
    })
  },
)

watch(
  () => box.value.isExpanded,
  () => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentWidth.value = parent!.clientWidth - 60
    } else {
      contentWidth.value = box.value.width - 60
    }
    nextTick(() => {
      chartRef.value!.chart!.resize()
    })
  },
)

watch(
  () => props.selectedResult,
  () => {
    chartVisible.value = true
  },
)

const chartOptions = computed(() => {
  const dataPoints = Object.entries(props.selectedResult)
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    .filter(([timestamp, res]) => res.valueQuantity !== undefined)
    .sort(([a], [b]) => a.localeCompare(b)) // sort by timestamp
    .map(([timestamp, res]) => ({
      name: formatDate(timestamp),
      value: [timestamp, res.valueQuantity],
    }))

  const displayName = Object.values(props.selectedResult).map((r) => r.codeDisplay)[0]
  const legendData = ['High Ref', displayName, 'Low Ref']

  const referenceLow = Object.entries(props.selectedResult).map(([timestamp, res]) => ({
    name: formatDate(timestamp),
    value: [timestamp, res.referenceRangeLow ?? null],
  }))
  const referenceHigh = Object.entries(props.selectedResult).map(([timestamp, res]) => ({
    name: formatDate(timestamp),
    value: [timestamp, res.referenceRangeHigh ?? null],
  }))
  if (dataPoints.length == 1) {
    const valueDate = new Date(referenceLow[0].name)
    const valueDateYesterday = new Date(valueDate.getTime() - 86400000).toISOString()
    const valueDateTomorrow = new Date(valueDate.getTime() + 86400000).toISOString()
    referenceLow.unshift({
      name: formatDate(valueDateYesterday),
      value: [valueDateYesterday, referenceLow[0].value[1]],
    })
    referenceLow.push({
      name: formatDate(valueDateTomorrow),
      value: [valueDateTomorrow, referenceLow[0].value[1]],
    })
    referenceHigh.unshift({
      name: formatDate(valueDateYesterday),
      value: [valueDateYesterday, referenceHigh[0].value[1]],
    })
    referenceHigh.push({
      name: formatDate(valueDateTomorrow),
      value: [valueDateTomorrow, referenceHigh[0].value[1]],
    })
  }

  return {
    tooltip: { trigger: 'axis' },
    legend: {
      data: legendData,
      orient: 'vertical',
      left: 'left',
      top: 'center',
    },
    grid: { left: '15%', right: '1%', bottom: '3%', containLabel: true },
    xAxis: {
      type: 'time',
      splitNumber: 5,
      axisLabel: { formatter: '{MM}/{dd}/{yy}' },
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: 'High Ref',
        type: 'line',
        data: referenceHigh,
        symbol: 'none',
        lineStyle: {
          type: 'dashed',
          color: '#ef4444',
        },
        itemStyle: {
          color: '#ef4444',
        },
      },
      {
        name: displayName,
        type: 'line',
        data: dataPoints,
        smooth: true,
        symbol: 'circle',
        lineStyle: {
          color: '#3b82f6',
        },
        itemStyle: {
          color: '#3b82f6',
        },
      },
      {
        name: 'Low Ref',
        type: 'line',
        data: referenceLow,
        symbol: 'none',
        lineStyle: {
          type: 'dashed',
          color: '#10b981',
        },
        itemStyle: {
          color: '#10b981',
        },
      },
    ],
  }
})
</script>
