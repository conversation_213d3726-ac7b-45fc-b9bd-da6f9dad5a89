<template>
  <ResizableBox title="LABS" type="labs">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div
              class="bg-white border border-gray-200 rounded-xl shadow-sm max-h-80 overflow-auto"
            >
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <FloatLabel variant="on">
                      <InputText
                        id="search"
                        v-model="search"
                        class="w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                      />
                      <label for="search">Search</label>
                    </FloatLabel>
                  </div>
                </template>
              </TableHeader>
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Results
                        </span>
                      </div>
                    </th>
                    <th
                      v-for="timestamp in filteredGroupResults.allTimestamps"
                      :key="timestamp"
                      scope="col"
                      class="ps-6 py-3 text-end"
                    >
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          {{ formatDate(timestamp) }}
                        </span>
                        <Button
                          v-if="hasUnseenResults(timestamp)"
                          @click.stop="markColumnAsSeen(timestamp)"
                          icon="pi pi-eye-slash"
                          variant="text"
                          class="h-4"
                          v-tooltip.top="'Mark as seen'"
                        />
                      </div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <tr
                    v-for="(row, testName) in filteredGroupResults.table"
                    :key="testName"
                    class="relative hover:bg-gray-100 cursor-pointer"
                    @click="showChartForTest(row)"
                  >
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{ testName }}</span>
                      </div>
                    </td>
                    <td
                      v-for="timestamp in filteredGroupResults.allTimestamps"
                      :key="timestamp"
                      class="h-px w-72 whitespace-nowrap"
                    >
                      <template v-if="row[timestamp]">
                        <div class="px-6 py-3" v-tooltip.left="getReferenceTooltip(row[timestamp])">
                          <span
                            class="block text-sm text-gray-800"
                            :class="{
                              'font-bold': row[timestamp] && row[timestamp].seen === false,
                            }"
                            :style="{ color: getColor(row[timestamp].interpretationCode) }"
                            >{{ row[timestamp].valueQuantity }}
                            <span class="text-xs">{{ row[timestamp].valueUnit }}</span></span
                          >
                        </div>
                      </template>
                      <template v-else>
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800"> - </span>
                        </div></template
                      >
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <LabsChart :selectedResult="selectedResult" />
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../../components/resizable/ResizableBox.vue'
import { computed, ref } from 'vue'
import { useEncounterStore } from '../../../stores/encounter'
import type { LaboratoryResultResponse } from '../../../api/api-reference'
import { formatDate } from '@/utils/timeMethods'
import LabsChart from './LabsChart.vue'
import TableHeader from '@/components/table/TableHeader.vue'
import InputText from 'primevue/inputtext'
import FloatLabel from 'primevue/floatlabel'
import Button from 'primevue/button'
import { api } from '../../../api'

const encounterStore = useEncounterStore()

const selectedResult = ref()
const search = ref('')

type ResultTable = {
  [testName: string]: {
    [timestamp: string]: LaboratoryResultResponse
  }
}

const groupResults = computed(() => {
  const table: ResultTable = {}
  const timestampSet = new Set<string>()

  for (const result of encounterStore.laboratoryResults) {
    const testName = result.codeDisplay ?? result.code ?? 'Unknown Test'
    const timestamp = result.effectiveDateTime ?? result.issued ?? ''

    if (!timestamp) continue

    timestampSet.add(timestamp)
    if (!table[testName]) {
      table[testName] = {}
    }

    table[testName][timestamp] = result
  }

  const allTimestamps = Array.from(timestampSet).sort()

  return { table, allTimestamps }
})

const filteredGroupResults = computed(() => {
  const query = search.value.trim().toLowerCase()
  if (!query) return groupResults.value

  const filteredTable: ResultTable = {}

  for (const [testName, resultMap] of Object.entries(groupResults.value.table)) {
    if (testName.toLowerCase().includes(query)) {
      filteredTable[testName] = resultMap
    }
  }

  return {
    table: filteredTable,
    allTimestamps: groupResults.value.allTimestamps,
  }
})

const getColor = (code?: string | null | undefined): string => {
  switch (code) {
    case 'HH':
    case 'LL':
    case 'CRIT':
    case 'A':
      return 'red'
    case 'H':
    case 'L':
      return 'orange'
    default:
      return 'inherit'
  }
}
const getReferenceTooltip = (result: LaboratoryResultResponse) => {
  const low = result.referenceRangeLow ?? '–'
  const high = result.referenceRangeHigh ?? '–'
  return `ref: ${low} - ${high}`.trim()
}

const showChartForTest = (row: Record<string, LaboratoryResultResponse>) => {
  selectedResult.value = row
}

const hasUnseenResults = (timestamp: string) => {
  for (const row of Object.values(filteredGroupResults.value.table)) {
    const result = row[timestamp]
    if (result && !result.seen) return true
  }
  return false
}

const markColumnAsSeen = async (timestamp: string) => {
  await api.patients.patientMarkLaboratoryResultAsSeen({
    patientId: encounterStore.selectedEncounter?.patientId,
    date: timestamp,
  })
  await encounterStore.getEncounter(encounterStore.selectedEncounter!)
}
</script>
