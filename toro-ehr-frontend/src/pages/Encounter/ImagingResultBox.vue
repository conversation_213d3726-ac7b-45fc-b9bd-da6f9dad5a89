<template>
  <ResizableBox title="IMAGING" type="imaging">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div class="bg-white border border-gray-200 rounded-xl shadow-sm">
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <FloatLabel variant="on">
                      <InputText
                        id="search"
                        v-model="search"
                        class="w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                      />
                      <label for="search">Search</label>
                    </FloatLabel>
                  </div>
                </template>
              </TableHeader>
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Description
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Date
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Finding
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-end">
                      <div class="flex items-center gap-x-2"></div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <tr
                    v-for="result in filteredResults"
                    :key="result.id"
                    class="relative hover:bg-gray-100 cursor-pointer"
                    :class="{
                      'font-bold': result.seen === false,
                    }"
                  >
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{ result.title }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          formatDate(result.effectiveDate)
                        }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800"></span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">
                          <Button
                            label="View Report"
                            @click="openFileInNewTab(result)"
                            class="p-button-danger"
                          />
                        </span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../components/resizable/ResizableBox.vue'
import { computed, ref, type ComputedRef } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { formatDate } from '@/utils/timeMethods'
import TableHeader from '@/components/table/TableHeader.vue'
import InputText from 'primevue/inputtext'
import FloatLabel from 'primevue/floatlabel'
import Button from 'primevue/button'
import { api } from '@/api'
import type { ImagingFileResponse } from '@/api/api-reference.ts'

const encounterStore = useEncounterStore()

type FlattenedImagingFile = ImagingFileResponse & {
  id: string
  status: string | null | undefined
  patientId: string | null | undefined
  effectiveDate: string | null | undefined
}

const flattenedFiles: ComputedRef<FlattenedImagingFile[]> = computed(() =>
  encounterStore.imagingResults.flatMap(
    (result) =>
      result.files?.map((file) => ({
        ...file,
        id: result.id!,
        status: result.status,
        patientId: result.patientId,
        effectiveDate: result.effectiveDate,
      })) ?? [],
  ),
)

const search = ref('')

const filteredResults: ComputedRef<FlattenedImagingFile[]> = computed(() => {
  const query = search.value.trim().toLowerCase()
  if (!query) return flattenedFiles.value

  return flattenedFiles.value.filter((item) => item?.title?.toLowerCase().includes(query)) ?? []
})

function openFileInNewTab(result: FlattenedImagingFile) {
  markFileAsAsSeen(result)
  window.open(result.url!, '_blank')
}

const markFileAsAsSeen = async (result: FlattenedImagingFile) => {
  await api.patients.patientMarkImagingResultAsSeen( {
    patientId: encounterStore.selectedEncounter?.patientId,
    id: result.id,
    url: result.url!,
  })
  await encounterStore.getEncounter(encounterStore.selectedEncounter!)
}
</script>
