<template>
  <ResizableBox title="SCRATCH" type="scratch">
    <Textarea
      id="over_label"
      v-model="scratch"
      @update:modelValue="updateScratchText"
      :rows="rows"
      auto-resize
      fluid
    />
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../components/resizable/ResizableBox.vue'
import { computed, ref, watch } from 'vue'
import Textarea from 'primevue/textarea'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import debounce from 'lodash.debounce'

const encounterStore = useEncounterStore()

const box = computed(() => encounterStore.encounterBoxes.filter((x) => x.type == 'scratch')[0])
const contentHeight = ref(box.value.height - 50)

const scratch = ref(encounterStore.selectedEncounter?.scratchText)
const rows = ref(Math.floor(contentHeight.value / 28))

watch(
  () => encounterStore.selectedEncounter,
  (newEncounter) => {
    scratch.value = newEncounter?.scratchText
  },
)

watch(
  () => box.value.height,
  (newValue) => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentHeight.value = parent!.clientHeight - 50
    } else {
      contentHeight.value = newValue - 50
    }
    rows.value = Math.floor(contentHeight.value / 28)
  },
)

watch(
  () => box.value.isExpanded,
  () => {
    if (box.value.isExpanded) {
      const parent = document.querySelector('.parent')
      contentHeight.value = parent!.clientHeight - 50
    } else {
      contentHeight.value = box.value.height - 50
    }
    rows.value = Math.floor(contentHeight.value / 28)
  },
)

const updateScratch = async () => {
  try {
    await api.encounter.encounterUpdateScratchText({
      patientId: encounterStore.selectedEncounter?.patientId,
      scratchText: scratch.value ?? "",
    })
  } catch (error) {
    console.log(error)
  }
}

const updateScratchText = debounce(() => {
  updateScratch()
}, 500)
</script>
