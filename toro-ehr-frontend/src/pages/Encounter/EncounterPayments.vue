<template>
  <div class="bg-gray-50">
    <!-- Full Width Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-4xl font-bold text-gray-900">Encounter Payments</h1>
            <p class="text-lg text-gray-600 mt-2">Process payments and manage transaction history</p>
          </div>
          <Button
            @click="$router.back()"
            variant="outlined"
            icon="pi pi-arrow-left"
            label="Back"
            class="flex items-center gap-2 text-lg px-6 py-3"
          />
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 px-8 py-8">
      <!-- Operation Result Message -->
      <div v-if="paymentResult" class="mb-8 p-6 rounded-lg border-l-4 shadow-lg"
           :class="paymentResult.success
             ? 'bg-green-50 border-green-400 text-green-800'
             : 'bg-red-50 border-red-400 text-red-800'">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i :class="paymentResult.success ? 'pi pi-check-circle text-green-500' : 'pi pi-times-circle text-red-500'"
               class="text-2xl"></i>
          </div>
          <div class="ml-4 flex-1">
            <div class="font-bold text-xl">
              {{ paymentResult.success ? 'Payment Processed Successfully!' : 'Payment Failed' }}
            </div>
            <div class="text-base mt-2">{{ paymentResult.message }}</div>
            <div v-if="paymentResult.success" class="text-sm mt-2 font-medium">
              ✓ Transaction has been recorded and is visible in the history below
            </div>
          </div>
          <div class="ml-auto">
            <Button
              icon="pi pi-times"
              variant="text"
              size="small"
              @click="paymentResult = null"
              class="text-gray-500 hover:text-gray-700 p-2"
              v-tooltip.left="'Dismiss message'"
            />
          </div>
        </div>
      </div>

      <!-- Two Column Layout -->
      <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <!-- Left Column - Payment Form (1/3 width) -->
        <div class="xl:col-span-1 space-y-6">
          <!-- Payment Form -->
          <PaymentForm
            v-if="patientId"
            :encounterId="encounterId"
            :patientId="patientId"
            @paymentSuccess="handlePaymentSuccess"
            @paymentError="handlePaymentError"
          />

          <!-- Loading state for payment form -->
          <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">New Payment</h2>
            </div>
            <div class="p-6 flex items-center justify-center">
              <div class="flex items-center space-x-2 text-gray-500">
                <i class="pi pi-spin pi-spinner"></i>
                <span>Loading payment options...</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column - Transaction History (2/3 width) -->
        <div class="xl:col-span-2 space-y-6">
          <!-- Transaction History -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-2xl font-semibold text-gray-900">Transaction History</h2>
              <p class="text-sm text-gray-600 mt-1">View and manage payment transactions for this encounter</p>
            </div>

            <!-- Empty State -->
            <div v-if="transactions.length === 0" class="p-8 text-center">
              <i class="pi pi-credit-card text-4xl text-gray-400 mb-4 block"></i>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
              <p class="text-gray-600">Process a payment to see transaction history here.</p>
            </div>

            <!-- Desktop Table -->
            <div v-else class="hidden md:block overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Method</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Card</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="tx in transactions" :key="tx.id" class="hover:bg-gray-50 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ formatDate(tx.date) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                      ${{ tx.amount?.toFixed(2) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ formatTransactionType(tx.transactionType) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ tx.paymentMethod === 'PosTerminal' ? 'Terminal' :
                         tx.paymentMethod === 'SavedCard' ? 'Card On File' : 'N/A' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ tx.cardType ?? 'Card' }} •••• {{ tx.last4 ?? '----' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                            :class="getPaymentStatusClasses(tx.status)">
                        <span class="w-2 h-2 rounded-full mr-2" :class="getPaymentStatusDotClass(tx.status)"></span>
                        {{ tx.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <Button
                        icon="pi pi-ellipsis-v"
                        variant="text"
                        rounded
                        @click="toggle($event, tx.id!)"
                        :disabled="tx.status?.toLowerCase() !== 'approved'"
                        :class="{ 'opacity-50 cursor-not-allowed': tx.status?.toLowerCase() !== 'approved' }"
                        v-tooltip.top="tx.status?.toLowerCase() !== 'approved' ? 'Only approved transactions can be refunded' : 'Refund options'"
                        aria-haspopup="true"
                        aria-controls="overlay_menu"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile Cards -->
            <div v-if="transactions.length > 0" class="md:hidden divide-y divide-gray-200">
              <div v-for="tx in transactions" :key="tx.id" class="p-6 space-y-4 hover:bg-gray-50 transition-colors">
                <div class="flex justify-between items-start">
                  <div>
                    <div class="text-lg font-semibold text-gray-900">${{ tx.amount?.toFixed(2) }}</div>
                    <div class="text-sm text-gray-500">{{ formatDate(tx.date) }}</div>
                  </div>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                        :class="getPaymentStatusClasses(tx.status)">
                    <span class="w-2 h-2 rounded-full mr-2" :class="getPaymentStatusDotClass(tx.status)"></span>
                    {{ tx.status }}
                  </span>
                </div>
                <div class="text-sm text-gray-600 space-y-1">
                  <div>{{ formatTransactionType(tx.transactionType) }} •
                       {{ tx.paymentMethod === 'PosTerminal' ? 'Terminal' :
                          tx.paymentMethod === 'SavedCard' ? 'Card On File' : 'N/A' }}</div>
                  <div>{{ tx.cardType ?? 'Card' }} •••• {{ tx.last4 ?? '----' }}</div>
                </div>
                <div class="flex justify-end">
                  <Button
                    variant="outlined"
                    label="Refund Options"
                    size="small"
                    @click="toggle($event, tx.id!)"
                    :disabled="tx.status?.toLowerCase() !== 'approved'"
                    :class="{ 'opacity-50 cursor-not-allowed': tx.status?.toLowerCase() !== 'approved' }"
                    v-tooltip.top="tx.status?.toLowerCase() !== 'approved' ? 'Only approved transactions can be refunded' : 'Refund options'"
                    aria-haspopup="true"
                    aria-controls="overlay_menu"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <Menu ref="menu" id="overlay_menu" :model="items" :popup="true"/>

  <!-- Refund Modal -->
  <RefundModal
    :isOpen="isRefundModalOpen"
    :transaction="selectedTransactionData"
    :refundType="refundType"
    :isLoading="isRefundLoading"
    @close="closeRefundModal"
    @refund="handleRefund"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '@/api'
import Button from 'primevue/button'
import Menu from 'primevue/menu'
import type { EncounterTransactionResponse, ProcessPaymentResponse } from '@/api/api-reference'
import { formatDate } from '@/utils/timeMethods'
import { getPaymentStatusClasses, getPaymentStatusDotClass } from '@/utils/statusColors'
import PaymentForm from '@/components/PaymentForm.vue'
import RefundModal from './RefundModal.vue'

const route = useRoute()
const encounterId = route.params.encounterId as string

const patientId = ref<string>('')
const transactions = ref<EncounterTransactionResponse[]>([])
const paymentResult = ref<ProcessPaymentResponse | null>(null)

const menu = ref()
const selectedTransaction = ref()
const isRefundModalOpen = ref(false)
const refundType = ref<'total' | 'partial'>('total')
const selectedTransactionData = ref<EncounterTransactionResponse | null>(null)
const isRefundLoading = ref(false)

const items = ref([
  {
    label: 'Refund total',
    icon: 'pi pi-dollar',
    command: () => openRefundModal('total')
  },
  {
    label: 'Refund partially',
    icon: 'pi pi-dollar',
    command: () => openRefundModal('partial')
  }
])

// Remove auto-hide watcher for payment results
// Keep success messages visible until manually dismissed
// watch(paymentResult, (newResult) => {
//   if (newResult?.success) {
//     setTimeout(() => {
//       paymentResult.value = null
//     }, 5000)
//   }
// })

const handlePaymentSuccess = async (result: ProcessPaymentResponse) => {
  paymentResult.value = result
  await refreshTransactions()
}

const handlePaymentError = (error: string) => {
  paymentResult.value = { success: false, message: error }
}

const refreshTransactions = async () => {
  try {
    const response = await api.encounter.encounterListEncounterTransactions(encounterId)
    transactions.value = response.data
  } catch (error) {
    console.error('Failed to refresh transactions', error)
  }
}

const formatTransactionType = (type?: string): string => {
  switch (type) {
    case 'Charge': return 'Charge'
    case 'Refund': return 'Refund'
    case 'Void': return 'Void'
    default: return type ?? '—'
  }
}



const toggle = (event: MouseEvent, id: string) => {
  const transaction = transactions.value.find(tx => tx.id === id)
  if (transaction?.status?.toLowerCase() === 'approved') {
    menu.value.toggle(event)
    selectedTransaction.value = id
  }
}

const openRefundModal = (type: 'total' | 'partial') => {
  if (selectedTransaction.value) {
    const transaction = transactions.value.find(tx => tx.id === selectedTransaction.value)
    if (transaction) {
      selectedTransactionData.value = transaction
      refundType.value = type
      isRefundModalOpen.value = true
    }
  }
}

const closeRefundModal = () => {
  isRefundModalOpen.value = false
  selectedTransactionData.value = null
  isRefundLoading.value = false
}

const handleRefund = async (refundAmount: number) => {
  if (!selectedTransactionData.value) return

  isRefundLoading.value = true
  try {
    let response
    if (refundType.value === 'total') {
      response = await api.encounter.encounterVoidOrRefundTotal(selectedTransactionData.value.id!)
      paymentResult.value = Array.isArray(response.data) ? response.data[0] : response.data
    } else {
      response = await api.encounter.encounterCustomRefund(selectedTransactionData.value.id!, {
        transactionId: selectedTransactionData.value.id!,
        refundAmount: refundAmount
      })
      paymentResult.value = response.data
    }

    await refreshTransactions()
    closeRefundModal()
  } catch (error) {
    console.error('Refund failed', error)
    paymentResult.value = { success: false, message: 'Refund failed. Please try again.' }
  } finally {
    isRefundLoading.value = false
  }
}

onMounted(async () => {
  try {
    // Fetch encounter details to get patientId
    const encounterDetails = await api.encounter.encounterGetEncounterById(encounterId)
    patientId.value = encounterDetails.data.patientId || ''

    if (!patientId.value) {
      console.warn('No patientId found in encounter details for encounter:', encounterId)
    }

    await refreshTransactions()
  } catch (error) {
    console.error('Failed to fetch encounter details', error)
  }
})
</script>
