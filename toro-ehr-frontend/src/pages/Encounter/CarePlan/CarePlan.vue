<template>
  <div class="bg-white border border-gray-200 rounded-xl shadow-sm max-h-80 overflow-auto">
    <TableHeader>
      <template #inputs>
        <div class="flex items-center gap-2">
          <div class="relative max-w-sm">
            <h2 class="text-xl font-bold">Care Plan</h2>
          </div>
          <span
            class="text-primary underline cursor-pointer text-sm flex py-2"
            @click="$emit('switch-table')"
          >
            Team <ArrowUpRightIcon class="size-3 m-1"
          /></span>
        </div>
      </template>
    </TableHeader>
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Date </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Status </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Practitioner </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Title </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <!-- <span class="text-xs font-semibold tracking-wide text-gray-800"> Title </span> -->
            </div>
          </th>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200">
        <tr
          v-for="(row, index) in encounterStore.patientCarePlans"
          :key="index"
          class="relative hover:bg-gray-100 cursor-pointer"
        >
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ formatDate(row.date) }}</span>
            </div>
          </td>
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{
                row.isConfirmed ? 'confirmed' : 'active'
              }}</span>
            </div>
          </td>
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.practitioner }}</span>
            </div>
          </td>
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.title }}</span>
            </div>
          </td>
          <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
            <div class="flex items-right gap-x-4">
              <a
                v-show="!row.isConfirmed"
                class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                href="#"
                @click.prevent="completeNote(row.id!)"
              >
                Mark completed
              </a>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <!-- End Table -->
  </div>
</template>
<script setup lang="ts">
import TableHeader from '@/components/table/TableHeader.vue'
import { useEncounterStore } from '@/stores/encounter'
import { formatDate } from '@/utils/timeMethods'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { api } from '../../../api'

defineEmits(['switch-table'])

const encounterStore = useEncounterStore()

const completeNote = async (id: string) => {
  await api.encounter.encounterMarkEncounterNoteCompleted(id)
  encounterStore.getCare(
    encounterStore.selectedEncounter!.patientId!,
    encounterStore.selectedEncounter!.id!,
  )
}
</script>
