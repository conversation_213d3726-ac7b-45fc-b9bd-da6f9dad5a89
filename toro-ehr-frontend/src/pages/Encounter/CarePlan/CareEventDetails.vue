<template>
  <div class="bg-white border border-gray-200 rounded-xl shadow-sm max-h-80 overflow-auto">
    <TableHeader>
      <template #inputs>
        <div class="flex items-center gap-4 relative max-w-sm">
          <h2 class="text-xl font-bold">Filtered by:</h2>
          <Select
            v-model="selectedOption"
            :options="filterOptions"
            option-label="text"
            option-value="value"
          />
        </div>
      </template>
    </TableHeader>

    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800">Date</span>
            </div>
          </th>
          <template v-for="column in tableConfig[selectedOption]?.columns" :key="column.label">
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold tracking-wide text-gray-800">{{
                  column.label
                }}</span>
              </div>
            </th>
          </template>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200">
        <tr
          v-for="(row, index) in filteredEvents"
          :key="index"
          class="relative hover:bg-gray-100 cursor-pointer"
        >
          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ formatDate(row.date) }}</span>
            </div>
          </td>

          <template v-for="column in tableConfig[selectedOption]?.columns" :key="column.label">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <component :is="column.render" :row="row" :getMedicationName="getMedicationName" />
              </div>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
    <ViewMedRequest
      :order-id="selectedMedRequestOrderId"
      :bundle-id="selectedMedRequestBundleId"
      :is-modal-open="isMedRequestVisible"
      @close="closeMedReq"
    />
  </div>
</template>

<script setup lang="ts">
import TableHeader from '@/components/table/TableHeader.vue'
import Select from 'primevue/select'
import { ref, computed, h } from 'vue'
import { formatDate } from '@/utils/timeMethods'
import { useEncounterStore } from '@/stores/encounter'
import { getDurations } from '@/utils/duration'
import { getFrequencies } from '@/utils/frequencies'
import type { MedicationEventResponse } from '@/api/api-reference'
import ViewMedRequest from './ViewMedRequest.vue'

const selectedOption = ref('Medication')
const encounterStore = useEncounterStore()

const frequencies = getFrequencies()
const durations = getDurations()

const isMedRequestVisible = ref(false)
const selectedMedRequestOrderId = ref()
const selectedMedRequestBundleId = ref()

const filterOptions = ref([
  { text: 'Doctor & Specialty', value: 'Encounter' },
  { text: 'Problems & Care Plans', value: 'Problem' },
  { text: 'Medication changes', value: 'Medication' },
  { text: 'Labs & imaging & procedure', value: 'Lab' },
])

const formatDurations = (duration: string) => durations.find((x) => x.value === duration)?.text
const formatFrequency = (frequency: string) => frequencies.find((x) => x.value === frequency)?.text

const getMedicationName = (med: MedicationEventResponse, recordedByPatient: boolean) => {
  if (recordedByPatient) return med.name
  return `${med.name}, ${formatFrequency(med.frequency!)}, ${formatDurations(med.duration!)}`
}

const tableConfig = {
  Encounter: {
    columns: [
      {
        label: 'Name',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.row.practitioner ? props.row.practitioner.name : 'patient'),
      },
      { label: 'Orders', render: () => h('span', '') },
      { label: 'Result', render: () => h('span', '') },
    ],
  },
  Problem: {
    columns: [
      {
        label: 'Abatement Date',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) => h('span', props.row.problem.abatement),
      },
      {
        label: 'Clinical Status',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) => h('span', props.row.problem.clinicalStatus),
      },
      {
        label: 'Name',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.row.practitioner ? props.row.practitioner.name : 'patient'),
      },
      {
        label: 'Condition',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) => h('span', props.row.problem.conditionDescription),
      },
    ],
  },
  Medication: {
    columns: [
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { label: 'Status', render: (props: any) => h('span', props.row.medication?.status) },
      {
        label: 'Medication, dose, frequency, duration',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.getMedicationName(props.row.medication!, props.row.practitioner == null)),
      },
      {
        label: 'PRN reason',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.row.medication?.prn ? props.row.medication?.prnReason : 'no'),
      },
      {
        label: 'Name',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.row.practitioner ? props.row.practitioner.name : 'patient'),
      },
      {
        label: 'MedReq',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) => {
          if (!props.row.practitioner) return null

          return h(
            'button',
            {
              class:
                'text-blue-500 hover:underline px-2 py-1 text-sm focus:outline-none focus:ring-0',
              onClick: () =>
                openMedReq(props.row.medication.orderId, props.row.medication.bundleId),
            },
            'open',
          )
        },
      },
    ],
  },
  Lab: {
    columns: [
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { label: 'Type', render: (props: any) => h('span', props.row.lab?.subType) },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { label: 'Description', render: (props: any) => h('span', props.row.lab?.name) },
      {
        label: 'Name',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        render: (props: any) =>
          h('span', props.row.practitioner ? props.row.practitioner.name : 'patient'),
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      { label: 'Result', render: (props: any) => h('span', props.row.lab?.result || '') },
    ],
  },
}

const filteredEvents = computed(() => {
  return encounterStore.patientCareEvents?.filter((x) => x.type === selectedOption.value)
})

const openMedReq = (orderId: string, bundleId: string | undefined) => {
  selectedMedRequestOrderId.value = orderId
  selectedMedRequestBundleId.value = bundleId
  isMedRequestVisible.value = true
}

const closeMedReq = () => {
  selectedMedRequestOrderId.value = undefined
  selectedMedRequestBundleId.value = undefined
  isMedRequestVisible.value = false
}
</script>
