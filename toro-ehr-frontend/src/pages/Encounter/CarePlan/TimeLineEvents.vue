<template>
  <div class="my-2">
    <g-gantt-chart
      :chart-start="minDate"
      :chart-end="maxDate"
      precision="month"
      bar-start="startDate"
      bar-end="endDate"
      :date-format="false"
      :bar-tooltip="false"
    >
      <template #upper-timeunit>
        <div
          class="flex items-center justify-between w-full"
          :class="{ '!bg-history': isPatientView }"
        >
          <button @click="selectedYear--" class="text-white px-2 py-1 focus:outline-none">
            {{ '<<' + previousYear }}
          </button>

          <div class="absolute left-1/2 transform -translate-x-1/2 text-center font-semibold">
            {{ selectedYear }}
          </div>

          <button
            v-if="nextYear <= currentYear"
            @click="selectedYear++"
            class="text-white px-2 py-1 focus:outline-none"
          >
            {{ nextYear + ' >>' }}
          </button>
        </div>
      </template>
      <template #timeunit="{ label }">
        <div>
          <p>{{ removeYear(label) }}</p>
        </div>
      </template>
      <g-gantt-row label="Doctor & Specialty" :bars="encounterEvents" />
      <g-gantt-row label="Problems & Care plans" :bars="problemEvents" />
      <g-gantt-row label="Med changes" :bars="medEvents" />
      <g-gantt-row label="Labs & Imaging & Procedures" :bars="labEvents"> </g-gantt-row>
      <template #bar-tooltip="{ bar }">
        <div class="custom-tooltip">
          <div><strong>Date:</strong> {{ formatDate(bar.eventDate) }} <br /></div>
          <div v-if="bar.practitioner">
            <strong>Practitioner:</strong> {{ bar.practitioner.name }} <br />
          </div>
          <div v-if="!bar.practitioner">Recorded by patient<br /></div>
          <div v-if="bar.medicineName">
            <strong>Medicine:</strong> {{ bar.medicineName }} <br />
          </div>
          <div v-if="bar.labName">{{ bar.labName }} <br /></div>
        </div>
      </template>
    </g-gantt-chart>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useEncounterStore } from '@/stores/encounter'
import { formatDate } from '@/utils/timeMethods'
import { useRoute } from 'vue-router'

const encounterStore = useEncounterStore()
const route = useRoute()
const isPatientView = computed(() => route.name === 'patient')

const currentYear = new Date().getFullYear()
const selectedYear = ref(currentYear)
const previousYear = computed(() => selectedYear.value - 1)
const nextYear = computed(() => selectedYear.value + 1)

const minDate = computed(() => new Date(selectedYear.value, 0, 1))
const maxDate = computed(() => new Date(selectedYear.value, 11, 31))

const encounterEvents = computed(() => {
  return encounterStore.patientCareEvents
    ?.filter((x) => x.type == 'Encounter')
    .map((item, index) => ({
      // startDate: new Date(item.date!),
      // endDate: new Date(new Date(item.date!).getTime() + 600 * 60 * 1000),
      startDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate(),
      ),
      endDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate() + 1,
      ),
      eventDate: new Date(item.date!),

      type: item.type,
      practitioner: item.practitioner,
      ganttBarConfig: {
        id: 'encounter_' + index,
        style: {
          background: '#' + item.practitioner?.color,
          borderRadius: '20px',
          color: 'black',
        },
      },
    }))
})

const problemEvents = computed(() => {
  return encounterStore.patientCareEvents
    ?.filter((x) => x.type == 'Problem')
    .map((item, index) => ({
      // startDate: new Date(item.date!),
      // endDate: new Date(new Date(item.date!).getTime() + 600 * 60 * 1000),
      startDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate(),
      ),
      endDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate() + 1,
      ),
      eventDate: new Date(item.date!),

      type: item.type,
      practitioner: item.practitioner,
      ganttBarConfig: {
        id: 'problem_' + index,
        style: {
          background: '#' + item.practitioner?.color,
          borderRadius: '20px',
          color: 'black',
        },
      },
    }))
})

const medEvents = computed(() => {
  return encounterStore.patientCareEvents
    ?.filter((x) => x.type == 'Medication')
    .map((item, index) => ({
      // startDate: new Date(item.date!),
      // endDate: new Date(new Date(item.date!).getTime() + 600 * 60 * 1000),
      startDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate(),
      ),
      endDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate() + 1,
      ),
      eventDate: new Date(item.date!),

      type: item.type,
      practitioner: item.practitioner,
      medicineName: item.medication?.name,
      ganttBarConfig: {
        id: 'med' + index,
        style: {
          background: '#' + item.practitioner?.color,
          borderRadius: '20px',
          color: 'black',
        },
      },
    }))
})

const labEvents = computed(() => {
  return encounterStore.patientCareEvents
    ?.filter((x) => x.type == 'Lab')
    .map((item, index) => ({
      // startDate: new Date(item.date!),
      // endDate: new Date(new Date(item.date!).getTime() + 600 * 60 * 1000),
      startDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate(),
      ),
      endDate: new Date(
        new Date(item.date!).getFullYear(),
        new Date(item.date!).getMonth(),
        new Date(item.date!).getDate() + 1,
      ),
      eventDate: new Date(item.date!),
      type: item.type,
      practitioner: item.practitioner,
      labName: item.lab?.name,
      ganttBarConfig: {
        id: 'lab_' + index,
        style: {
          background: '#' + item.practitioner?.color,
          borderRadius: '20px',
          color: 'black',
        },
      },
    }))
})

const removeYear = (label: string) => label.slice(0, -5)
</script>
<style>
.g-gantt-tooltip {
  z-index: 30;
  margin-left: -22px;
}
.g-upper-timeunit {
  height: 25px;
  background-color: theme('colors.primary.DEFAULT') !important;
  color: white !important;
}
.g-timeunits-container {
  height: 25px;
}
.g-gantt-rows-container {
  margin-top: -30px;
  z-index: 5;
}
.g-gantt-row-label {
  background-color: white !important;
  color: theme('colors.primary.DEFAULT') !important;
  border-top: 2px #ededed solid !important;
  box-shadow: none !important;
}
</style>
