<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Add Problem</h2>

      <!-- Form -->
      <form @submit.prevent="addProblem">
        <AutoCompleteFluent
          v-model="selectedCondition"
          id="condition"
          label="Condition"
          :options="conditions"
          :hasMore="hasMore"
          optionLabel="displayName"
          @search="fetchConditions"
        />
        <Select
          id="clinicalConditionStatus"
          label="Clinical Condition Status"
          :options="clinicalConditionStatuses"
        />
        <Select
          id="conditionVerificationStatus"
          label="Condition Verification Status"
          :options="conditionVerificationStatuses"
        />
        <DateTimePicker id="diagnosisDate" label="Diagnosis date" dateFormat="m/d/yy" />
        <DateTimePicker id="abatement" label="Abatement" dateFormat="m/d/yy" />
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="addProblem" class="bg-primary text-white px-4 py-2 rounded">Save</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import Select from '@/components/form-extensions/SelectFluent.vue'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import type { CodingResponse } from '@/api/api-reference'
import * as yup from 'yup'
import { ref } from 'vue'
import {
  getClinicalConditionStatuses,
  getConditionVerificationStatuses,
} from '@/utils/clinicalConditionStatuses'
import { useEncounterStore } from '@/stores/encounter'
import AutoCompleteFluent from '@/components/form-extensions/AutoCompleteFluent.vue'
import { useToast } from 'vue-toastification'

const encounterStore = useEncounterStore()
const toast = useToast()

const emit = defineEmits(['close'])
defineProps<{
  isModalOpen: boolean
}>()

const conditions = ref<CodingResponse[]>([])
const selectedCondition = ref()
const clinicalConditionStatuses = getClinicalConditionStatuses()
const conditionVerificationStatuses = getConditionVerificationStatuses()

const hasMore = ref(true)
const fetchConditions = async (query: string, page: number, reset = false) => {
  if (!query.trim()) {
    return
  }
  const conditionsResponse = await api.icd10.icd10ListIcd10({
    searchParam: query,
    pageNumber: page,
  })
  if (reset) conditions.value = []
  conditions.value = [...conditions.value, ...(conditionsResponse.data.items ?? [])]
  hasMore.value = conditions.value.length < (conditionsResponse.data.totalItems ?? 0)
}

const { handleSubmit } = useForm({
  validationSchema: yup.object({
    clinicalConditionStatus: yup.string().required(),
    conditionVerificationStatus: yup.string().required(),
    diagnosisDate: yup.string().required(),
    abatement: yup.string().nullable(),
  }),
})

const addProblem = handleSubmit(async (values) => {
  console.log(values)
  try {
    await api.patients.patientCreatePatientProblem({
      ...values,
      condition: selectedCondition.value.id,
      patientId: encounterStore.selectedEncounter!.patientId!,
      encounterId: encounterStore.selectedEncounter!.id!,
      diagnosisDate: values.diagnosisDate.toISOString(),
      abatement: values.abatement?.toISOString(),
    })
    toast.success('Problem added successfully')
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
