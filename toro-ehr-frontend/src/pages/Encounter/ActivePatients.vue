<template>
  <ResizableBox
    :title="title"
    type="active-patients"
    layout="left"
    :class="{ '!bg-history': isPatientView }"
  >
    <div class="p-4">
      <div
        v-for="encounter in encounterStore.activeEncounters"
        :key="encounter.patientId"
        class="mb-1"
      >
        <div
          class="cursor-pointer px-4 py-2 rounded-md text-white transition"
          :class="{
            'bg-white/20 shadow-sm font-bold':
              encounter.patientId === encounterStore.selectedEncounter?.patientId,
            'hover:bg-white/20':
              encounter.patientId !== encounterStore.selectedEncounter?.patientId,
          }"
          @click="selectEncounter(encounter)"
        >
          {{ encounter.fullName }}
        </div>
      </div>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '@/components/resizable/ResizableBox.vue'
import { computed, watch } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import type { EncounterResponse } from '@/api/api-reference.ts'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()
const encounterStore = useEncounterStore()
const isPatientView = computed(() => route.name === 'patient')
const title = computed(() => (isPatientView.value ? 'PATIENT' : 'PATIENTS'))

const selectEncounter = async (encounter: EncounterResponse) => {
  encounterStore.setSelectedEncounter(encounter)
  router.replace({ name: route.name!, params: { id: encounter?.id } })
}

const box = computed(
  () => encounterStore.encounterBoxes.filter((x) => x.type == 'active-patients')[0],
)

watch(
  () => encounterStore.parentContainerWidth,
  (newValue) => {
    if (newValue < 1280) {
      encounterStore.updateEncounterBoxes({
        ...box.value,
        isOpen: false,
      })
    }
  },
)
</script>
