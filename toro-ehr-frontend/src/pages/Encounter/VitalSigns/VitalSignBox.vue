<template>
  <ResizableBox title="VITALS" type="vitals">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
          <div class="p-1.5 min-w-full inline-block align-middle">
            <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
              <TableHeader>
                <template #inputs>
                  <div class="relative max-w-sm">
                    <MultiSelect
                      v-model="selectedMeasurements"
                      :options="measurements"
                      optionLabel="textLong"
                      placeholder="Select Columns"
                      :maxSelectedLabels="3"
                      class="w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                    />
                  </div>
                </template>

                <template #buttons>
                  <div class="flex items-center gap-2">
                    <a
                      v-if="!isPatientView"
                      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                      href="#"
                      @click.prevent="recordVitalsOpen = true"
                    >
                      <PlusIcon class="shrink-0 w-4 h-4" />
                      Record Vitals
                    </a>
                    <span
                      class="text-primary underline cursor-pointer text-sm flex"
                      @click="showTable = !showTable"
                    >
                      {{ showTable ? 'Show Graph' : 'Show Table' }}
                      <ArrowUpRightIcon class="size-3 m-1"
                    /></span>
                  </div>
                </template>
              </TableHeader>
              <!-- Table -->
              <table
                v-if="preliminaryVitalSigns.length > 0 && showTable"
                class="min-w-full divide-y divide-gray-200"
              >
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      v-for="measurement in selectedMeasurements"
                      :key="measurement.type"
                      scope="col"
                      class="ps-6 py-3 text-start"
                    >
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          {{ formatColumnName(measurement) }}
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="ps-6 py-3 text-start"></th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td
                      v-for="measurement in selectedMeasurements"
                      :key="measurement.type"
                      class="h-px w-72 whitespace-nowrap"
                    >
                      <div v-if="measurement.valueType == 'bloodPressure'" class="px-6 py-3">
                        <span
                          v-if="
                            preliminaryVitalSigns.filter(
                              (x) => x.type == 'SystolicBloodPressure',
                            )[0]?.value
                          "
                          class="block text-sm text-gray-800"
                          >{{
                            preliminaryVitalSigns.filter(
                              (x) => x.type == 'SystolicBloodPressure',
                            )[0]?.value
                          }}/{{
                            preliminaryVitalSigns.filter(
                              (x) => x.type == 'DiastolicBloodPressure',
                            )[0]?.value
                          }}</span
                        >
                      </div>
                      <div v-else class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          preliminaryVitalSigns.filter((x) => x.type == measurement.type)[0]?.value
                        }}</span>
                      </div>
                    </td>
                    <td class="size-px whitespace-nowrap">
                      <div class="px-6 py-1.5">
                        <a
                          class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                          href="#"
                          @click.prevent="recordVitalsOpen = true"
                        >
                          Record
                        </a>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <table
                v-if="nonpreliminaryVitalSigns.length > 0 && showTable"
                class="min-w-full divide-y divide-gray-200"
              >
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Type
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Value
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Unit
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Recorded at
                        </span>
                      </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-start">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800">
                          Recorded by
                        </span>
                      </div>
                    </th>

                    <th scope="col" class="px-6 py-3 text-end">
                      <div class="flex items-center gap-x-2">
                        <span class="text-xs font-semibold tracking-wide text-gray-800"> </span>
                      </div>
                    </th>
                  </tr>
                </thead>

                <tbody class="divide-y divide-gray-200">
                  <tr v-for="vitalSign in formattedVitals" :key="vitalSign.id">
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          getMeasurmentText(vitalSign.type!)
                        }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span
                          v-tooltip.right="formatTooltip(vitalSign)"
                          class="block text-sm text-gray-800"
                          >{{ vitalSign.value }}</span
                        >
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{
                          getMeasurmentUnit(vitalSign.type!)
                        }}</span>
                      </div>
                    </td>

                    <td class="size-px whitespace-nowrap">
                      <div class="px-6 py-1.5">
                        <span class="block text-sm text-gray-800">{{
                          formatDate(vitalSign.recordedDate)
                        }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">{{ vitalSign.recordedBy }}</span>
                      </div>
                    </td>
                    <td class="h-px w-72 whitespace-nowrap">
                      <div class="px-6 py-3">
                        <span class="block text-sm text-gray-800">
                          <Button
                            icon="pi pi-ellipsis-v"
                            variant="link"
                            @click="(e) => toggleMenu(e, vitalSign)"
                            class="p-button-danger"
                          />
                        </span>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              <!-- End Table -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <Menu ref="menuRef" :model="items" popup />

    <VitalsChart v-if="!showTable" :selectedMeasurement="selectedMeasurements" />
  </ResizableBox>
  <RecordVitals
    :isModalOpen="recordVitalsOpen"
    :selectedMeasurements="selectedMeasurements"
    :preliminaryVitalSigns="preliminaryVitalSigns"
    @close="closeRecordVitals"
  />
  <EditVital
    :measurements="measurements"
    :curentVitalSigns="currentRow"
    :isModalOpen="editVitalOpen"
    :status="currentStatus"
    @close="closeEditVital"
  />
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="cancelVitalSign"
    :title="confirmDialogTitle"
    :message="confirmDialogMessage"
  />
</template>
<script setup lang="ts">
import ResizableBox from '@/components/resizable/ResizableBox.vue'
import { computed, ref } from 'vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import TableHeader from '@/components/table/TableHeader.vue'
import { PlusIcon } from '@heroicons/vue/24/outline'
import { formatDate } from '@/utils/timeMethods'
import VitalsChart from './VitalsChart.vue'
import type { Measurement } from '@/utils/interfaces.ts'
import MultiSelect from 'primevue/multiselect'
import { getDefaultMeasurements } from '@/utils/vitalSigns.ts'
import { useRoute } from 'vue-router'
import RecordVitals from './RecordVitals.vue'
import Button from 'primevue/button'
import type { VitalSignRequest, VitalSignResponse } from '@/api/api-reference'
import Menu from 'primevue/menu'
import EditVital from './EditVital.vue'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { api } from '@/api'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
const route = useRoute()

const measurements = ref<Measurement[]>(getDefaultMeasurements())
const isPatientView = computed(() => route.name === 'patient')
const showTable = ref(true)

const encounterStore = useEncounterStore()
const selectedMeasurements = computed({
  get: () => measurements.value.filter((m) => m.selected),
  set: (selected) => {
    measurements.value.forEach((m) => {
      m.selected = selected.includes(m)
    })
  },
})

const getMeasurmentText = (type: string) => {
  const measurement = measurements.value.filter((x) => x.type == type)[0]
  return measurement?.textLong
}

const getMeasurmentUnit = (type: string) => {
  const measurement = measurements.value.filter((x) => x.type == type)[0]
  return measurement?.unit
}

const formatColumnName = (measurement: Measurement) => {
  let columnName = measurement.textShort
  if (measurement.unit) {
    columnName += ' (' + measurement.unit + ')'
  }
  return columnName
}

const formatTooltip = (vitalSign: VitalSignResponse) => {
  if (vitalSign.status == 'amended') {
    return `Amended ${formatDate(vitalSign.recordedDate)} by ${vitalSign.modifiedBy}`
  }
  if (vitalSign.status == 'corrected') {
    return `Corrected ${formatDate(vitalSign.recordedDate)} by ${vitalSign.modifiedBy}`
  }
}

const menuRef = ref()
const currentRow = ref<VitalSignResponse | null>(null)
const currentStatus = ref('')
const confirmDialog = ref()
const confirmDialogTitle = ref('')
const confirmDialogMessage = ref('')

const items = ref([
  {
    label: 'Amend',
    command: () => {
      currentStatus.value = 'amended'
      editVitalOpen.value = true
    },
  },
  {
    label: 'Corect',
    command: () => {
      currentStatus.value = 'corrected'
      editVitalOpen.value = true
    },
  },
  {
    label: 'Cancel',
    command: () => {
      currentStatus.value = 'cancel'
      openConfirmModal()
    },
  },
  {
    label: 'Set entered in error',
    command: () => {
      currentStatus.value = 'entered-in-error'
      openConfirmModal()
    },
  },
])

const toggleMenu = (event: MouseEvent, row: VitalSignResponse) => {
  currentRow.value = row
  menuRef.value.toggle(event)
}

const closeEditVital = () => {
  currentRow.value = null
  currentStatus.value = ''
  editVitalOpen.value = false
}

const openConfirmModal = () => {
  confirmDialogTitle.value =
    currentStatus.value == 'cancel' ? 'Cancel Vital Sign' : 'Set entered in error'
  confirmDialogMessage.value =
    currentStatus.value == 'cancel'
      ? `Are you sure you want to cancel vital sign?`
      : 'Are you sure you want to set entered in error status?'
  confirmDialog.value?.open(0)
}

const cancelVitalSign = async () => {
  try {
    const vitals: VitalSignRequest[] = []
    if (currentRow.value) {
      if (currentRow.value!.type == 'BloodPressure') {
        const [systolic, diastolic] = currentRow.value!.value!.split('/').map((v) => v.trim())
        const [systolicId, diastolicId] = currentRow.value!.id!.split('/').map((v) => v.trim())
        vitals.push({ id: systolicId, type: 'SystolicBloodPressure', value: systolic })
        vitals.push({ id: diastolicId, type: 'DiastolicBloodPressure', value: diastolic })
      } else {
        vitals.push({
          id: currentRow.value.id,
          type: currentRow.value!.type,
          value: currentRow.value!.value,
        })
      }
    }

    await api.encounter.encounterAddVitalSigns({
      vitals: vitals,
      patientId: encounterStore.selectedEncounter!.patientId!,
      encounterId: encounterStore.selectedEncounter?.id,
      status: currentStatus.value,
    })

    await encounterStore.getPatientVitalSigns(encounterStore.selectedEncounter!.patientId!)
  } catch (error) {
    console.log(error)
  }
}

const recordVitalsOpen = ref(false)
const editVitalOpen = ref(false)

const vitalSigns = computed(() => encounterStore.vitalSigns)

const preliminaryVitalSigns = computed(() => {
  return vitalSigns.value?.filter((vitalSign) => vitalSign.status == 'preliminary') ?? []
})
const nonpreliminaryVitalSigns = computed(() => {
  return vitalSigns.value?.filter((vitalSign) => vitalSign.status != 'preliminary') ?? []
})

const formattedVitals = computed(() => {
  const grouped = new Map<string, VitalSignResponse[]>()

  for (const vital of nonpreliminaryVitalSigns.value) {
    const key = vital.createdAt!
    if (!grouped.has(key)) {
      grouped.set(key, [])
    }
    grouped.get(key)!.push(vital)
  }

  const result: VitalSignResponse[] = []

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  for (const [createdAt, vitals] of grouped.entries()) {
    const systolic = vitals.find((v) => v.type === 'SystolicBloodPressure')
    const diastolic = vitals.find((v) => v.type === 'DiastolicBloodPressure')

    if (systolic || diastolic) {
      result.push({
        ...systolic,
        type: 'BloodPressure',
        id: `${systolic?.id ?? ''}/${diastolic?.id ?? ''}`,
        value: `${systolic?.value ?? ''}/${diastolic?.value ?? ''}`,
      })
    }

    for (const vital of vitals) {
      if (vital.type !== 'SystolicBloodPressure' && vital.type !== 'DiastolicBloodPressure') {
        result.push(vital)
      }
    }
  }

  return result
})

const closeRecordVitals = () => {
  encounterStore.getPatientVitalSigns(encounterStore.selectedEncounter!.patientId!)
  recordVitalsOpen.value = false
}
</script>
