<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Dashboard
    </h2>
    <TableSection>
      <TableHeader>
        <template #buttons>
          <Select
            v-model="selectedFilter"
            :options="filterOptions"
            optionLabel="text"
            option-value="value"
            placeholder="Filter"
            class="w-full md:w-56"
            @change="filter"
          />
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Patient
                </span>
              </div>
            </th>

            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Doctor
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Date
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Time
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Status
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in itemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{ row.patientName }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{
                  row.practitionerName
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(
                    row.startAt!,
                    authStore.user!.timeZone ?? 'local',
                  ).formattedDate
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(
                    row.startAt!,
                    authStore.user!.timeZone ?? 'local',
                  ).formattedTime
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getEncounterStatusClasses(row.status)"
                >
                  <span
                    class="w-1.5 h-1.5 rounded-full mr-1.5"
                    :class="getEncounterStatusDotClass(row.status)"
                  ></span>
                  {{ row.status }}
                </span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4">
                <a
                  v-show="getActionLabel(row.status!)"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="handleStatusAction(row.id!, row.status!)"
                >
                  {{ getActionLabel(row.status!) }}
                </a>
                <a
                  v-show="
                    row.status === EncounterStatus.InProgress &&
                    isEncounterOwner(row.practitionerId!)
                  "
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  @click.prevent="router.push({ name: 'encounters', params: { id: row.id! } })"
                >
                  Open Encounter
                </a>
                <a
                  v-show="row.status === EncounterStatus.InProgress && row.hasNotes"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  @click.prevent="
                    router.push({ name: 'checkout', params: { encounterId: row.id! } })
                  "
                >
                  Checkout
                </a>
                <a
                  v-show="
                    row.status === EncounterStatus.Planned ||
                    row.status === EncounterStatus.CheckedIn ||
                    row.status === EncounterStatus.Arrived
                  "
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  @click="confirmCancelAppointment($event, row.appointmentId!)"
                >
                  Cancel
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
    <ConfirmDialog
      ref="confirmDialog"
      @confirmedAction="startEncounter"
      title="Start Encounter"
      :message="`Are you sure you want to start encounter?`"
    />
    <!-- Confirm Popup for Cancel Appointment -->
    <ConfirmPopup />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api'
import type { ActiveEncounterResponse, SelectListItem } from '../api/api-reference'
import TableHeader from '../components/table/TableHeader.vue'
import TableFooter from '../components/table/TableFooter.vue'
import TableSection from '../components/table/TableSection.vue'
import {
  getEncounterStatusClasses,
  getEncounterStatusDotClass,
  getEnumNameFromValue,
  getEnumKey,
} from '@/utils/statusColors'
import { EncounterStatus } from '@/api/api-reference'
import router from '../router'
import ConfirmDialog from '@/components/form-extensions/ConfirmInfoDialog.vue'
import Select from 'primevue/select'
import { useAuthStore } from '@/stores/auth.ts'
import { formatDateTimeInLocationTimeZone } from '@/utils/timeMethods'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'vue-toastification'
import ConfirmPopup from 'primevue/confirmpopup'

const authStore = useAuthStore()
const confirm = useConfirm()
const toast = useToast()

const limit = 10
const itemList = ref<ActiveEncounterResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

const confirmDialog = ref()

const selectedFilter = ref('All')
const filterOptions = ref<SelectListItem[]>([
  {
    text: 'All',
    value: 'All',
  },
  {
    text: 'Mine',
    value: 'Mine',
  },
])

onMounted(async () => {
  await fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = async () => {
  if (!isLastPage.value) {
    pageNumber.value++
    await fetchData()
  }
}

const prevPage = async () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    await fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.encounter.encounterGetActiveEncounters({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
      showAll: selectedFilter.value === 'All',
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching appointments:', error)
  }
}

const filter = async () => {
  await fetchData()
}

const isEncounterOwner = (encounterPractitionerId: string) => {
  return authStore.user?.employeeId === encounterPractitionerId
}

function getActionLabel(status: string): string {
  // Convert enum value to enum name for consistent handling
  const enumName = getEnumNameFromValue(status)

  switch (enumName) {
    case 'Planned':
      return 'Check in'
    case 'CheckedIn':
      return 'Arrived'
    case 'Arrived':
      return 'Start'
    default:
      return ''
  }
}

async function handleStatusAction(encounterId: string, status: string) {
  // Convert enum value to enum name for consistent handling
  const enumName = getEnumNameFromValue(status)

  switch (enumName) {
    case 'Planned':
      await router.push({ name: 'checkin-questionnaires', params: { encounterId: encounterId } })
      break
    case 'CheckedIn':
      await updateEncounterStatus(
        encounterId,
        getEnumKey(EncounterStatus, EncounterStatus.Arrived)!,
      )
      break
    case 'Arrived':
      confirmDialog.value.open(encounterId)
      break
  }
}

async function startEncounter(encounterId: string) {
  await updateEncounterStatus(encounterId, getEnumKey(EncounterStatus, EncounterStatus.InProgress)!)
}

async function updateEncounterStatus(encounterId: string, newStatus: string) {
  try {
    await api.encounter.encounterUpdateStatus({
      encounterId: encounterId,
      status: newStatus,
    })
    // refresh data after successful status update
    await fetchData()
  } catch (e) {
    console.error('Error updating encounter status:', e)
    // still refresh data to ensure UI is in sync
    await fetchData()
  }
}

const confirmCancelAppointment = (event: Event, appointmentId: string) => {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: 'Are you sure you want to cancel this appointment?',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'No',
      severity: 'secondary',
      outlined: true,
    },
    acceptProps: {
      label: 'Yes, Cancel',
      severity: 'danger',
    },
    accept: () => {
      cancelAppointment(appointmentId)
    },
  })
}

const cancelAppointment = async (appointmentId: string) => {
  try {
    await api.appointments.appointmentCancelAppointment({ id: appointmentId })
    toast.success('Appointment cancelled successfully.')
    await fetchData()
  } catch (error) {
    console.error(error)
    toast.error('Failed to cancel appointment.')
  }
}
</script>
