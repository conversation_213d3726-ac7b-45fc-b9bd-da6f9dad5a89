<template>
  <div class="organization-page">
    <div class="mx-4 sm:mx-8">
      <h2 class="border-b py-6 text-4xl font-semibold">Profile</h2>
      <!-- Mobile Dropdown Menu -->
      <div class="relative mb-4 sm:hidden">
        <input class="peer hidden" type="checkbox" id="dropdown-toggle" />
        <label
          for="dropdown-toggle"
          class="flex w-full cursor-pointer select-none rounded-lg border p-2 px-3 text-sm text-gray-700 ring-toroblue-600 peer-checked:ring"
        >
          {{ getSelectedSectionLabel }}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="ml-auto h-4 text-slate-700 transition peer-checked:rotate-180"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
          </svg>
        </label>
        <ul
          class="max-h-0 select-none flex-col overflow-hidden rounded-b-lg shadow-md transition-all duration-300 peer-checked:max-h-56 peer-checked:py-3 absolute w-full bg-white z-10 border border-t-0"
        >
          <li
            v-for="section in sections"
            :key="section.id"
            class="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-toroblue-600 hover:text-white"
            @click="selectSection(section.id)"
          >
            {{ section.label }}
          </li>
        </ul>
      </div>

      <div class="flex pt-3 gap-4">
        <!-- Sidebar Navigation (Visible on Larger Screens) -->
        <div class="w-48 flex-shrink-0 hidden sm:block">
          <ul>
            <li
              v-for="section in sections"
              :key="section.id"
              class="mt-5 cursor-pointer border-l-2 px-2 py-2 font-semibold transition"
              :class="getActiveClass(section.id)"
              @click="selectSection(section.id)"
            >
              {{ section.label }}
            </li>
          </ul>
        </div>

        <!-- Content Area -->
        <div class="w-full sm:flex-1 overflow-hidden rounded-xl sm:bg-gray-50 sm:px-4 sm:shadow">
          <div v-if="selectedSection">
            <div class="pt-4">
              <div class="flex items-center justify-between">
                <h1 class="py-2 text-2xl font-semibold">{{ getSelectedSectionLabel }}</h1>
                <div class="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
                  <p class="text-blue-800 text-sm font-medium">
                    Please complete your profile prior to your appointment.
                  </p>
                </div>
              </div>
            </div>
            <Divider />
            <component :is="selectedSectionComponent" ref="currentSectionRef" />
            <NextButton
              @save-and-next="saveAndGoToNextSection"
              v-if="hasNextSection && shouldShowSaveAndNext"
            />
            <div v-else-if="hasNextSection" class="flex justify-end mt-6 mb-4">
              <Button
                label="Next"
                icon="pi pi-arrow-right"
                iconPos="right"
                @click="goToNextSection"
                class="px-6 py-3"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import {
  ref,
  onMounted,
  computed,
  defineAsyncComponent,
  shallowRef,
  onBeforeMount,
  watch,
} from 'vue'
import Divider from 'primevue/divider'
import Button from 'primevue/button'
import { usePatientStore } from '@/stores/patient.ts'
import { api } from '@/api'
import NextButton from '@/components/common/NextButton.vue'

const patientStore = usePatientStore()

const route = useRoute()
const router = useRouter()
const currentSectionRef = ref()

onBeforeMount(async () => {
  await patientStore.getPatientProfile()
})

// Define sections to avoid repetition
const sections = shallowRef([
  {
    id: 'personal-info',
    label: 'Personal Information',
    component: defineAsyncComponent(() => import('./PersonalInfo.vue')),
  },
  {
    id: 'contact-info',
    label: 'Contact Information',
    component: defineAsyncComponent(() => import('./ContactInfo.vue')),
  },
  {
    id: 'insurance-info',
    label: 'Insurance Information',
    component: defineAsyncComponent(() => import('./InsuranceInfo.vue')),
  },
  {
    id: 'medications',
    label: 'Medications',
    component: defineAsyncComponent(() => import('./Medications.vue')),
  },
  {
    id: 'allergies',
    label: 'Allergies',
    component: defineAsyncComponent(() => import('./Allergies.vue')),
  },
  {
    id: 'immunizations',
    label: 'Immunizations',
    component: defineAsyncComponent(() => import('./Immunizations.vue')),
  },
])

const selectedSection = ref(route.params.section || 'personal-info')

// Watch route changes and update selectedSection
watch(
  () => route.params.section,
  (newSection) => {
    if (sections.value.some((s) => s.id === newSection)) {
      selectedSection.value = newSection
    } else {
      router.replace({ name: 'patient-profile', params: { section: 'personal-info' } })
    }
  },
)

// Function to change the route when selecting a section
const selectSection = (sectionId: string) => {
  router.push({ name: 'patient-profile', params: { section: sectionId } })
}

const getSelectedSectionLabel = computed(() => {
  const section = sections.value.find((s) => s.id === selectedSection.value)
  return section ? section.label : 'Select Section'
})

const selectedSectionComponent = computed(() => {
  const section = sections.value.find((s) => s.id === selectedSection.value)
  return section ? section.component : null
})

// Function to determine active class for sidebar
const getActiveClass = (sectionId: string) => {
  return selectedSection.value === sectionId
    ? 'border-l-primary text-primary'
    : 'border-transparent'
}

// Function to navigate to the next section
const goToNextSection = () => {
  const currentIndex = sections.value.findIndex((s) => s.id === selectedSection.value)
  if (currentIndex < sections.value.length - 1) {
    // Go to next section
    selectSection(sections.value[currentIndex + 1].id)
  } else {
    // If we're at the last section, go to questionnaires or appointments
    router.push('/patient/questionnaires')
  }
}

// Function to save current section and navigate to next
const saveAndGoToNextSection = async () => {
  try {
    // Only call save for sections that have meaningful save operations
    if (
      shouldShowSaveAndNext.value &&
      currentSectionRef.value &&
      typeof currentSectionRef.value.save === 'function'
    ) {
      await currentSectionRef.value.save()
    }
    // After successful save, navigate to next section
    goToNextSection()
  } catch (error) {
    console.error('Error saving section:', error)
    // Don't navigate if save failed
  }
}

// Computed property to check if there's a next section
const hasNextSection = computed(() => {
  const currentIndex = sections.value.findIndex((s) => s.id === selectedSection.value)
  return currentIndex < sections.value.length
})

// Sections that have meaningful save operations and should show "Save & Next"
const sectionsWithSave = ['personal-info', 'contact-info']

// Computed property to check if current section should show "Save & Next" button
const shouldShowSaveAndNext = computed(() => {
  return sectionsWithSave.includes(selectedSection.value)
})
</script>
