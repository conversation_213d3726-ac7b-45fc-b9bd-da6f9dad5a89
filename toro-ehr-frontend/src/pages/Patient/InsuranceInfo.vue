<template>
  <div class="mb-4">
    <div class="flex flex-col">
      <div class="-m-1.5 overflow-x-auto">
        <div class="p-1.5 min-w-full inline-block align-middle">
          <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
            <TableHeader>
              <template #inputs>
                <Checkbox v-model="hasInsurance" inputId="insurance" :disabled="!isInsuranceListEmpty" binary/>
                <label for="insurance"> I have no insurance </label>
              </template>
              <template #buttons>
                <Button v-if="!hasInsurance" icon="pi pi-plus" @click="openEditModal(undefined)" label="Add Coverage"/>
              </template>
            </TableHeader>
            <!-- Table -->
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Order
                </span>
                  </div>
                </th>
                <th scope="col" class="ps-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                   Issuer
                </span>
                  </div>
                </th>

                <th scope="col" class="ps-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Group/Member number
                </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Start/End
                </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Coverage type
                </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-start">
                  <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Relation
                </span>
                  </div>
                </th>

                <th scope="col" class="px-6 py-3 text-end"></th>
              </tr>
              </thead>

              <tbody class="divide-y divide-gray-200">
              <tr v-for="row in patientInsurances" :key="row.id">
                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <Tag :severity="row.order === 'Primary' ? 'success' : 'info'" >{{ row.order }}</Tag>
                  </div>
                </td>
                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm font-semibold text-gray-500">{{ row.issuer }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm font-semibold text-gray-500">{{ row.groupId }}</span>
                    <span class="block text-sm font-semibold text-gray-500">{{ row.memberId }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm text-gray-500">{{ row.start ? formatDateTime(row.start).formattedDate : 'N/A' }}</span>
                    <span class="block text-sm text-gray-500">{{ row.end ? formatDateTime(row.end).formattedDate : 'N/A' }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm font-semibold text-gray-500">{{ row.type }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm font-semibold text-gray-500">{{ row.relationship }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                  <div class="flex items-right gap-x-4">
                    <Button
                      v-tooltip.top="'Details'"
                      icon="pi pi-eye"
                      variant="text"
                      severity="info"
                      @click.prevent="openEditModal(row)"/>
                    <Button
                      v-tooltip.top="'Delete'"
                      icon="pi pi-trash"
                      variant="text"
                      severity="danger"
                      @click="confirmDialog?.open(row.id)"/>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
            <EditCoverage
              :isModalOpen="isModalOpen"
              :insurance="selectedRow"
              :existingInsurances="patientInsurances"
              @close="closeModal"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="deleteInsurance"
    title="Delete Insurance"
    message="Are you sure you want to delete this insurance from your list?"
  />
</template>

<style scoped>

</style>

<script setup lang="ts">
import {ref, watchEffect, computed} from "vue";
import EditCoverage from "@/pages/Patient/EditCoverage.vue";
import type {PatientInsuranceResponse} from "@/api/api-reference.ts";
import TableHeader from '../../components/table/TableHeader.vue'
import Tag from 'primevue/tag';
import Button from "primevue/button";
import Checkbox from "primevue/checkbox";
import {api} from "@/api";
import ConfirmDialog from "@/components/form-extensions/ConfirmDialog.vue";
import {useToast} from "vue-toastification";

const toast = useToast();
const isModalOpen = ref(false)
const selectedRow = ref<PatientInsuranceResponse | null>(null)
const hasInsurance = ref(false)
const patientInsurances = ref<PatientInsuranceResponse[]>([]);

const openEditModal = (insurance: PatientInsuranceResponse | undefined) => {
  if (insurance) {
    selectedRow.value = insurance
  } else {
    selectedRow.value = null
  }
  isModalOpen.value = true
}

const confirmDialog = ref<any | null>(null)

const isInsuranceListEmpty = computed(() => patientInsurances.value.length === 0);

const closeModal = () => {
  fetchInsurances()
  isModalOpen.value = false
}

const fetchInsurances = async () => {
  try {
    const response = await api.patients.patientListInsurances();
    // API returns single object, but we need array for UI
    patientInsurances.value = Array.isArray(response.data) ? response.data : [response.data].filter(Boolean);
  } catch (error) {
    console.log(error)
    toast.error("Failed to load insurance information")
  }
}

watchEffect(async () => {
  await fetchInsurances()
});

const formatDateTime = (dateTimeOffset: string) => {
  const date = new Date(dateTimeOffset);

  const formattedDate = date.toISOString().split('T')[0]; // Extract YYYY-MM-DD
  const formattedTime = date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });

  return {formattedDate, formattedTime};
};

const deleteInsurance = async (id: string) => {
  try {
    await api.patients.patientDeleteInsurance({patientInsuranceId: id})
    toast.success("Insurance coverage deleted successfully")
    await fetchInsurances();
  } catch (error) {
    console.log(error)
    toast.error("Failed to delete insurance coverage")
  }
}

</script>
