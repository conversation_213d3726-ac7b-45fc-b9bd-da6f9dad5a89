<template>
  <div class="py-2">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      <div class="col-span-2">
        <div class="my-3">
          <AutoCompleteFluent
            v-model="selectedImmunization"
            id="selectedImmunization"
            label="Search immunizations"
            :options="immunizationOptions"
            :hasMore="hasMore"
            optionLabel="displayName"
            @search="fetchImmuznizations"
          />
        </div>
      </div>
      <div class="col-span-1">
        <div class="my-3">
          <FloatLabel variant="on">
            <DateTimePicker
              v-model="immunizationDate"
              id="immunizationDate"
              hourFormat="12"
              dateFormat="m/d/yy"
            />
            <label :for="immunizationDate">Immunization Date</label>
          </FloatLabel>
        </div>
      </div>
      <div class="col-span-1 flex items-center">
        <Button icon="pi pi-plus" label="Add" @click="addImmunization" />
      </div>
    </div>
  </div>
  <!-- Medication Table -->
  <div class="flex flex-col">
    <div class="-m-1.5 overflow-x-auto">
      <div class="p-1.5 min-w-full inline-block align-middle">
        <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-start">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Description
                  </span>
                </th>

                <th scope="col" class="px-6 py-3 text-start">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Date
                  </span>
                </th>

                <th scope="col" class="px-6 py-3 text-end"></th>
              </tr>
            </thead>

            <tbody class="divide-y divide-gray-200">
              <tr v-for="(med, index) in patientImmunizations" :key="med.displayName">
                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm text-gray-500">{{ med.displayName }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap">
                  <div class="px-6 py-3">
                    <span class="block text-sm text-gray-500">{{ med.date }}</span>
                  </div>
                </td>

                <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                  <div class="flex items-right gap-x-4">
                    <Button
                      icon="pi pi-trash"
                      variant="text"
                      severity="danger"
                      @click="confirmDialog?.open(med.id)"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="removeImmunization"
    title="Delete Immunization"
    message="Are you sure you want to delete this immunization from your list?"
  />
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Button from 'primevue/button'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import { api } from '@/api'
import type {
  AddPatientImmunizationCommand,
  PatientImmunizationResponse,
} from '@/api/api-reference.ts'
import DateTimePicker from 'primevue/datepicker'
import { useToast } from 'vue-toastification'
import AutoCompleteFluent from '../../components/form-extensions/AutoCompleteFluent.vue'

onMounted(() => {
  fetchPatientImmunizations()
})

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const confirmDialog = ref<any | null>(null)

const toast = useToast()

const selectedImmunization = ref()
const immunizationOptions = ref()
const immunizationDate = ref()
const patientImmunizations = ref<PatientImmunizationResponse[]>([])

const hasMore = ref(true)
const fetchImmuznizations = async (query: string, page: number, reset = false) => {
  const immunizationsResponse = await api.immunizations.immunizationListImmunizations({
    searchParam: query,
    pageNumber: page,
  })

  if (reset) immunizationOptions.value = []
  immunizationOptions.value = [
    ...immunizationOptions.value,
    ...(immunizationsResponse.data.items?.map((x) => ({
      displayName: `${x.displayName}`,
      code: `${x.code}`,
    })) ?? []),
  ]
  hasMore.value = immunizationOptions.value.length < (immunizationsResponse.data.totalItems ?? 0)
}

const fetchPatientImmunizations = async () => {
  patientImmunizations.value = (await api.patients.patientListImmunizations()).data
}

const addImmunization = async () => {
  try {
    const data = {
      code: selectedImmunization?.value?.code,
      displayName: selectedImmunization?.value?.displayName,
      date: formatDateTime(immunizationDate?.value).formattedDate,
    } as AddPatientImmunizationCommand

    await api.patients.patientAddImmunization(data)

    selectedImmunization.value = undefined
    await fetchPatientImmunizations()
    toast.success('Immunization added successfully')
  } catch (error) {
    console.error('Error adding immunization:', error)
    toast.error('Failed to add immunization')
  }
}

const removeImmunization = async (id: string) => {
  try {
    await api.patients.patientDeleteImmunization({ id })
    await fetchPatientImmunizations()
    toast.success('Immunization removed successfully')
  } catch (error) {
    console.error('Error removing immunization:', error)
    toast.error('Failed to remove immunization')
  }
}

const formatDateTime = (dateTimeOffset: string) => {
  const date = new Date(dateTimeOffset)

  const formattedDate = date.toISOString().split('T')[0] // Extract YYYY-MM-DD
  const formattedTime = date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  })

  return { formattedDate, formattedTime }
}
</script>
