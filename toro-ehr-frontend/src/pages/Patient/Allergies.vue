<template>
  <div class="py-2">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      <div class="col-span-1">
        <div class="my-3">
          <AutoCompleteFluent
            v-model="selectedAllergy"
            id="selectedAllergy"
            label="Search allergies"
            :options="allergyOptions"
            :hasMore="hasMore"
            optionLabel="displayName"
            @search="fetchAllergies"
          />
        </div>
      </div>
      <div class="col-span-1">
        <div class="my-3">
          <FloatLabel variant="on">
            <InputText
              :id="selectedAllergyReaction"
              v-model="selectedAllergyReaction"
              class="w-full"
            />
            <label :for="selectedAllergyReaction">Reaction</label>
          </FloatLabel>
        </div>
      </div>
      <div class="col-span-1">
        <div class="my-3">
          <FloatLabel variant="on">
            <Select
              :inputId="selectedAllergySeverity"
              v-model="selectedAllergySeverity"
              optionLabel="text"
              option-value="value"
              :options="severityOptions"
              class="w-full"
            />
            <label :for="selectedAllergySeverity">Severity</label>
          </FloatLabel>
        </div>
      </div>
      <div class="col-span-1 flex items-center">
        <Button icon="pi pi-plus" label="Add Allergy" @click="addAllergy" class="w-full" />
      </div>
    </div>
    <!-- Medication Table -->
    <div class="flex flex-col">
      <div class="-m-1.5 overflow-x-auto">
        <div class="p-1.5 min-w-full inline-block align-middle">
          <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="ps-6 py-3 text-start">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                      Description
                    </span>
                  </th>

                  <th scope="col" class="ps-6 py-3 text-start">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                      Reaction
                    </span>
                  </th>

                  <th scope="col" class="px-6 py-3 text-start">
                    <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                      Severity
                    </span>
                  </th>

                  <th scope="col" class="px-6 py-3 text-end"></th>
                </tr>
              </thead>

              <tbody class="divide-y divide-gray-200">
                <tr v-for="(med, index) in patientAllergies" :key="med.code">
                  <td class="h-px w-72 whitespace-nowrap">
                    <div class="px-6 py-3">
                      <span class="block text-sm text-gray-500">{{ med.displayName }}</span>
                    </div>
                  </td>

                  <td class="h-px w-72 whitespace-nowrap">
                    <div class="px-6 py-3">
                      <span class="block text-sm text-gray-500">{{ med.reaction }}</span>
                    </div>
                  </td>

                  <td class="h-px w-72 whitespace-nowrap">
                    <div class="px-6 py-3">
                      <span class="block text-sm text-gray-500">{{ med.severity }}</span>
                    </div>
                  </td>

                  <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                    <div class="flex items-right gap-x-4">
                      <Button
                        icon="pi pi-trash"
                        variant="text"
                        severity="danger"
                        @click="confirmDialog?.open(index)"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="removeAllergy"
    title="Delete Allergy"
    message="Are you sure you want to delete this allergy from your list?"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import Button from 'primevue/button'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import { api } from '@/api'
import { usePatientStore } from '@/stores/patient.ts'
import Select from 'primevue/select'
import InputText from 'primevue/inputtext'
import type { PatientAllergyResponse } from '@/api/api-reference.ts'
import { useToast } from 'vue-toastification'
import AutoCompleteFluent from '../../components/form-extensions/AutoCompleteFluent.vue'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const confirmDialog = ref<any | null>(null)

const patientStore = usePatientStore()
const toast = useToast()

const selectedAllergy = ref()
const selectedAllergyReaction = ref(undefined)
const selectedAllergySeverity = ref(undefined)
const allergyOptions = ref()
const patientAllergies = ref<PatientAllergyResponse[]>([])

onMounted(() => {
  if (patientStore.patientProfile) {
    patientAllergies.value = patientStore.patientProfile!.allergies ?? []
  }
})

const severityOptions = ref([
  { text: 'Mild', value: 'Mild' },
  { text: 'Moderate', value: 'Moderate' },
  { text: 'Severe', value: 'Severe' },
])

const hasMore = ref(true)
const fetchAllergies = async (query: string, page: number, reset = false) => {
  const response = await api.allergies.allergieListAllergiess({
    searchParam: query,
    pageNumber: page,
  })

  if (reset) allergyOptions.value = []
  allergyOptions.value = [
    ...allergyOptions.value,
    ...(response.data.items?.map((x) => ({
      displayName: `${x.displayName}`,
      code: `${x.code}`,
    })) ?? []),
  ]
  hasMore.value = allergyOptions.value.length < (response.data.totalItems ?? 0)
}

const addAllergy = async () => {
  const newPatientAllergies = [
    ...patientAllergies.value,
    {
      code: selectedAllergy.value.code,
      displayName: selectedAllergy.value.displayName,
      reaction: selectedAllergyReaction.value,
      severity: selectedAllergySeverity.value,
    },
  ]
  try {
    await api.patients.patientSetAllergies(newPatientAllergies)
    selectedAllergy.value = undefined
    selectedAllergyReaction.value = undefined
    selectedAllergySeverity.value = undefined
    toast.success('Allergy added successfully')
  } catch (e) {
    console.log(e)
    toast.error('Failed to add allergy')
  } finally {
    await patientStore.getPatientProfile()
  }
}

const removeAllergy = async (index: number) => {
  try {
    patientAllergies.value!.splice(index, 1)
    await api.patients.patientSetAllergies(patientAllergies.value!)
    await patientStore.getPatientProfile()
    toast.success('Allergy removed successfully')
  } catch (error) {
    console.error('Error removing allergy:', error)
    toast.error('Failed to remove allergy')
  }
}

watch(
  () => patientStore.patientProfile,
  (patient) => {
    if (patient) {
      patientAllergies.value = patient.allergies ?? []
    }
  },
)
</script>
