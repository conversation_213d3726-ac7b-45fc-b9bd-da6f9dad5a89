<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white rounded shadow-md w-full max-w-4xl max-h-[90vh] overflow-hidden relative">
      <!-- Close button -->
      <button
        @click="$emit('close')"
        class="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
        type="button"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          ></path>
        </svg>
      </button>

      <div class="p-6">
        <h2 class="text-lg font-bold mb-6 pr-8">Add Coverage</h2>
        <div class="overflow-y-auto max-h-[75vh]">
          <!-- Form -->
          <form @submit.prevent="addCoverage">
            <fieldset :disabled="!isNew">
              <!-- Two-column layout for main fields -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="space-y-4">
                  <InputText id="issuer" label="Issuer" />
                  <InputText id="groupId" label="Group Id" />
                  <InputText id="memberId" label="Member Id" />
                  <DateTimePicker :disabled="!isNew" id="start" label="Start" dateFormat="m/d/yy" />
                  <DateTimePicker id="end" label="End" dateFormat="m/d/yy" />
                </div>
                <div class="space-y-4">
                  <Select :disabled="!isNew" id="order" label="Order" :options="orderOptions" />
                  <small
                    v-if="isNew && orderOptions.length < allOrderOptions.length"
                    class="text-gray-500 text-sm -mt-2 block"
                  >
                    Note: Some order types are already in use. Only one Primary, Secondary, and
                    Tertiary insurance allowed.
                  </small>
                  <Select :disabled="!isNew" id="type" label="Type" :options="typeOptions" />
                  <InputNumber id="copay" label="Copay" />
                  <InputNumber id="deductible" label="Deductible" />
                </div>
              </div>
              <!-- Relationship section -->
              <Fieldset class="mb-6">
                <template #legend>
                  <label class="bg-white text-sm text-gray-700"> Relationship to Insured </label>
                </template>
                <div class="flex items-center gap-6">
                  <div class="flex items-center gap-2">
                    <RadioButton
                      v-model="relationship"
                      inputId="Self"
                      name="relationship"
                      value="Self"
                    />
                    <label for="Self">Self</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <RadioButton
                      v-model="relationship"
                      inputId="Spouse"
                      name="relationship"
                      value="Spouse"
                    />
                    <label for="Spouse">Spouse</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <RadioButton
                      v-model="relationship"
                      inputId="Child"
                      name="relationship"
                      value="Child"
                    />
                    <label for="Child">Child</label>
                  </div>
                  <div class="flex items-center gap-2">
                    <RadioButton
                      v-model="relationship"
                      inputId="Other"
                      name="relationship"
                      value="Other"
                    />
                    <label for="Other">Other</label>
                  </div>
                </div>
              </Fieldset>

              <!-- Subscriber details (when not Self) -->
              <div v-if="relationship !== 'Self'" class="mb-6">
                <h3 class="text-md font-semibold mb-4 text-gray-700">Subscriber Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="space-y-4">
                    <InputText id="subscriber.firstName" label="First Name" />
                    <InputText id="subscriber.lastName" label="Last Name" />
                  </div>
                  <div class="space-y-4">
                    <DateTimePicker id="subscriber.birthday" label="Birthday" dateFormat="m/d/yy" />
                    <Fieldset>
                      <template #legend>
                        <label class="bg-white text-sm text-gray-700"> Birth Sex </label>
                      </template>
                      <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                          <RadioButton
                            v-model="birthSex"
                            inputId="Female"
                            name="birthSex"
                            value="Female"
                          />
                          <label for="Female">Female</label>
                        </div>
                        <div class="flex items-center gap-2">
                          <RadioButton
                            v-model="birthSex"
                            inputId="Male"
                            name="birthSex"
                            value="Male"
                          />
                          <label for="Male">Male</label>
                        </div>
                        <div class="flex items-center gap-2">
                          <RadioButton
                            v-model="birthSex"
                            inputId="ReOther"
                            name="birthSex"
                            value="Other"
                          />
                          <label for="ReOther">Other</label>
                        </div>
                      </div>
                    </Fieldset>
                  </div>
                </div>
              </div>
            </fieldset>
          </form>

          <!-- Card files section -->
          <div v-if="isNew" class="mb-6">
            <Fieldset>
              <template #legend>
                <label class="bg-white text-sm text-gray-700"> Card Files </label>
              </template>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Front File Upload -->
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">Front of Card</label>
                  <div class="flex items-center gap-3">
                    <FileUpload
                      mode="basic"
                      chooseLabel="Choose Front"
                      custom-upload
                      auto
                      accept="image/*,application/pdf"
                      @select="handleFileSelect($event, 'front')"
                      class="flex-shrink-0"
                    />
                    <span v-if="selectedFileFront" class="text-gray-600 text-sm truncate">
                      {{ selectedFileFront.name }}
                    </span>
                    <span v-else class="text-gray-400 text-sm">No file chosen</span>
                  </div>
                </div>

                <!-- Back File Upload -->
                <div class="space-y-2">
                  <label class="text-sm font-medium text-gray-700">Back of Card</label>
                  <div class="flex items-center gap-3">
                    <FileUpload
                      mode="basic"
                      chooseLabel="Choose Back"
                      custom-upload
                      auto
                      accept="image/*,application/pdf"
                      @select="handleFileSelect($event, 'back')"
                      class="flex-shrink-0"
                    />
                    <span v-if="selectedFileBack" class="text-gray-600 text-sm truncate">
                      {{ selectedFileBack.name }}
                    </span>
                    <span v-else class="text-gray-400 text-sm">No file chosen</span>
                  </div>
                </div>
              </div>
            </Fieldset>
          </div>
          <!-- Existing card images -->
          <div v-if="props.insurance?.cardFrontUrl || props.insurance?.cardBackUrl" class="mb-6">
            <h3 class="text-md font-semibold mb-4 text-gray-700">Current Card Images</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-if="props.insurance?.cardFrontUrl" class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Front of Card</label>
                <a
                  :href="props.insurance?.cardFrontUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="block"
                >
                  <img
                    alt="Front of insurance card"
                    v-if="isImageExisting(props.insurance?.cardFrontUrl)"
                    :src="props.insurance?.cardFrontUrl"
                    class="h-24 w-auto object-cover border rounded"
                  />
                  <div
                    v-else
                    class="h-24 w-32 border rounded flex items-center justify-center bg-gray-50"
                  >
                    <span class="text-gray-500 text-sm">PDF Document</span>
                  </div>
                </a>
              </div>
              <div v-if="props.insurance?.cardBackUrl" class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Back of Card</label>
                <a
                  :href="props.insurance?.cardBackUrl"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="block"
                >
                  <img
                    alt="Back of insurance card"
                    v-if="isImageExisting(props.insurance?.cardBackUrl)"
                    :src="props.insurance?.cardBackUrl"
                    class="h-24 w-auto object-cover border rounded"
                  />
                  <div
                    v-else
                    class="h-24 w-32 border rounded flex items-center justify-center bg-gray-50"
                  >
                    <span class="text-gray-500 text-sm">PDF Document</span>
                  </div>
                </a>
              </div>
            </div>
          </div>

          <!-- New file previews -->
          <div v-if="selectedFileFront || selectedFileBack" class="mb-6">
            <h3 class="text-md font-semibold mb-4 text-gray-700">New Files Selected</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-if="selectedFileFront" class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Front of Card (New)</label>
                <a
                  :href="getFileUrl(selectedFileFront)"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="block"
                >
                  <img
                    alt="New front of insurance card"
                    v-if="isImageNew(selectedFileFront)"
                    :src="getFileUrl(selectedFileFront)"
                    class="h-24 w-auto object-cover border rounded"
                  />
                  <div
                    v-else
                    class="h-24 w-32 border rounded flex items-center justify-center bg-gray-50"
                  >
                    <span class="text-gray-500 text-sm">PDF Document</span>
                  </div>
                </a>
              </div>
              <div v-if="selectedFileBack" class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Back of Card (New)</label>
                <a
                  :href="getFileUrl(selectedFileBack)"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="block"
                >
                  <img
                    alt="New back of insurance card"
                    v-if="isImageNew(selectedFileBack)"
                    :src="getFileUrl(selectedFileBack)"
                    class="h-24 w-auto object-cover border rounded"
                  />
                  <div
                    v-else
                    class="h-24 w-32 border rounded flex items-center justify-center bg-gray-50"
                  >
                    <span class="text-gray-500 text-sm">PDF Document</span>
                  </div>
                </a>
              </div>
            </div>
          </div>

          <!-- Save button -->
          <div v-if="props.insurance == null" class="flex justify-end pt-4 border-t">
            <Button @click="addCoverage" class="bg-primary text-white px-6 py-2 rounded">
              Save
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useField, useForm } from 'vee-validate'
import { computed, ref, watch, watchEffect } from 'vue'
import { api } from '@/api'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import RadioButton from 'primevue/radiobutton'
import Select from '../../components/form-extensions/SelectFluent.vue'
import Fieldset from 'primevue/fieldset'
import * as yup from 'yup'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import { useAuthStore } from '@/stores/auth.ts'
import Button from 'primevue/button'
import FileUpload from 'primevue/fileupload'
import type { PatientInsuranceResponse } from '@/api/api-reference.ts'
import { useToast } from 'vue-toastification'

const authStore = useAuthStore()
const toast = useToast()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  insurance: PatientInsuranceResponse | null
  existingInsurances?: PatientInsuranceResponse[]
}>()

const isNew = computed(() => props.insurance == null)

// computed property to get existing insurance orders (excluding current one being edited)
const existingInsuranceOrders = computed(() => {
  if (!props.existingInsurances) return []

  return props.existingInsurances
    .filter((ins) => ins.id !== props.insurance?.id) // exclude current insurance if editing
    .map((ins) => ins.order)
    .filter((order) => order) // filter out null/undefined orders
})

const allOrderOptions = [
  { text: 'Primary', value: 'Primary' },
  { text: 'Secondary', value: 'Secondary' },
  { text: 'Tertiary', value: 'Tertiary' },
  { text: 'Supplemental', value: 'supplemental' },
]

// computed property to filter available order options based on existing insurances
const orderOptions = computed(() => {
  if (!isNew.value) {
    // if editing existing insurance, show all options
    return allOrderOptions
  }

  // for new insurance, filter out already used orders (except Supplemental)
  return allOrderOptions.filter((option) => {
    if (option.value === 'supplemental') {
      // always allow supplemental (multiple allowed)
      return true
    }

    // check if this order is already used
    return !existingInsuranceOrders.value.includes(option.value)
  })
})

const typeOptions = ref([
  { text: 'Medicare', value: 'Medicare' },
  { text: 'Medicaid', value: 'Medicaid' },
  { text: 'Commercial HMO', value: 'Commercial HMO' },
  { text: 'Commercial PPO', value: 'Commercial PPO' },
  { text: 'Government (Federal/State/Local)', value: 'Government' },
  { text: 'Worker’s Comp', value: 'Worker’s Comp' },
  { text: 'Medicare Advantage', value: 'Medicare Advantage' },
  { text: 'Self Pay', value: 'Self Pay' },
])
const initialValues = {
  issuer: '',
  groupId: '',
  memberId: '',
  relationship: 'Self',
  start: new Date(),
  end: new Date(),
  order: '',
  type: '',
  copay: null,
  deductible: null,
  cardFront: '',
  cardBack: '',
  subscriber: {
    firstName: '',
    lastName: '',
    birthday: new Date(),
    birthSex: 'Female',
  },
}

const { handleSubmit, resetForm, setValues } = useForm({
  validationSchema: yup.object({
    issuer: yup.string().required('Issuer is required'),
    start: yup.string().required('Insurance start is required'),
    order: yup.string().required('Order is required'),
    type: yup.string().required('Type start is required'),
  }),
  initialValues: initialValues,
})

const { value: relationship } = useField<string | null>('relationship')
const { value: birthSex } = useField<string | null>('subscriber.birthSex')

const addCoverage = handleSubmit(async (values) => {
  try {
    // create form data matching API interface
    const form: any = {
      Issuer: values.issuer,
      GroupId: values.groupId,
      MemberId: values.memberId,
      Start: values.start?.toDateString(),
      End: values.end?.toDateString(),
      Order: values.order,
      Type: values.type,
      Copay: values.copay,
      Deductible: values.deductible,
      Relation: values.relationship,
      CardFront: selectedFileFront.value || null,
      CardBack: selectedFileBack.value || null,
    }

    // add subscriber details if applicable
    if (values.relationship !== 'Self' && values.subscriber) {
      form['Subscriber.FirstName'] = values.subscriber.firstName
      form['Subscriber.LastName'] = values.subscriber.lastName
      form['Subscriber.Birthday'] = values.subscriber.birthday?.toDateString()
      form['Subscriber.BirthSex'] = values.subscriber.birthSex
    }

    await api.patients.patientAddInsurance(form)
    toast.success('Insurance coverage added successfully')
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
    toast.error('Failed to add insurance coverage')
  }
})

watchEffect(() => {
  if (props.insurance) {
    const insuranceObj = {
      issuer: props.insurance.issuer || '',
      groupId: props.insurance.groupId || '',
      memberId: props.insurance.memberId || '',
      relationship: props.insurance.relationship || 'Self',
      order: props.insurance.order || '',
      type: props.insurance.type || '',
      copay: props.insurance.copay || null,
      deductible: props.insurance.deductible || null,
      start: props.insurance.start ? new Date(props.insurance.start) : new Date(),
      end: props.insurance.end ? new Date(props.insurance.end) : new Date(),
      cardFront: '',
      cardBack: '',
      subscriber: props.insurance.subscriber
        ? {
            firstName: props.insurance.subscriber.firstName || '',
            lastName: props.insurance.subscriber.lastName || '',
            birthday: props.insurance.subscriber.birthday
              ? new Date(props.insurance.subscriber.birthday)
              : new Date(),
            birthSex: props.insurance.subscriber.birthSex || 'Female',
          }
        : {
            firstName: '',
            lastName: '',
            birthday: new Date(),
            birthSex: 'Female',
          },
    }
    setValues(insuranceObj as any)
  }
})

watch(
  () => props.isModalOpen,
  async (newValue) => {
    if (newValue) {
      selectedFileFront.value = undefined
      selectedFileBack.value = undefined
      resetForm()
    }
  },
)

const selectedFileFront = ref<File>()
const selectedFileBack = ref<File>()

const handleFileSelect = (event: any, side: 'front' | 'back') => {
  if (event.files && event.files.length > 0) {
    if (side === 'front') {
      selectedFileFront.value = event.files[0]
    } else {
      selectedFileBack.value = event.files[0]
    }
  }
}

const isImageNew = (file: File) => {
  return file.type.startsWith('image/')
}

const isImageExisting = (filePath: string) => {
  return filePath.endsWith('.jpg') || filePath.endsWith('.png') || filePath.endsWith('.jpeg')
}

const getFileUrl = (file: File) => {
  return URL.createObjectURL(file)
}
</script>
