<template>
  <InputText id="note" label="Notes" />
  <Select id="fasting" label="Fasting" :options="yesNoOptions" />
  <Select id="repeat" label="Repeat" :options="yesNoOptions" />
  <Select id="isRequired" label="Required" :options="yesNoOptions" />

  <div class="flex justify-end gap-2">
    <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="closeOrderEntry">
      Cancel
    </button>
    <Button
      v-if="editingOrderTemplateEntry"
      label="Edit Order"
      @click="submitOrderEntry(undefined)"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white disabled:opacity-50 disabled:pointer-events-none"
    />
    <Button
      v-if="!editingOrderTemplateEntry"
      icon="pi pi-plus"
      label="Add Order"
      @click="toggleMenu"
      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white disabled:opacity-50 disabled:pointer-events-none"
    />
    <Menu ref="menuRef" :model="steps" popup />
  </div>
</template>
<script setup lang="ts">
import { useForm } from 'vee-validate'
import { computed, onMounted, ref, watch } from 'vue'
import type {
  SearchOrderEntryResponse,
  OrderTemplateEntryResponse,
  LabTemplateEntryResponse,
} from '@/api/api-reference'
import Select from '@/components/form-extensions/SelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import Button from 'primevue/button'
import * as yup from 'yup'
import Menu from 'primevue/menu'

const props = defineProps<{
  selectedOrderEntry: SearchOrderEntryResponse | undefined
  editingOrderTemplateEntry: OrderTemplateEntryResponse | undefined
  numberOfSteps: number
}>()

const emits = defineEmits(['close', 'submitLabOrderEntry'])
const yesNoOptions = ref([
  { text: 'Yes', value: true },
  { text: 'No', value: false },
])

const steps = computed(() => {
  const stepItems = []
  for (let step = 1; step <= props.numberOfSteps; step++) {
    stepItems.push({
      label: `Step ${step}`,
      command: () => {
        submitOrderEntry(step)
      },
    })
  }
  return stepItems
})

const menuRef = ref()
const toggleMenu = (event: MouseEvent) => {
  if (props.numberOfSteps <= 1) {
    submitOrderEntry(1)
  } else {
    menuRef.value.toggle(event)
  }
}

const initialValues = {
  specimen: 'BLD',
  note: '',
  fasting: false,
  repeat: false,
  isRequired: false,
}

const schema = yup.object({
  note: yup.string().nullable(),
  fasting: yup.bool().required('Fasting is required'),
  repeat: yup.bool().required('Repeat is required'),
  isRequired: yup.bool().required('Required is required'),
})

const { handleSubmit, setValues, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

function submitOrderEntry(stepNumber: number | undefined) {
  const submitFn = handleSubmit(async (values) => {
    stepNumber = stepNumber ?? 1
    emits('submitLabOrderEntry', values, stepNumber)
  })
  submitFn()
}

onMounted(async () => {
  if (props.editingOrderTemplateEntry) {
    const labEditingOrder = props.editingOrderTemplateEntry as LabTemplateEntryResponse
    setValues({
      ...labEditingOrder,
    })
  }
})

const closeOrderEntry = () => {
  resetForm()
  emits('close')
}

watch(
  () => props.editingOrderTemplateEntry,
  async (newValue) => {
    if (newValue) {
      const labEditingOrder = newValue as LabTemplateEntryResponse
      setValues({
        ...labEditingOrder,
      })
    }
  },
)
</script>
