<template>
  <div class="px-4 sm:px-6 lg:px-8 mx-auto">
    <div class="flex flex-col">
      <div class="-m-1.5 overflow-x-auto">
        <div class="p-1.5 min-w-full inline-block align-middle">
          <div class="flex flex-col md:flex-row gap-4">
            <!-- Sidebar (Selects & DatePicker) -->
            <div class="w-full md:w-1/4 mt-8 flex flex-col gap-2">
              <SelectFluent
                id="locationId"
                label="Location"
                :options="locations"
                optionLabel="locationName"
                v-model="location"
              />
              <SelectFluent
                id="practitionerId"
                label="Practitioner"
                :options="practitioners"
                optionLabel="fullName"
                v-model="practitioner"
              />
              <DatePicker
                id="selectedDate"
                label=""
                :inline="!isPopupDatePicker"
                :manualInput="false"
                :min-date="new Date()"
                v-model="selectedDate"
              />
            </div>

            <!-- Available Slots Section -->
            <div class="w-full md:w-3/4 bg-white p-6 rounded-lg mt-8">
              <h2 class="text-xl font-semibold mb-4 text-gray-800">
                Available Slots
                <span v-if="selectedDateString"> for {{ selectedDateString }}:</span>
              </h2>

              <!-- Existing Appointment Message -->
              <div
                v-if="existingAppointment"
                class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Existing Appointment Found</h3>
                    <div class="mt-2 text-sm text-blue-800">
                      <p>
                        You have an existing appointment on this date with
                        <strong>{{ existingAppointment.employeeName }}</strong>
                        at <strong>{{ existingAppointment.locationName }}</strong> from
                        {{ formatExistingAppointmentTime(existingAppointment.startAt) }} to
                        {{ formatExistingAppointmentTime(existingAppointment.endAt) }}. Status:
                        <strong>{{ existingAppointment.status }}</strong>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Responsive Grid -->
              <div
                v-if="timeSlots.length > 0"
                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4"
              >
                <Button
                  v-for="(slot, index) in timeSlots"
                  :key="index"
                  :label="formatTimeSlot(slot)"
                  class="w-full py-3 px-4 md:py-2 md:px-3 lg:py-3 lg:px-4 text-lg md:text-sm 2xl:text-lg font-medium bg-white border border-primary text-primary rounded-lg shadow-sm hover:bg-primary hover:text-white transition duration-300"
                  @click="openConfirmationDialog(slot)"
                />
              </div>

              <p v-else class="text-gray-500 text-lg text-center">
                No available appointment slots.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ConfirmDialog
    ref="confirmDialog"
    @confirmedAction="bookAppointment"
    title="Book Appointment"
    :message="`Are you sure you want to schedule an appointment with ${practitioner?.fullName} ${date} from ${fromTime} to ${toTime} at ${location?.locationName}?`"
  />
</template>

<script setup lang="ts">
import DatePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import Button from 'primevue/button'
import SelectFluent from '@/components/form-extensions/SelectObjectFluent.vue'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useAppointmentsStore } from '../../../stores/appointments'
import type {
  LocationWithPractitionersResponse,
  AppointmentSlot,
  PractitionerResponse,
  AvailableAppointmentSlotsResponse,
  ExistingAppointmentInfo,
} from '../../../api/api-reference'
import { api } from '../../../api'
import { format } from 'date-fns'
import ConfirmDialog from '@/components/form-extensions/ConfirmInfoDialog.vue'
import { useAuthStore } from '../../../stores/auth'
import { useRoute, useRouter } from 'vue-router'
import { convertToUtc } from '../../../utils/timeMethods'
import { useToast } from 'vue-toastification'

const toast = useToast()
const router = useRouter()
const route = useRoute()

const authStore = useAuthStore()

const appointmentStore = useAppointmentsStore()
const reschedule = ref(false)

onMounted(async () => {
  await appointmentStore.getLocationsWithPractitioners()
  if (route.query.appointmentId) {
    const appointmentResponse = await api.appointments.appointmentAppointmentById(
      route.query.appointmentId as string,
    )
    const appointment = appointmentResponse.data
    location.value = locations.value.filter((x) => x.locationId == appointment.locationId)[0]
    practitioner.value = practitioners.value.filter(
      (x: PractitionerResponse) => x.employeeId == appointment.employeeId,
    )[0]
    selectedDate.value = new Date(appointment.startAt!)
    reschedule.value = true
  }
  window.addEventListener('resize', updateWindowWidth)
})
onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

const locations = ref<LocationWithPractitionersResponse[]>([])
const practitioners = computed(() => location.value?.practitioners ?? [])
const location = ref()
const practitioner = ref()
const selectedDate = ref()
const selectedDateString = computed(() =>
  selectedDate.value ? format(new Date(selectedDate.value), 'M/d/yyyy') : '',
)
const confirmDialog = ref()
const dateFrom = ref<Date>()
const dateTo = ref<Date>()

const selectedSlot = ref<AppointmentSlot>()
const date = computed(() => (dateFrom.value ? format(dateFrom.value, 'M/d/yyyy') : ''))
const fromTime = computed(() => {
  if (!selectedSlot.value) return ''
  const timeZone = selectedSlot.value?.practitionerTimeZone
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone,
    timeStyle: 'short',
  })
  return `${formatter.format(new Date(selectedSlot.value!.from!))}`
})
const toTime = computed(() => {
  if (!selectedSlot.value) return ''
  const timeZone = selectedSlot.value?.practitionerTimeZone
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone,
    timeStyle: 'short',
  })
  return `${formatter.format(new Date(selectedSlot.value!.to!))}`
})
const timeSlots = ref<AppointmentSlot[]>([])
const existingAppointment = ref<ExistingAppointmentInfo | null>(null)

watch(
  () => appointmentStore.locationsWithPractitioners,
  (newValue) => {
    locations.value = newValue
  },
)

const getSlots = async () => {
  if (practitioner.value && location.value && selectedDate.value) {
    const response = await api.employees.employeeGetAvailableAppointmentSlots(
      practitioner.value.employeeId,
      location.value.locationId,
      { date: convertToUtc(selectedDate.value).toISOString(), reschedule: reschedule.value },
    )
    const slotsResponse = response.data as AvailableAppointmentSlotsResponse
    timeSlots.value = slotsResponse?.availableSlots ?? []
    existingAppointment.value = slotsResponse?.existingAppointment ?? null
  } else {
    // clear slots and existing appointment when selection is incomplete
    timeSlots.value = []
    existingAppointment.value = null
  }
}
watch([locations, practitioner, selectedDate], getSlots)

const formatTimeSlot = (slot: AppointmentSlot) => {
  if (!slot.from || !slot.to) return 'Invalid Slot'
  const timeZone = slot.practitionerTimeZone
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeZone,
    timeStyle: 'short',
  })
  return `${formatter.format(new Date(slot.from))} - ${formatter.format(new Date(slot.to))}`
}

const formatExistingAppointmentTime = (dateTime: string | undefined) => {
  if (!dateTime) return ''
  const formatter = new Intl.DateTimeFormat('en-US', {
    timeStyle: 'short',
  })
  return formatter.format(new Date(dateTime))
}

const openConfirmationDialog = (slot: AppointmentSlot) => {
  if (!slot.from || !slot.to) return
  // Store actual Date objects instead of formatted strings
  dateFrom.value = new Date(slot.from)
  dateTo.value = new Date(slot.to)
  selectedSlot.value = slot
  confirmDialog.value.open()
}

const bookAppointment = async () => {
  try {
    if (route.query.appointmentId) {
      await api.appointments.appointmentEditAppointment({
        id: route.query.appointmentId as string,
        employeeId: practitioner.value.employeeId,
        locationId: location.value.locationId,
        startAt: dateFrom.value?.toISOString(),
        durationInMinutes: 30,
      })
      toast.success('Appointment updated successfully.')
    } else {
      await api.appointments.appointmentCreateForExistingPatient({
        employeeId: practitioner.value.employeeId,
        locationId: location.value.locationId,
        patientId: authStore.user?.patientId ?? '',
        startAt: dateFrom.value?.toISOString(),
        durationInMinutes: 30,
        initiatedByPatient: true,
      })
      toast.success('Appointment created successfully.')
    }
    router.push('/patient-appointments')
  } catch (e) {
    console.log(e)
  }
}

//responsive
const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
}
const windowWidth = ref(window.innerWidth)
const isPopupDatePicker = computed(() => windowWidth.value >= 768 && windowWidth.value <= 1350)
</script>
