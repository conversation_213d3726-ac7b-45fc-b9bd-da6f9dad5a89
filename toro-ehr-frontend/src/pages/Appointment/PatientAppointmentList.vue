<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Appointments
    </h2>

    <!-- Upcoming Appointments Section -->
    <TableSection>
      <TableHeader>
        <template #inputs>
          <h3 class="text-lg font-medium text-gray-900">Upcoming Appointments</h3>
        </template>
        <template #buttons>
          <RouterLink
            :to="{ name: 'book-appointment' }"
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Add appointment
          </RouterLink>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Doctor
                </span>
              </div>
            </th>

            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Date
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Time
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Duration
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Status
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in upcomingAppointments" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                  row.employeeName
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span
                  class="block text-sm font-semibold text-gray-500 cursor-help"
                  v-tooltip.top="formatAddressTooltip(row.locationAddress)"
                >
                  {{ row.location }}
                </span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(row.startAt!, row.timeZone!).formattedDate
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(row.startAt!, row.timeZone!).formattedTime
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.durationInMinutes }} min</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getAppointmentStatusClasses(row.status)"
                >
                  <span
                    class="w-1.5 h-1.5 rounded-full mr-1.5"
                    :class="getAppointmentStatusDotClass(row.status)"
                  ></span>
                  {{ row.status }}
                </span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4">
                <!-- Check In - Only for Confirmed status -->
                <a
                  v-show="canShowCheckIn(row)"
                  class="inline-flex items-center gap-x-1 text-sm font-medium focus:outline-none"
                  :class="{
                    'text-primary hover:underline focus:underline cursor-pointer': canCheckIn(row),
                    'text-gray-400 cursor-not-allowed': !canCheckIn(row),
                  }"
                  href="#"
                  @click.prevent="
                    canCheckIn(row) &&
                    router.push({
                      name: 'checkin-questionnaires-patient',
                      params: { encounterId: row.encounterId },
                    })
                  "
                  v-tooltip="
                    !canCheckIn(row) ? `Available at: ${formatDate(row.checkInAvailableAt!)}` : null
                  "
                >
                  Check In
                </a>

                <!-- Reschedule - For Pending and Confirmed only -->
                <a
                  v-show="canShowReschedule(row)"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="
                    router.push({ name: 'book-appointment', query: { appointmentId: row.id } })
                  "
                >
                  Reschedule
                </a>

                <!-- Cancel - For Pending and Confirmed only -->
                <a
                  v-show="canShowCancel(row)"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="openCancelConfirmation(row)"
                >
                  Cancel
                </a>

                <!-- No actions message for statuses with no available actions -->
                <span v-show="!hasAnyActions(row)" class="text-sm text-gray-400 italic"> </span>
              </div>
            </td>
          </tr>
          <tr v-if="upcomingAppointments?.length === 0">
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              No upcoming appointments found.
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="upcomingTotalItems"
        :isFirstPage="upcomingIsFirstPage"
        :isLastPage="upcomingIsLastPage"
        @prevPage="upcomingPrevPage"
        @nextPage="upcomingNextPage"
      />
    </TableSection>

    <!-- Past Appointments Section -->
    <TableSection class="mt-8">
      <TableHeader>
        <template #inputs>
          <h3 class="text-lg font-medium text-gray-900">Past Appointments</h3>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Doctor
                </span>
              </div>
            </th>

            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Date
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Time
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Duration
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Status
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in pastAppointments" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                  row.employeeName
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span
                  class="block text-sm font-semibold text-gray-500 cursor-help"
                  v-tooltip.top="formatAddressTooltip(row.locationAddress)"
                >
                  {{ row.location }}
                </span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(row.startAt!, row.timeZone!).formattedDate
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{
                  formatDateTimeInLocationTimeZone(row.startAt!, row.timeZone!).formattedTime
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.durationInMinutes }} min</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getAppointmentStatusClasses(row.status)"
                >
                  <span
                    class="w-1.5 h-1.5 rounded-full mr-1.5"
                    :class="getAppointmentStatusDotClass(row.status)"
                  ></span>
                  {{ row.status }}
                </span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4"></div>
            </td>
          </tr>
          <tr v-if="pastAppointments?.length === 0">
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              No past appointments found.
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="pastTotalItems"
        :isFirstPage="pastIsFirstPage"
        :isLastPage="pastIsLastPage"
        @prevPage="pastPrevPage"
        @nextPage="pastNextPage"
      />
    </TableSection>

    <ConfirmDialog
      ref="confirmDialog"
      @confirmedAction="cancelAppointment"
      title="Cancel Appointment"
      :message="cancellationMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api'
import { type AppointmentResponse, AppointmentTimeFilter } from '@/api/api-reference.ts'
import { PlusIcon } from '@heroicons/vue/24/outline'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'

import router from '../../router'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'
import { formatDate, formatDateTimeInLocationTimeZone } from '@/utils/timeMethods.ts'
import { getAppointmentStatusClasses, getAppointmentStatusDotClass } from '@/utils/statusColors.ts'
import { useToast } from 'vue-toastification'

const toast = useToast()

const limit = 10

// Upcoming appointments state
const upcomingAppointments = ref<AppointmentResponse[]>()
const upcomingPageNumber = ref(1)
const upcomingTotalPages = ref(1)
const upcomingTotalItems = ref(0)

// Past appointments state
const pastAppointments = ref<AppointmentResponse[]>()
const pastPageNumber = ref(1)
const pastTotalPages = ref(1)
const pastTotalItems = ref(0)

const search = ref('')
const confirmDialog = ref()
const cancellationMessage = ref('Are you sure you want to cancel this appointment?')
const selectedAppointmentId = ref<string>('')

onMounted(async () => {
  await fetchUpcomingAppointments()
  await fetchPastAppointments()
})

// Upcoming appointments pagination
const upcomingIsFirstPage = computed(() => upcomingPageNumber.value === 1)
const upcomingIsLastPage = computed(() => upcomingPageNumber.value === upcomingTotalPages.value)

const upcomingNextPage = () => {
  if (!upcomingIsLastPage.value) {
    upcomingPageNumber.value++
    fetchUpcomingAppointments()
  }
}

const upcomingPrevPage = () => {
  if (!upcomingIsFirstPage.value) {
    upcomingPageNumber.value--
    fetchUpcomingAppointments()
  }
}

// Past appointments pagination
const pastIsFirstPage = computed(() => pastPageNumber.value === 1)
const pastIsLastPage = computed(() => pastPageNumber.value === pastTotalPages.value)

const pastNextPage = () => {
  if (!pastIsLastPage.value) {
    pastPageNumber.value++
    fetchPastAppointments()
  }
}

const pastPrevPage = () => {
  if (!pastIsFirstPage.value) {
    pastPageNumber.value--
    fetchPastAppointments()
  }
}

const fetchUpcomingAppointments = async () => {
  try {
    const result = await api.appointments.appointmentListPatientAppointments({
      pageNumber: upcomingPageNumber.value,
      pageSize: limit,
      searchParam: search.value,
      timeFilter: AppointmentTimeFilter.Upcoming,
    })
    upcomingAppointments.value = result.data.items
    upcomingPageNumber.value = result.data.pageNumber ?? 1
    upcomingTotalPages.value = result.data.totalPages ?? 1
    upcomingTotalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching upcoming appointments:', error)
  }
}

const fetchPastAppointments = async () => {
  try {
    const result = await api.appointments.appointmentListPatientAppointments({
      pageNumber: pastPageNumber.value,
      pageSize: limit,
      searchParam: search.value,
      timeFilter: AppointmentTimeFilter.Past,
    })
    pastAppointments.value = result.data.items
    pastPageNumber.value = result.data.pageNumber ?? 1
    pastTotalPages.value = result.data.totalPages ?? 1
    pastTotalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching past appointments:', error)
  }
}

const getCancellationWarningMessage = (appointment: AppointmentResponse): string => {
  const { checkInStartOffsetHours, missedAppointmentFee, startAt } = appointment

  // If no fee or checkin window, show basic message
  if (!missedAppointmentFee || !checkInStartOffsetHours) {
    return 'Are you sure you want to cancel this appointment?'
  }

  // Calculate if we're within the cancellation fee window
  const appointmentTime = new Date(startAt!)
  const now = new Date()
  const hoursUntilAppointment = (appointmentTime.getTime() - now.getTime()) / (1000 * 60 * 60)

  const feeAmount = missedAppointmentFee.toFixed(2)

  if (hoursUntilAppointment <= checkInStartOffsetHours) {
    return `Are you sure you want to cancel this appointment?\n\nA cancellation fee of $${feeAmount} will apply since you are canceling within ${checkInStartOffsetHours} hours of the scheduled time.`
  } else {
    return `Are you sure you want to cancel this appointment?`
  }
}

const openCancelConfirmation = (appointment: AppointmentResponse) => {
  selectedAppointmentId.value = appointment.id!
  cancellationMessage.value = getCancellationWarningMessage(appointment)
  confirmDialog.value?.open(appointment.id)
}

const cancelAppointment = async (id: string) => {
  try {
    await api.appointments.appointmentCancelAppointment({ id: id })
    // refresh both lists since a canceled appointment might move from upcoming to past
    await fetchUpcomingAppointments()
    await fetchPastAppointments()
    toast.success('Appointment cancelled successfully.')
  } catch (e) {
    console.log(e)
  }
}

const canCheckIn = (row: AppointmentResponse) => {
  return new Date() >= new Date(row.checkInAvailableAt!)
}

// Action visibility functions based on appointment status
const canShowCheckIn = (row: AppointmentResponse) => {
  return row.status === 'Confirmed'
}

const canShowReschedule = (row: AppointmentResponse) => {
  const status = row.status?.toLowerCase()
  return status === 'pending' || status === 'confirmed'
}

const canShowCancel = (row: AppointmentResponse) => {
  const status = row.status?.toLowerCase()
  return status === 'pending' || status === 'confirmed'
}

const hasAnyActions = (row: AppointmentResponse) => {
  return canShowCheckIn(row) || canShowReschedule(row) || canShowCancel(row)
}

// format address for tooltip display
const formatAddressTooltip = (address: string | undefined) => {
  if (!address) return 'Address not available'
  return address
}
</script>
