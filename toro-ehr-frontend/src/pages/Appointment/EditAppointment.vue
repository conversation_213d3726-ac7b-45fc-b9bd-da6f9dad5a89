<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[99]"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <div class="flex justify-between items-start mb-4">
        <div class="flex items-center gap-2">
          <h2 class="text-lg font-bold">Edit Appointment</h2>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getAppointmentStatusClasses(appointmentStatus)">
            <span class="w-1.5 h-1.5 rounded-full mr-1.5" :class="getAppointmentStatusDotClass(appointmentStatus)"></span>
            {{ appointmentStatus }}
          </span>
        </div>
        <Button
          icon="pi pi-times"
          severity="secondary"
          variant="text"
          rounded
          aria-label="Cancel"
          class="w-8 h-8 p-0 flex items-center justify-center"
          style="min-width: 2rem; height: 2rem"
          @click="$emit('close')"
        />
      </div>
      <div class="overflow-y-auto max-h-[70vh]">
        <form @submit.prevent="editAppointment">
          <InputText id="patient" label="Patient" :disabled="true"/>
          <Select
            id="employeeId"
            label="Practitioner"
            :options="appointmentsStore.practitionerLookups"
            :disabled="true"
          />
          <Select id="locationId" label="Location" :options="appointmentsStore.locationLookups" :disabled="true"/>
          <InputNumber id="durationInMinutes" label="Duration" suffix=" minutes" :disabled="true"/>
          <DateTimePicker id="startAt" label="Start" showTime hourFormat="12" dateFormat="m/d/yy" :disabled="true"/>
        </form>
        <div class="flex justify-end gap-2 mt-4">
          <Button
            v-if="canShowCancelButton()"
            ref="cancelButton"
            label="Cancel Appointment"
            severity="danger"
            @click="confirmCancelAppointment"
          />
          <Button
            v-if="canShowConfirmButton()"
            label="Confirm"
            severity="primary "
            @click="confirmAppointment"
          />
          <div v-if="!canShowCancelButton() && !canShowConfirmButton()"
               class="text-sm text-gray-500 italic py-2">
            No actions available for {{ appointmentStatus?.toLowerCase() }} appointments
          </div>
        </div>
      </div>
    </div>

    <!-- Confirm Popup for Cancel Appointment -->
    <ConfirmPopup />
  </div>
</template>

<script setup lang="ts">
import {useForm} from 'vee-validate'
import {ref, watch} from 'vue'
import {api} from '@/api'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import type {EditAppointmentCommand} from '@/api/api-reference.ts'
import * as yup from 'yup'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'
import DateTimePicker from '../../components/form-extensions/DateTimePickerFluent.vue'
import {useAppointmentsStore} from '@/stores/appointments.ts'
import Button from "primevue/button";

import ConfirmPopup from "primevue/confirmpopup";
import {useConfirm} from "primevue/useconfirm";
import {useToast} from "vue-toastification";
import { getAppointmentStatusClasses, getAppointmentStatusDotClass } from "@/utils/statusColors.ts";

const toast = useToast()
const confirm = useConfirm()
const appointmentsStore = useAppointmentsStore()
const appointmentStatus = ref<string>()
const cancelButton = ref()

const emit = defineEmits(['close'])
const props = defineProps<{
  appointmentId: string
  isModalOpen: boolean
}>()

const initialValues = {
  id: '',
  patient: '',
  employeeId: '',
  locationId: '',
  durationInMinutes: 0,
  startAt: new Date(),
}

const schema = yup.object({
  patient: yup.string().required('Patient is required'),
  employeeId: yup.string().required('Provider is required'),
  locationId: yup.string().required('Location is required'),
  durationInMinutes: yup.number().required('Duration is required'),
  startAt: yup.date().required('Start at is required'),
})

const {handleSubmit, setValues, resetForm} = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const editAppointment = handleSubmit(async (values) => {
  try {
    const form: EditAppointmentCommand = {
      ...values,
      startAt: values.startAt.toISOString(),
    }
    await api.appointments.appointmentEditAppointment(form)
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

const confirmAppointment = async () => {
  try {
    await api.appointments.appointmentConfirm({ id: props.appointmentId })
    toast.success('Appointment confirmed successfully.')
    emit('close')
  } catch (error) {
    console.error(error)
    toast.error('Failed to confirm appointment.')
  }
}

const confirmCancelAppointment = (event: Event) => {
  confirm.require({
    target: event.currentTarget as HTMLElement,
    message: 'Are you sure you want to cancel this appointment?',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: 'No',
      severity: 'secondary',
      outlined: true
    },
    acceptProps: {
      label: 'Yes, Cancel',
      severity: 'danger'
    },
    accept: () => {
      cancelAppointment()
    }
  })
}

const cancelAppointment = async () => {
  try {
    await api.appointments.appointmentCancelAppointment({ id: props.appointmentId })
    toast.success('Appointment cancelled successfully.')
    emit('close')
  } catch (error) {
    console.error(error)
    toast.error('Failed to cancel appointment.')
  }
}

watch(
  () => props.isModalOpen,
  async (newValue) => {
    if (newValue && props.appointmentId) {
      try {
        await appointmentsStore.getLocationsLookups()
        resetForm()
        const appointment = (await api.appointments.appointmentAppointmentById(props.appointmentId)).data
        appointmentStatus.value = appointment.status
        const form = {
          id: appointment.id,
          patient: appointment.patientFullName,
          employeeId: appointment.employeeId,
          locationId: appointment.locationId,
          startAt: new Date(appointment.startAt ?? ''),
          durationInMinutes: appointment.durationInMinutes,
        }
        setValues(form)
      } catch (error) {
        console.log(error)
      }
    }
  },
)



// Action visibility functions based on appointment status
const canShowCancelButton = (): boolean => {
  const status = appointmentStatus.value?.toLowerCase()
  // completed appointments cannot be canceled
  return status === 'pending' || status === 'confirmed' || status === 'checked in'
}

const canShowConfirmButton = (): boolean => {
  return appointmentStatus.value === 'Pending'
}
</script>
