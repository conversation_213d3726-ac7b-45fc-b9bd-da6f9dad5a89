<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-gray-100 text-gray-800">
    <div class="bg-white p-6 rounded-lg shadow-lg text-center">
      <h1 class="text-4xl font-bold text-red-600 mb-4">403 - Forbidden</h1>
      <p class="text-lg mb-6">You don't have permission to access this page.</p>
      <button
        @click="goHome"
        class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300"
      >
        Go Back to Homepage
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// Navigate to the homepage
const goHome = () => {
  router.push('/')
}
</script>
