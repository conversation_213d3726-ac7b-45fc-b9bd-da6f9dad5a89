<template>
  <div class="flex min-h-full flex-col px-6 py-36 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
      <div class="flex items-center w-full leading-9">
        <img src="../../assets/logo_black.png" alt="logo" />
      </div>
      <h2 class="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
        Sign in to your account
      </h2>
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
      <Form ref="form" :validation-schema="schema" @submit="login" :initial-values="formData">
        <InputText id="email" label="Email" type="email" />
        <InputText id="password" label="Password" wrapper-class="mt-4" type="password" />
        <div class="mt-5">
          <button
            type="submit"
            class="flex w-full justify-center rounded-md bg-primary px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-toroblue-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-toroblue-600"
          >
            Sign in
          </button>
          <div class="mt-2">
            <a href="#">Forgot your password?</a>
            <br />
            <a href="#">Didn't receive unlock instructions?</a>
          </div>
        </div>
      </Form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Form /*type SubmissionContext*/ } from 'vee-validate'
import * as yup from 'yup'
import { onMounted, ref } from 'vue'
import type { LoginUserCommand } from '../../api/api-reference'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import { useAuthStore } from '@/stores/auth'
import { useToast } from 'vue-toastification'
import { useRouter } from 'vue-router'

const router = useRouter()
const toast = useToast()
const authStore = useAuthStore()

onMounted(() => {
  if (authStore.user) {
    authStore.logout()
  }
})

const form = ref<LoginUserCommand | null>(null)

const schema = yup.object().shape({
  email: yup.string().required().email(),
  password: yup.string().required(),
})

const formData = ref<LoginUserCommand>({
  email: '',
  password: '',
})

const login = async (values: LoginUserCommand /*, actions: SubmissionContext*/) => {
  try {
    await authStore.login(values)
    if (authStore.user!.selectedUserRole == 'Patient') {
      router.push('/my-appointments')
    } else if (authStore.user!.selectedUserRole == 'Employee') {
      if (authStore.user!.locationEmployeeRoles?.every((r) => r == 'OrganizationAdmin')) {
        router.push('/employees')
      } else {
        router.push('/dashboard')
      }
    } else {
      router.push('/organizations')
    }
  } catch (error) {
    console.log(error)
    toast.error('Invalid login attempt.')
  }
}
</script>
