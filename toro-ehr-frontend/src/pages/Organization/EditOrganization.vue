<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Update Organization</h2>

      <!-- Form -->
      <form @submit.prevent="createOrUpdateOrganization">
        <InputText id="name" label="Organization Name" />
        <SelectFluent id="type" label="Organization Type" :options="organizationTypes" />
        <SelectFluent id="defaultLocationId" label="Default Location" :options="locations" />
        <SelectFluent
          v-if="!showNewAdmin"
          id="contactPersonId"
          label="Contact Person"
          :options="employees"
          v-model="employee"
        />
        <InputText v-if="showNewAdmin" id="organizationAdmin.firstName" label="First Name" />
        <InputText v-if="showNewAdmin" id="organizationAdmin.lastName" label="Last Name" />
        <InputText v-if="showNewAdmin" id="organizationAdmin.email" label="Email" />

        <div class="flex justify-end">
          <Button
            type="button"
            severity="secondary"
            label="Cancel"
            @click="$emit('close')"
            class="mr-2"
          />
          <Button type="submit" label="Save" />
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { watch, ref } from 'vue'
import { api } from '../../api'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import SelectFluent from '../../components/form-extensions/SelectFluent.vue'
import { type OrganizationResponse, type SelectListItem } from '@/api/api-reference'
import * as yup from 'yup'
import Button from 'primevue/button'
import { useToast } from 'vue-toastification'

const toast = useToast()

interface OrganizationForm {
  id?: string
  name?: string
  defaultLocationId?: string
  contactPersonId?: string
  type: string
}

const locations = ref<SelectListItem[]>([])
const employees = ref<SelectListItem[]>([])
const employee = ref<string | undefined>('')
const showNewAdmin = ref(false)

enum OrganizationType {
  Hospital = 'Hospital',
  NonPrescribingOrganization = 'Non-Prescribing Organization',
  PrescribingOrganization = 'Prescribing  Organization',
}

const organizationTypes: SelectListItem[] = Object.entries(OrganizationType).map(
  ([key, value]) => ({
    value: key,
    text: value,
  }),
)

const emit = defineEmits(['close'])
const props = defineProps<{
  organization: OrganizationResponse | null
  isModalOpen: boolean
}>()

const { handleSubmit, setValues } = useForm<OrganizationForm>({
  validationSchema: yup.object({
    name: yup.string().required(),
    defaultLocationId: yup.string().required('Default location is required'),
    type: yup.string().required('Type is required'),

    contactPersonId: yup
      .string()
      .nullable()
      .when([], {
        is: () => !showNewAdmin.value,
        then: (schema) => schema.required('Contact person is required'),
        otherwise: (schema) => schema.nullable(),
      }),

    organizationAdmin: yup.object({
      firstName: yup.string().when([], {
        is: () => showNewAdmin.value,
        then: (schema) => schema.required('First name is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      lastName: yup.string().when([], {
        is: () => showNewAdmin.value,
        then: (schema) => schema.required('Last name is required'),
        otherwise: (schema) => schema.notRequired(),
      }),
      email: yup
        .string()
        .email('Invalid email format')
        .when([], {
          is: () => showNewAdmin.value,
          then: (schema) => schema.required('Email is required'),
          otherwise: (schema) => schema.notRequired(),
        }),
    }),
  }),
})

const fetchData = async () => {
  try {
    const [locationsResponse, employeesResponse] = await Promise.all([
      api.locations.locationListLocationsLookup({ organizationId: props.organization?.id }),
      api.employees.employeeListEmployeesAdminLookup({ organizationId: props.organization?.id }),
    ])
    locations.value = locationsResponse.data
    employees.value = employeesResponse.data
    employees.value.push({ text: 'Create new...', value: '' })
  } catch (error) {
    console.error('Error loading data:', error)
  }
}

const closeModal = () => {
  showNewAdmin.value = false
  emit('close')
}

watch(
  () => props.organization,
  async (newValue) => {
    if (newValue) {
      setValues({
        id: newValue.id,
        name: newValue.name,
        defaultLocationId: newValue.defaultLocation,
        contactPersonId: newValue.contactPerson?.id,
        type: newValue.type,
      })
      employee.value = newValue.contactPerson?.id
      await fetchData()
    }
  },
)

watch(
  () => employee.value,
  async (newValue) => {
    if (newValue == '') {
      showNewAdmin.value = true
    }
  },
)

const createOrUpdateOrganization = handleSubmit(async (values) => {
  try {
    await api.organizations.organizationEdit(values)
    showNewAdmin.value = false
    emit('close')
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const firstErrorKey = Object.keys(error.errors)[0]
    const message = error.errors[firstErrorKey][0]

    toast.error(message)
    console.log(error)
  }
})
</script>
