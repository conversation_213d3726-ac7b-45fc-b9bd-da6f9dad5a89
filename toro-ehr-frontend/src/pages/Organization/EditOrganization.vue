<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">
        {{ form.id ? 'Update Organization' : 'Create Organization' }}
      </h2>

      <!-- Form -->
      <form @submit.prevent="createOrUpdateOrganization">
        <InputText id="name" label="Organization Name" v-model="form.name" />
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button type="submit" class="bg-primary text-white px-4 py-2 rounded">Save</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { reactive, watch } from 'vue'
import { api } from '../../api'
import InputText from '../../components/form-extensions/InputText.vue'
import type { OrganizationResponse } from '../../api/api-reference'
import * as yup from 'yup'

interface OrganizationForm {
  id?: string
  name?: string
}

const emit = defineEmits(['close'])
const props = defineProps<{
  organization: OrganizationResponse | null
  isModalOpen: boolean
}>()

let form = reactive<OrganizationForm>({
  id: '',
  name: '',
})

const { handleSubmit } = useForm<OrganizationForm>({
  validationSchema: yup.object({
    name: yup.string().required(),
  }),
})

watch(
  () => props.organization,
  (newValue) => {
    form = { ...newValue }
  },
)

const createOrUpdateOrganization = handleSubmit(async () => {
  try {
    if (form.id) {
      await api.organizations.organizationEdit(form)
    } else {
      await api.organizations.organizationCreate(form)
    }
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
