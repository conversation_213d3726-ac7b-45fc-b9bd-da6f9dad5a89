<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Organizations
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <MagnifyingGlassIcon class="size-4 text-gray-400" />
            </div>
          </div>
        </template>

        <template #buttons>
          <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click.prevent="openCreateModal()"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Add organization
          </a>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Name
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Contact person
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in itemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{ row.name }}</span>
              </div>
            </td>
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                  row.contactPerson?.fullName
                }}</span>
                <span class="block text-sm text-gray-500">{{ row.contactPerson?.email }}</span>
              </div>
            </td>
            <td class="size-px whitespace-nowrap">
              <div class="px-6 py-1.5">
                <a
                  class="inline-flex items-center gap-x-1 text-sm text-primary decoration-2 hover:underline focus:outline-none focus:underline font-medium"
                  href="#"
                  @click.prevent="openEditModal(row)"
                >
                  Edit
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>

    <CreateOrganization :isModalOpen="isCreateModalOpen" @close="closeModal" />
    <EditOrganization
      :isModalOpen="isEditModalOpen"
      :organization="selectedRow"
      @close="closeModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '../../api'
import type { OrganizationResponse } from '../../api/api-reference'
import { MagnifyingGlassIcon, PlusIcon } from '@heroicons/vue/24/outline'
import debounce from 'lodash.debounce'
import EditOrganization from './EditOrganization.vue'
import CreateOrganization from './CreateOrganization.vue'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'

const limit = 10

const itemList = ref<OrganizationResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

const isCreateModalOpen = ref(false)
const isEditModalOpen = ref(false)
const selectedRow = ref<OrganizationResponse | null>(null)

onMounted(async () => {
  fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = () => {
  if (!isLastPage.value) {
    pageNumber.value++
    fetchData()
  }
}

const prevPage = () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.organizations.organizationListOrganizations({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
    })

    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }
}

const openCreateModal = () => {
  isCreateModalOpen.value = true
}

const openEditModal = (organization: OrganizationResponse) => {
  selectedRow.value = organization
  isEditModalOpen.value = true
}

const closeModal = () => {
  fetchData()
  isCreateModalOpen.value = false
  isEditModalOpen.value = false
}

const filter = debounce(() => {
  pageNumber.value = 1
  fetchData()
}, 500)
</script>
