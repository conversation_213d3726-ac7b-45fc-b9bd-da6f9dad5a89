<template>
  <div class="bg-gray-50">
    <!-- Full Width Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-4xl font-bold text-gray-900">Checkout</h1>
            <p class="text-lg text-gray-600 mt-2">Complete payment and checkout for this encounter</p>
          </div>
          <Button
            @click="router.back()"
            variant="outlined"
            icon="pi pi-arrow-left"
            label="Back"
            class="flex items-center gap-2 text-lg px-6 py-3"
          />
        </div>
      </div>

      <!-- Encounter Info -->
      <div v-if="encounterDetails" class="px-8 py-6 bg-gray-50 border-t border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <span class="font-semibold text-gray-700 text-lg">Patient:</span>
            <span class="ml-3 text-gray-900 text-2xl font-medium">{{ encounterDetails.patientName }}</span>
          </div>
          <div>
            <span class="font-semibold text-gray-700 text-lg">Practitioner:</span>
            <span class="ml-3 text-gray-900 text-xl">{{ encounterDetails.practitionerName }}</span>
          </div>
          <div>
            <span class="font-semibold text-gray-700 text-lg">Date:</span>
            <span class="ml-3 text-gray-900 text-xl">{{ formatDate(encounterDetails.encounterDate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area - Two Column Layout -->
    <div class="flex-1 px-8 py-8">
      <!-- Operation Result Message -->
      <div v-if="operationResult" class="mb-8 p-6 rounded-lg border-l-4 shadow-lg"
           :class="operationResult.success
             ? 'bg-green-50 border-green-400 text-green-800'
             : 'bg-red-50 border-red-400 text-red-800'">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <i :class="operationResult.success ? 'pi pi-check-circle text-green-500' : 'pi pi-times-circle text-red-500'"
               class="text-2xl"></i>
          </div>
          <div class="ml-4 flex-1">
            <div class="font-bold text-xl">
              {{ operationResult.success ? 'Payment Processed Successfully!' : 'Payment Failed' }}
            </div>
            <div class="text-base mt-2">{{ operationResult.message }}</div>
            <div v-if="operationResult.success" class="text-sm mt-2 font-medium">
              ✓ You can now proceed with checkout or process additional payments
            </div>
          </div>
          <div class="ml-auto">
            <Button
              icon="pi pi-times"
              variant="text"
              size="small"
              @click="operationResult = null"
              class="text-gray-500 hover:text-gray-700 p-2"
              v-tooltip.left="'Dismiss message'"
            />
          </div>
        </div>
      </div>

      <!-- Two Column Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Left Column - Payment Options -->
        <div class="space-y-6">
          <!-- Payment Form -->
          <CheckoutPaymentForm
            v-if="patientId"
            :encounterId="encounterId"
            :patientId="patientId"
            @paymentSuccess="handlePaymentSuccess"
            @paymentError="handlePaymentError"
            @paymentSkipped="handlePaymentSkipped"
          />

          <!-- Loading state for payment form -->
          <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">Payment Options</h2>
            </div>
            <div class="p-6 flex items-center justify-center">
              <div class="flex items-center space-x-2 text-gray-500">
                <i class="pi pi-spin pi-spinner"></i>
                <span>Loading payment options...</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column - Checkout Actions -->
        <div class="space-y-6">
          <!-- Checkout Actions -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-2xl font-semibold text-gray-900">Complete Checkout</h2>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <Button
                  @click="completeCheckout"
                  :label="isCheckingOut ? 'Processing Checkout...' : 'Complete Checkout'"
                  :loading="isCheckingOut"
                  :disabled="isCheckingOut"
                  class="w-full py-4 text-lg font-semibold"
                  severity="success"
                />
                <Button
                  @click="router.push({ name: 'dashboard' })"
                  label="Cancel"
                  variant="outlined"
                  class="w-full py-4 text-lg"
                  :disabled="isCheckingOut"
                />
              </div>
              <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div class="flex items-start">
                  <i class="pi pi-info-circle text-blue-600 mt-1 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium text-blue-800">Checkout Information</p>
                    <p class="text-sm text-blue-700 mt-1">
                      Checkout will finalize this encounter. Payment can be processed before or after checkout.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { api } from '@/api'
import Button from 'primevue/button'
import CheckoutPaymentForm from '@/components/CheckoutPaymentForm.vue'
import type { EncounterDetailsResponse, ProcessPaymentResponse } from '@/api/api-reference'
import { formatDate } from '@/utils/timeMethods'

const route = useRoute()
const router = useRouter()
const encounterId = route.params.encounterId as string

const patientId = ref<string>('')
const encounterDetails = ref<EncounterDetailsResponse | null>(null)
const operationResult = ref<{ success: boolean; message: string } | null>(null)
const isCheckingOut = ref(false)

const handlePaymentSuccess = (result: ProcessPaymentResponse) => {
  operationResult.value = {
    success: result.success || false,
    message: result.message || 'Payment processed successfully'
  }

  // Keep success message visible until manually dismissed
  // No auto-hide for payment success to ensure user sees the result
}

const handlePaymentError = (error: string) => {
  operationResult.value = { success: false, message: error }
}

const handlePaymentSkipped = () => {
  operationResult.value = {
    success: true,
    message: 'Payment skipped. You can process payment later if needed.'
  }

  setTimeout(() => {
    operationResult.value = null
  }, 3000)
}

const completeCheckout = async () => {
  isCheckingOut.value = true
  try {
    await api.encounter.encounterCheckoutEncounter({
      encounterId: encounterId
    })

    operationResult.value = {
      success: true,
      message: 'Checkout completed successfully! Encounter has been finalized.'
    }

    // Redirect to dashboard after successful checkout
    setTimeout(() => {
      router.push({ name: 'dashboard' })
    }, 2000)

  } catch (error) {
    console.error('Checkout failed', error)
    operationResult.value = {
      success: false,
      message: 'Checkout failed. Please try again.'
    }
  } finally {
    isCheckingOut.value = false
  }
}

onMounted(async () => {
  try {
    // Fetch encounter details to get patientId and encounter info
    const response = await api.encounter.encounterGetEncounterById(encounterId)
    encounterDetails.value = response.data
    patientId.value = response.data.patientId || ''

    if (!patientId.value) {
      console.warn('No patientId found in encounter details for encounter:', encounterId)
      operationResult.value = {
        success: false,
        message: 'Unable to load patient information for this encounter.'
      }
    }
  } catch (error) {
    console.error('Failed to fetch encounter details', error)
    operationResult.value = {
      success: false,
      message: 'Failed to load encounter details. Please try again.'
    }
  }
})
</script>
