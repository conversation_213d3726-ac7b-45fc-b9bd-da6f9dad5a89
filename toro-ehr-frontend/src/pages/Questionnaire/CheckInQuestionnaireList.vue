<template>
  <div class="organization-page mx-4 max-w-screen-xl sm:mx-8">
    <h2 class="border-b py-6 text-4xl font-semibold">Check-In Questionnaires</h2>

    <div class="grid grid-cols-8 pt-3 sm:grid-cols-10">
      <div class="col-span-2 hidden sm:block">
        <ul v-for="(items, location) in groupedQuestionnaires" :key="location" class="mb-6">
          <li class="px-2 py-1 text-sm font-bold text-gray-400 uppercase">{{ location }}</li>
          <li
            v-for="q in items"
            :key="q.id"
            :class="[
              'mt-2 cursor-pointer border-l-2 px-2 py-1 text-sm font-semibold transition',
              getActiveClass(`questionnaire-${q.id}`),
            ]"
            @click="selectSection(`questionnaire-${q.id}`)"
          >
            {{ q.title }}
          </li>
        </ul>
      </div>

      <div class="col-span-8 overflow-hidden rounded-xl sm:bg-gray-50 sm:px-8 sm:shadow">
        <div v-for="q in questionnaires" :key="q.id">
          <div v-show="selectedSection === `questionnaire-${q.id}`" class="pt-4">
            <h1 class="py-2 text-2xl font-semibold">{{ q.title }}</h1>
            <Divider />
            <QuestionList
              :questionnaire="q"
              :encounter-id="encounterId"
              @submitted="markAsSubmitted(q.id!)"
              @next="goToNextQuestionnaire"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Divider from 'primevue/divider'
import QuestionList from '../Questionnaire/QuestionList.vue'
import { api } from '@/api'
import type { PatientQuestionnaireResponse } from '@/api/api-reference.ts'
import { useAuthStore } from '@/stores/auth.ts'

const router = useRouter()
const route = useRoute()

const authStore = useAuthStore()

const questionnaires = ref<PatientQuestionnaireResponse[]>([])
const selectedSection = ref('')
const submitted = ref<Set<string>>(new Set())

const encounterId = route.params.encounterId as string

onBeforeMount(async () => {
  const response = await api.questionnaires.questionnaireListPatientQuestionnaires({
    placement: 'CheckIn',
    encounterId: encounterId,
  })
  questionnaires.value = response.data

  const sectionParam = route.params.section as string | undefined
  const firstId =
    questionnaires.value.length > 0 ? `questionnaire-${questionnaires.value[0].id}` : ''
  selectedSection.value = sectionParam ?? firstId
})

const groupedQuestionnaires = computed(() => {
  return questionnaires.value.reduce(
    (acc, q) => {
      const loc = q.locationName || 'Unknown Location'
      if (!acc[loc]) acc[loc] = []
      acc[loc].push(q)
      return acc
    },
    {} as Record<string, PatientQuestionnaireResponse[]>,
  )
})

const markAsSubmitted = async (id: string) => {
  submitted.value.add(id)
  const allSubmitted = questionnaires.value.every((q) => submitted.value.has(q.id!))

  if (allSubmitted) {
    await api.encounter.encounterUpdateStatus({ encounterId: encounterId, status: 'CheckedIn' })

    if (authStore.user?.patientId) {
      await router.push({ name: 'patient-appointments' })
    } else {
      await router.push({ name: 'dashboard' })
    }
  }
}

const selectSection = (sectionId: string) => {
  selectedSection.value = sectionId
}

const getActiveClass = (sectionId: string) => {
  return selectedSection.value === sectionId
    ? 'border-l-primary text-primary'
    : 'border-transparent'
}

const goToNextQuestionnaire = () => {
  const currentIndex = questionnaires.value.findIndex(
    (q) => `questionnaire-${q.id}` === selectedSection.value,
  )
  if (currentIndex < questionnaires.value.length - 1) {
    // Go to next questionnaire
    selectSection(`questionnaire-${questionnaires.value[currentIndex + 1].id}`)
  }
  // Note: For check-in questionnaires, we don't auto-navigate away since the markAsSubmitted
  // function already handles the final navigation when all questionnaires are completed
}
</script>
