<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[99]"
  >
    <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md max-h-[90vh] overflow-hidden">
      <!-- Title -->
      <h2 class="text-xl font-bold mb-4">Questionnaire Details</h2>

      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Questionnaire Info -->
        <div class="mb-4 p-4 bg-gray-100 rounded-lg shadow-sm">
          <p class="text-lg font-semibold">{{ questionnaire?.title }}</p>
          <p class="text-sm text-gray-600">Classification: {{ questionnaire?.classification }}</p>
          <p class="text-sm text-gray-600">Placement: {{ questionnaire?.placement }}</p>
        </div>

        <!-- Questions -->
        <div class="space-y-3">
          <div
            v-for="(question, index) in questionnaire?.questions"
            :key="index"
            class="p-4 border border-gray-300 rounded-lg shadow-sm bg-white"
          >
            <p class="font-semibold text-gray-800">{{ question.text || 'No question text' }}</p>
            <p class="text-sm text-blue-600">Type: {{ question.type || 'Unknown' }}</p>
            <ul v-if="question.options && question.options.length" class="mt-2 list-disc pl-4 text-gray-700">
              <li v-for="(option, i) in question.options" :key="i">{{ option }}</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Close Button -->
      <div class="mt-4 flex justify-end border-t pt-4">
        <Button class="px-4 py-2" label="Close" severity="secondary" variant="text" @click="$emit('close')" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {watchEffect, ref} from 'vue'
import { api } from '@/api'
import type {QuestionnaireDetailsResponse} from '@/api/api-reference.ts'
import Button from 'primevue/button'

const questionnaire = ref<QuestionnaireDetailsResponse>();

const emit = defineEmits(['close'])
const props = defineProps<{
  questionnaireId: string
  isModalOpen: boolean
}>()

watchEffect(async () => {
  if (props.questionnaireId) {
    const response = await api.questionnaires.questionnaireGetQuestionnaire(props.questionnaireId);
    questionnaire.value = response.data
  }
});

</script>
