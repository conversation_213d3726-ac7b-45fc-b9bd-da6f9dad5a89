<template>
  <div class="organization-page mx-4 max-w-screen-xl sm:mx-8">
    <h2 class="border-b py-6 text-4xl font-semibold">Questionnaires</h2>

    <div class="grid grid-cols-8 pt-3 sm:grid-cols-10">
      <!-- Mobile Dropdown -->
      <div class="relative my-4 w-56 sm:hidden">
        <input class="peer hidden" type="checkbox" id="dropdown-toggle" />
        <label
          for="dropdown-toggle"
          class="flex w-full cursor-pointer select-none rounded-lg border p-2 px-3 text-sm text-gray-700 ring-blue-700 peer-checked:ring"
        >
          {{ getSelectedSectionLabel }}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="ml-auto h-4 text-slate-700 transition peer-checked:rotate-180"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
          </svg>
        </label>
        <ul
          class="max-h-0 select-none flex-col overflow-hidden rounded-b-lg shadow-md transition-all duration-300 peer-checked:max-h-56 peer-checked:py-3"
        >
          <template v-for="(items, location) in groupedQuestionnaires" :key="location">
            <li class="px-3 py-2 text-xs font-semibold text-gray-400 uppercase">{{ location }}</li>
            <li
              v-for="q in items"
              :key="q.id"
              class="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-toroblue-600 hover:text-white"
              @click="selectSection(`questionnaire-${q.id}`)"
            >
              {{ q.title }}
            </li>
          </template>
        </ul>
      </div>

      <!-- Sidebar -->
      <div class="col-span-2 hidden sm:block">
        <ul v-for="(items, location) in groupedQuestionnaires" :key="location" class="mb-6">
          <li class="px-2 py-1 text-sm font-bold text-gray-400 uppercase">{{ location }}</li>
          <li
            v-for="q in items"
            :key="q.id"
            :class="[
              'mt-2 cursor-pointer border-l-2 px-2 py-1 text-sm font-semibold transition',
              getActiveClass(`questionnaire-${q.id}`),
            ]"
            @click="selectSection(`questionnaire-${q.id}`)"
          >
            {{ q.title }}
          </li>
        </ul>
      </div>

      <!-- Content -->
      <div class="col-span-8 overflow-hidden rounded-xl sm:bg-gray-50 sm:px-8 sm:shadow">
        <div v-for="q in questionnaires" :key="q.id">
          <div v-show="selectedSection === `questionnaire-${q.id}`" class="pt-4">
            <h1 class="py-2 text-2xl font-semibold">{{ q.title }}</h1>
            <Divider />
            <QuestionList
              :questionnaire="q"
              :encounter-id="null"
              @submitted="() => {}"
              @next="goToNextQuestionnaire"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Divider from 'primevue/divider'
import QuestionList from '../Questionnaire/QuestionList.vue'
import { api } from '@/api'
import type { PatientQuestionnaireResponse } from '@/api/api-reference.ts'

const route = useRoute()
const router = useRouter()
const questionnaires = ref<PatientQuestionnaireResponse[]>([])
const selectedSection = ref('')

onBeforeMount(async () => {
  const response = await api.questionnaires.questionnaireListPatientQuestionnaires({
    placement: 'Profile',
  })
  questionnaires.value = response.data

  const sectionParam = route.params.section as string | undefined
  const validSectionIds = questionnaires.value.map((q) => `questionnaire-${q.id}`)

  if (sectionParam && validSectionIds.includes(sectionParam)) {
    selectedSection.value = sectionParam
  } else if (questionnaires.value.length > 0) {
    selectSection(`questionnaire-${questionnaires.value[0].id}`)
  }
})

watch(
  () => route.params.section,
  (newSection) => {
    if (typeof newSection === 'string') {
      selectedSection.value = newSection
    }
  },
)

const groupedQuestionnaires = computed(() => {
  return questionnaires.value.reduce(
    (acc, q) => {
      const loc = q.locationName || 'Unknown Location'
      if (!acc[loc]) acc[loc] = []
      acc[loc].push(q)
      return acc
    },
    {} as Record<string, PatientQuestionnaireResponse[]>,
  )
})

const getSelectedSectionLabel = computed(() => {
  const section = questionnaires.value.find(
    (q) => `questionnaire-${q.id}` === selectedSection.value,
  )
  return section?.title ?? 'Select Section'
})

const selectSection = (sectionId: string) => {
  router.push({ name: 'patient-questionnaires', params: { section: sectionId } })
}

const getActiveClass = (sectionId: string) => {
  return selectedSection.value === sectionId
    ? 'border-l-primary text-primary'
    : 'border-transparent'
}

const goToNextQuestionnaire = () => {
  const currentIndex = questionnaires.value.findIndex(
    (q) => `questionnaire-${q.id}` === selectedSection.value,
  )
  if (currentIndex < questionnaires.value.length - 1) {
    // Go to next questionnaire
    selectSection(`questionnaire-${questionnaires.value[currentIndex + 1].id}`)
  } else {
    // If we're at the last questionnaire, go to appointments
    router.push('/patient-appointments')
  }
}
</script>
