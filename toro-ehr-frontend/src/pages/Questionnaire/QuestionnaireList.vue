<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Questionnaires
    </h2>

    <!-- My Organization Questionnaires -->
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filterMyQuestionnaires()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="mySearch"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <i class="pi pi-search text-gray-400"></i>
            </div>
          </div>
        </template>
        <template #buttons>
          <Select
            v-model="myFilterSelected"
            :options="myFilterOptions"
            optionLabel="text"
            option-value="value"
            placeholder="Filter"
            class="w-full md:w-56"
            @change="filterMyQuestionnaires"
          />
          <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-toroblue-600 focus:outline-none focus:bg-toroblue-600 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click.prevent="router.push({ name: 'add-questionnaire' })"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Create questionnaire
          </a>
        </template>
      </TableHeader>
      <!-- My Questionnaires Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Title
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Classification
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Placement
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Organization
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in myItemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{ row.title }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-500">{{
                  row.classification
                }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.placement }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.locationName }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.organizationName }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4">
                <a
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="openDetailsModal(row.id!)"
                >
                  View
                </a>
                <a
                  v-if="row.isFromLocation"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="
                    router.push({ name: 'edit-questionnaire', params: { id: row.id } })
                  "
                >
                  Edit
                </a>
                <a
                  v-if="row.isFromLocation"
                  class="inline-flex items-center gap-x-1 text-sm text-red-600 font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="openDeactivateModal(row.id!, row.title!)"
                >
                  Deactivate
                </a>
                <a
                  v-if="!row.isFromLocation"
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="
                    router.push({ name: 'copy-questionnaire', params: { id: row.id } })
                  "
                >
                  Copy
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End My Questionnaires Table -->

      <TableFooter
        :totalItems="myTotalItems"
        :isFirstPage="myIsFirstPage"
        :isLastPage="myIsLastPage"
        @prevPage="myPrevPage"
        @nextPage="myNextPage"
      />
    </TableSection>

    <!-- Other Questionnaires -->
    <div class="mt-8">
      <TableSection>
        <TableHeader>
          <template #inputs>
            <div class="relative max-w-xs">
              <label class="sr-only">Search</label>
              <input
                @input="filterOtherQuestionnaires()"
                type="text"
                class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                placeholder="Search"
                v-model="otherSearch"
              />
              <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
                <i class="pi pi-search text-gray-400"></i>
              </div>
            </div>
          </template>
        </TableHeader>

        <!-- Other Questionnaires Table -->
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="ps-6 py-3 text-start">
                <div class="flex items-center gap-x-2">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Title
                  </span>
                </div>
              </th>
              <th scope="col" class="ps-6 py-3 text-start">
                <div class="flex items-center gap-x-2">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Classification
                  </span>
                </div>
              </th>
              <th scope="col" class="ps-6 py-3 text-start">
                <div class="flex items-center gap-x-2">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Placement
                  </span>
                </div>
              </th>
              <th scope="col" class="ps-6 py-3 text-start">
                <div class="flex items-center gap-x-2">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Location
                  </span>
                </div>
              </th>
              <th scope="col" class="ps-6 py-3 text-start">
                <div class="flex items-center gap-x-2">
                  <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                    Organization
                  </span>
                </div>
              </th>
              <th scope="col" class="px-6 py-3 text-end"></th>
            </tr>
          </thead>

          <tbody class="divide-y divide-gray-200">
            <tr v-for="row in otherItemList" :key="row.id">
              <td class="h-px w-72 whitespace-nowrap">
                <div class="px-6 py-3">
                  <span class="block text-sm font-semibold text-gray-800">{{ row.title }}</span>
                </div>
              </td>

              <td class="h-px w-72 whitespace-nowrap">
                <div class="px-6 py-3">
                  <span class="block text-sm font-semibold text-gray-500">{{
                    row.classification
                  }}</span>
                </div>
              </td>

              <td class="h-px w-72 whitespace-nowrap">
                <div class="px-6 py-3">
                  <span class="block text-sm text-gray-500">{{ row.placement }}</span>
                </div>
              </td>

              <td class="h-px w-72 whitespace-nowrap">
                <div class="px-6 py-3">
                  <span class="block text-sm text-gray-500">{{ row.locationName }}</span>
                </div>
              </td>

              <td class="h-px w-72 whitespace-nowrap">
                <div class="px-6 py-3">
                  <span class="block text-sm text-gray-500">{{ row.organizationName }}</span>
                </div>
              </td>

              <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
                <div class="flex items-right gap-x-4">
                  <a
                    class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="openDetailsModal(row.id!)"
                  >
                    View
                  </a>
                  <a
                    v-if="row.isFromLocation"
                    class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="
                      router.push({ name: 'edit-questionnaire', params: { id: row.id } })
                    "
                  >
                    Edit
                  </a>
                  <a
                    v-if="row.isFromLocation"
                    class="inline-flex items-center gap-x-1 text-sm text-red-600 font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="openDeactivateModal(row.id!, row.title!)"
                  >
                    Deactivate
                  </a>
                  <a
                    v-if="!row.isFromLocation"
                    class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="
                      router.push({ name: 'copy-questionnaire', params: { id: row.id } })
                    "
                  >
                    Copy
                  </a>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <!-- End Other Questionnaires Table -->

        <TableFooter
          :totalItems="otherTotalItems"
          :isFirstPage="otherIsFirstPage"
          :isLastPage="otherIsLastPage"
          @prevPage="otherPrevPage"
          @nextPage="otherNextPage"
        />
      </TableSection>
    </div>
    <QuestionnaireDetails
      :isModalOpen="isDetailsModalOpen"
      :questionnaire-id="questionnaireDetailsId"
      @close="closeDetailsModal"
    />
    <ConfirmDialog
      ref="confirmDialog"
      @confirmedAction="deactivateQuestionnaire"
      title="Deactivate Questionnaire"
      :message="deactivateMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '@/api'
import type { QuestionnaireResponse } from '@/api/api-reference.ts'
import { PlusIcon } from '@heroicons/vue/24/outline'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import router from '../../router'
import debounce from 'lodash.debounce'
import Select from 'primevue/select'
import QuestionnaireDetails from './QuestionnaireDetails.vue'
import ConfirmDialog from '@/components/form-extensions/ConfirmDialog.vue'

// My Organization & Location Questionnaires
const myFilterOptions = ref([
  { text: 'My Organization', value: 'organization' },
  { text: 'My Location', value: 'location' },
])

const myFilterSelected = ref('organization')
const myItemList = ref<QuestionnaireResponse[]>()
const myPageNumber = ref(1)
const myTotalPages = ref(1)
const myTotalItems = ref(0)
const mySearch = ref('')

// Other Questionnaires
const otherItemList = ref<QuestionnaireResponse[]>()
const otherPageNumber = ref(1)
const otherTotalPages = ref(1)
const otherTotalItems = ref(0)
const otherSearch = ref('')

const limit = 10
const isDetailsModalOpen = ref(false)
const questionnaireDetailsId = ref('')

// Deactivate functionality
const confirmDialog = ref()
const questionnaireToDeactivate = ref('')
const deactivateMessage = ref('')

onMounted(async () => {
  await fetchMyQuestionnaires()
  await fetchOtherQuestionnaires()
})

// My Questionnaires computed properties and methods
const myIsFirstPage = computed(() => myPageNumber.value === 1)
const myIsLastPage = computed(() => myPageNumber.value === myTotalPages.value)

const myNextPage = async () => {
  if (!myIsLastPage.value) {
    myPageNumber.value++
    await fetchMyQuestionnaires()
  }
}

const myPrevPage = async () => {
  if (!myIsFirstPage.value) {
    myPageNumber.value--
    await fetchMyQuestionnaires()
  }
}

const fetchMyQuestionnaires = async () => {
  try {
    const result = await api.questionnaires.questionnaireListQuestionnaires({
      pageNumber: myPageNumber.value,
      pageSize: limit,
      searchParam: mySearch.value,
      locationFilter: myFilterSelected.value,
    })
    myItemList.value = result.data.items
    myPageNumber.value = result.data.pageNumber ?? 1
    myTotalPages.value = result.data.totalPages ?? 1
    myTotalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching my questionnaires:', error)
  }
}

const filterMyQuestionnaires = debounce(() => {
  myPageNumber.value = 1
  fetchMyQuestionnaires()
}, 500)

// Other Questionnaires computed properties and methods
const otherIsFirstPage = computed(() => otherPageNumber.value === 1)
const otherIsLastPage = computed(() => otherPageNumber.value === otherTotalPages.value)

const otherNextPage = async () => {
  if (!otherIsLastPage.value) {
    otherPageNumber.value++
    await fetchOtherQuestionnaires()
  }
}

const otherPrevPage = async () => {
  if (!otherIsFirstPage.value) {
    otherPageNumber.value--
    await fetchOtherQuestionnaires()
  }
}

const fetchOtherQuestionnaires = async () => {
  try {
    const result = await api.questionnaires.questionnaireListQuestionnaires({
      pageNumber: otherPageNumber.value,
      pageSize: limit,
      searchParam: otherSearch.value,
      locationFilter: 'other',
    })
    otherItemList.value = result.data.items
    otherPageNumber.value = result.data.pageNumber ?? 1
    otherTotalPages.value = result.data.totalPages ?? 1
    otherTotalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching other questionnaires:', error)
  }
}

const filterOtherQuestionnaires = debounce(() => {
  otherPageNumber.value = 1
  fetchOtherQuestionnaires()
}, 500)

const openDetailsModal = (id: string) => {
  questionnaireDetailsId.value = id
  isDetailsModalOpen.value = true
}
const closeDetailsModal = () => {
  questionnaireDetailsId.value = ''
  isDetailsModalOpen.value = false
}

const openDeactivateModal = (id: string, title: string) => {
  questionnaireToDeactivate.value = id
  deactivateMessage.value = `Are you sure you want to deactivate the questionnaire "${title}"? This action cannot be undone.`
  confirmDialog.value?.open(0)
}

const deactivateQuestionnaire = async () => {
  try {
    await api.questionnaires.questionnaireDeactivateQuestionnaire(questionnaireToDeactivate.value)
    // refresh both tables after deactivation
    await fetchMyQuestionnaires()
    await fetchOtherQuestionnaires()
  } catch (error) {
    console.error('Error deactivating questionnaire:', error)
  }
}
</script>
