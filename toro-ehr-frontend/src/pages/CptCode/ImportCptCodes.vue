<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">Import CPT codes</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="importCptCodes">
          <InputFile
            id="file"
            label="File"
            wrapperClass="mb-2"
            v-model="selectedFile"
            accept=".xlsx"
          />
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="importCptCodes" class="bg-primary text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { ref } from 'vue'
import { api } from '../../api'
import * as yup from 'yup'
import InputFile from '../../components/form-extensions/InputFile.vue'

const emit = defineEmits(['close'])
defineProps<{
  isModalOpen: boolean
}>()

const selectedFile = ref<File | undefined>(undefined)

const { handleSubmit } = useForm({
  validationSchema: yup.object({
    file: yup.mixed<File>().required('File is required.'),
  }),
})

const importCptCodes = handleSubmit(async () => {
  try {
    await api.cptcodes.cptCodesImport({ File: selectedFile.value as File })
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
