<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">View Allergie</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700">Allergie Code:</label>
          <p class="text-gray-900 font-semibold">{{ allergie.code }}</p>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700">Description:</label>
          <p class="text-gray-900">{{ allergie.displayName }}</p>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700">Code System:</label>
          <p class="text-gray-900">{{ allergie.codeSystemName }} | {{ allergie.codeSystem }}</p>
        </div>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CodingResponse } from '../../api/api-reference'

defineEmits(['close'])
defineProps<{
  isModalOpen: boolean
  allergie: CodingResponse
}>()
</script>
