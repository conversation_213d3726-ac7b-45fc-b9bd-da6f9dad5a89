<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">View Loinc Code</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-400">Loinc Code:</label>
          <p class="text-gray-900 font-semibold">{{ loincCode?.loincNum }}</p>
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-400">Description:</label>
          <p class="text-gray-900">{{ loincCode?.component }}</p>
        </div>
        <div class="mb-4">
          <label v-if="loincCode?.property" class="block text-sm font-medium text-gray-400"
            >Property:</label
          >
          <p class="text-gray-900">{{ loincCode?.property }}</p>
        </div>
        <div class="mb-4">
          <label v-if="loincCode?.timeAspct" class="block text-sm font-medium text-gray-400"
            >TimeAspct:</label
          >
          <p class="text-gray-900">{{ loincCode?.timeAspct }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.system" class="block text-sm font-medium text-gray-400"
            >System:</label
          >
          <p class="text-gray-900">{{ loincCode?.system }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.scaleTyp" class="block text-sm font-medium text-gray-400"
            >ScaleTyp:</label
          >
          <p class="text-gray-900">{{ loincCode?.scaleTyp }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.methodTyp" class="block text-sm font-medium text-gray-400"
            >MethodTyp:</label
          >
          <p class="text-gray-900">{{ loincCode?.methodTyp }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.class" class="block text-sm font-medium text-gray-400"
            >Class:</label
          >
          <p class="text-gray-900">{{ loincCode?.class }}</p>
        </div>

        <div class="mb-4">
          <label
            v-if="loincCode?.versionLastChanged"
            class="block text-sm font-medium text-gray-400"
            >VersionLastChanged:</label
          >
          <p class="text-gray-900">{{ loincCode?.versionLastChanged }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.chngType" class="block text-sm font-medium text-gray-400"
            >ChngType:</label
          >
          <p class="text-gray-900">{{ loincCode?.chngType }}</p>
        </div>

        <div class="mb-4">
          <label
            v-if="loincCode?.definitionDescription"
            class="block text-sm font-medium text-gray-400"
            >DefinitionDescription:</label
          >
          <p class="text-gray-900">{{ loincCode?.definitionDescription }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.status" class="block text-sm font-medium text-gray-400"
            >Status:</label
          >
          <p class="text-gray-900">{{ loincCode?.status }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.consumerName" class="block text-sm font-medium text-gray-400"
            >ConsumerName:</label
          >
          <p class="text-gray-900">{{ loincCode?.consumerName }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.classtype" class="block text-sm font-medium text-gray-400"
            >Classtype:</label
          >
          <p class="text-gray-900">{{ loincCode?.classtype }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.formula" class="block text-sm font-medium text-gray-400"
            >Formula:</label
          >
          <p class="text-gray-900">{{ loincCode?.formula }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.exmplAnswers" class="block text-sm font-medium text-gray-400"
            >ExmplAnswers:</label
          >
          <p class="text-gray-900">{{ loincCode?.exmplAnswers }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.surveyQuestText" class="block text-sm font-medium text-gray-400"
            >SurveyQuestText:</label
          >
          <p class="text-gray-900">{{ loincCode?.surveyQuestText }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.surveyQuestSrc" class="block text-sm font-medium text-gray-400"
            >SurveyQuestSrc:</label
          >
          <p class="text-gray-900">{{ loincCode?.surveyQuestSrc }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.unitsRequired" class="block text-sm font-medium text-gray-400"
            >UnitsRequired:</label
          >
          <p class="text-gray-900">{{ loincCode?.unitsRequired }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.relatedNames2" class="block text-sm font-medium text-gray-400"
            >RelatedNames2:</label
          >
          <p class="text-gray-900">{{ loincCode?.relatedNames2 }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.shortName" class="block text-sm font-medium text-gray-400"
            >ShortName:</label
          >
          <p class="text-gray-900">{{ loincCode?.shortName }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.orderObs" class="block text-sm font-medium text-gray-400"
            >OrderObs:</label
          >
          <p class="text-gray-900">{{ loincCode?.orderObs }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.longCommonName" class="block text-sm font-medium text-gray-400"
            >LongCommonName:</label
          >
          <p class="text-gray-900">{{ loincCode?.longCommonName }}</p>
        </div>

        <div class="mb-4">
          <label v-if="loincCode?.displayName" class="block text-sm font-medium text-gray-400"
            >DisplayName:</label
          >
          <p class="text-gray-900">{{ loincCode?.displayName }}</p>
        </div>
      </div>
      <div class="flex justify-end">
        <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="closeModal">
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { api } from '../../api'
import type { LoincCodeDetailsResponse } from '../../api/api-reference'

const loincCode = ref<LoincCodeDetailsResponse>()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  loincCodeId: string
}>()

watch(
  () => props.loincCodeId,
  async (newValue) => {
    const response = await api.loinc.loincCodeGetLoincCodeById(newValue)
    loincCode.value = response.data
  },
)

const closeModal = () => {
  loincCode.value = undefined
  emit('close')
}
</script>
