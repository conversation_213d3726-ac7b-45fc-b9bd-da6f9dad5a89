<template>
  <div class="organization-page">
    <div class="mx-4 max-w-screen-xl sm:mx-8">
      <h2 class="border-b py-6 text-4xl font-semibold">Profile</h2>
      <div class="grid grid-cols-8 pt-3 sm:grid-cols-10">
        <!-- Mobile Dropdown Menu -->
        <div class="relative my-4 w-56 sm:hidden">
          <input class="peer hidden" type="checkbox" id="dropdown-toggle" />
          <label
            for="dropdown-toggle"
            class="flex w-full cursor-pointer select-none rounded-lg border p-2 px-3 text-sm text-gray-700 ring-toroblue-600 peer-checked:ring"
          >
            {{ getSelectedSectionLabel }}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="ml-auto h-4 text-slate-700 transition peer-checked:rotate-180"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </label>
          <ul
            class="max-h-0 select-none flex-col overflow-hidden rounded-b-lg shadow-md transition-all duration-300 peer-checked:max-h-56 peer-checked:py-3"
          >
            <li
              v-for="section in sections"
              :key="section.id"
              class="cursor-pointer px-3 py-2 text-sm text-slate-600 hover:bg-toroblue-600 hover:text-white"
              @click="selectSection(section.id)"
            >
              {{ section.label }}
            </li>
          </ul>
        </div>

        <!-- Sidebar Navigation (Visible on Larger Screens) -->
        <div class="col-span-2 hidden sm:block">
          <ul>
            <li
              v-for="section in sections"
              :key="section.id"
              class="mt-5 cursor-pointer border-l-2 px-2 py-2 font-semibold transition"
              :class="getActiveClass(section.id)"
              @click="selectedSection = section.id"
            >
              {{ section.label }}
            </li>
          </ul>
        </div>

        <!-- Content Area -->
        <div class="col-span-8 overflow-hidden rounded-xl sm:bg-gray-50 sm:px-8 sm:shadow">
          <div v-if="selectedSection">
            <div class="pt-4">
              <h1 class="py-2 text-2xl font-semibold">{{ getSelectedSectionLabel }}</h1>
            </div>
            <Divider />
            <component :is="selectedSectionComponent" />
            <div class="flex justify-end"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent, shallowRef, watch, onMounted } from 'vue'
import Divider from 'primevue/divider'
import { useAuthStore } from '@/stores/auth'
import { usePractitionerStore } from '@/stores/practitioner'
import type { UserInfoResponse } from '../../../api/api-reference'

const authStore = useAuthStore()
const practitionerStore = usePractitionerStore()

const fetchData = async (user: UserInfoResponse) => {
  try {
    practitionerStore.getLocationEmployee(user.employeeId!, user.locationId!)
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }
}

onMounted(async () => {
  if (authStore.user) {
    fetchData(authStore.user)
  }
})

watch(
  () => authStore.user,
  async (user) => {
    if (user) {
      fetchData(user)
    }
  },
  { immediate: true },
)

// Define sections to avoid repetition
const sections = shallowRef([
  {
    id: 'generalSettings',
    label: 'General Settings',
    component: defineAsyncComponent(() => import('./GeneralSettings/GeneralSettings.vue')),
  },
  {
    id: 'officeHours',
    label: 'Office Hours',
    component: defineAsyncComponent(() => import('./OfficeHours/OfficeHours.vue')),
  },
  {
    id: 'outOfOfficeHours',
    label: 'Out of Office',
    component: defineAsyncComponent(() => import('./OutOfOffice/OutOfOffice.vue')),
  },
])

const selectedSection = ref('generalSettings')

// Function to determine active class for sidebar
const getActiveClass = (sectionId: string) => {
  return selectedSection.value === sectionId
    ? 'border-l-primary text-primary'
    : 'border-transparent'
}

// Computed property to dynamically load the selected section component
const selectedSectionComponent = computed(() => {
  const section = sections.value.find((s) => s.id === selectedSection.value)
  return section ? section.component : null
})

// Computed property to get selected section label
const getSelectedSectionLabel = computed(() => {
  const section = sections.value.find((s) => s.id === selectedSection.value)
  return section ? section.label : 'Select Section'
})

// Function to handle section selection from dropdown
const selectSection = (sectionId: string) => {
  selectedSection.value = sectionId
  const dropdownToggle = document.getElementById('dropdown-toggle') as HTMLInputElement
  if (dropdownToggle) {
    dropdownToggle.checked = false
  }
}
</script>
