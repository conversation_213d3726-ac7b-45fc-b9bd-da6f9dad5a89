<template>
  <h2 class="text-lg font-bold mb-4">Exclusion Hours</h2>
  <form @submit.prevent="addExclusionHours">
    <div class="flex items-center gap-2">
      <DateTimePicker
        id="from"
        label="From"
        timeOnly
        hourFormat="12"
        :stepMinute="15"
        :disabled="!isDisabled"
      />
      <DateTimePicker
        id="to"
        label="To"
        timeOnly
        hourFormat="12"
        :stepMinute="15"
        :disabled="!isDisabled"
      />
    </div>
    <div class="flex items-center gap-2 w-full -mt-2">
      <InputText id="title" label="Title (optional)" class="flex-1" :disabled="!isDisabled" />
      <Button @click="addExclusionHours">Add</Button>
    </div>
  </form>
  <table class="min-w-full divide-y divide-gray-200">
    <tbody class="divide-y divide-gray-200">
      <tr v-for="(row, index) in modelValue" :key="index">
        <td class="h-px w-48 whitespace-nowrap">
          <div class="px-6 py-3">
            <span class="block text-sm text-gray-500">{{ row.title }}</span>
          </div>
        </td>

        <td class="h-px w-48 whitespace-nowrap">
          <div class="px-6 py-3">
            <span class="block text-sm text-gray-500">{{ displayTimeSpan(row.from) }}</span>
          </div>
        </td>

        <td class="h-px w-48 whitespace-nowrap">
          <div class="px-6 py-3">
            <span class="block text-sm text-gray-500">{{ displayTimeSpan(row.to) }}</span>
          </div>
        </td>

        <td class="size-px whitespace-nowrap">
          <div class="px-6 py-1.5">
            <Button
              icon="pi pi-trash"
              aria-label="delete"
              variant="link"
              @click="removeExclusionHours(index)"
            />
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import type { Exclusion } from '../../../../api/api-reference'
import * as yup from 'yup'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import { displayTimeSpan, parseDateToTimeString } from '@/utils/timeMethods'
import Button from 'primevue/button'

const props = defineProps<{ modelValue: Exclusion[]; isDisabled: boolean }>()
const emit = defineEmits(['update:modelValue'])

const schema = yup.object({
  from: yup.string().required('From time is required'),
  to: yup
    .string()
    .required('To time is required')
    .test('is-greater', 'To must be later than From', function (value) {
      const { from } = this.parent
      if (!from || !value) return true
      return value > from
    }),
  title: yup.string().nullable(),
})

const { handleSubmit, resetForm } = useForm({
  validationSchema: schema,
})

const addExclusionHours = handleSubmit(async (values) => {
  const newExclusionHours = {
    from: parseDateToTimeString(values.from),
    to: parseDateToTimeString(values.to),
    title: values.title,
  }
  const updatedExclusionHours = [...props.modelValue, newExclusionHours]
  emit('update:modelValue', updatedExclusionHours)
  resetForm()
})

const removeExclusionHours = (index: number) => {
  const updatedExclusionHours = props.modelValue.filter((_, i) => i !== index)
  emit('update:modelValue', updatedExclusionHours)
}
</script>
