<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">Add Office Hours</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="editOfficeHours">
          <MultiSelect id="days" label="Select days" :options="dayList" />
          <div class="flex items-center gap-2">
            <ToggleSwitch v-model="isOpen" />
            <label>{{ isOpenLabel }}</label>
          </div>
          <div class="flex items-center gap-2 mt-2">
            <DateTimePicker
              id="openTime"
              label="Open"
              timeOnly
              hourFormat="12"
              :stepMinute="15"
              :disabled="!isOpen"
            />
            <DateTimePicker
              id="closeTime"
              label="Close"
              timeOnly
              hourFormat="12"
              :stepMinute="15"
              :disabled="!isOpen"
            />
          </div>
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="editOfficeHours" class="bg-primary text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm, useField } from 'vee-validate'
import { computed } from 'vue'
import { api } from '../../../../api'
import * as yup from 'yup'
import { useAuthStore } from '@/stores/auth'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import MultiSelect from '@/components/form-extensions/MultiSelectFluent.vue'
import { getDays } from '../../../../utils/days'
import ToggleSwitch from 'primevue/toggleswitch'
import type { EditOfficeHoursRequest, OfficeHours } from '../../../../api/api-reference'
import { parseDateToTimeString } from '../../../../utils/timeMethods'

const authStore = useAuthStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  officeHours: OfficeHours[] | undefined
  isModalOpen: boolean
}>()

const initialValues = {
  openTime: '',
  closeTime: '',
  days: [],
  isOpen: true,
}

const dayList = getDays()
const isOpenLabel = computed(() => (isOpen.value ? 'open' : 'closed'))

const schema = yup.object({
  isOpen: yup.boolean().required(),
  openTime: yup.string().when('isOpen', {
    is: true,
    then: (schema) => schema.required('Open time is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  closeTime: yup.string().when('isOpen', {
    is: true,
    then: (schema) =>
      schema
        .required('Close time is required')
        .test('is-greater', 'Close time must be later than Open time', function (value) {
          const { openTime } = this.parent
          if (!openTime || !value) return true // Skip validation if one of them is missing
          return value > openTime // Ensure closeTime is greater than openTime
        }),
    otherwise: (schema) => schema.notRequired(),
  }),
  days: yup
    .array()
    .of(yup.string().required())
    .min(1, 'At least one day is required')
    .required('Days are required'),
})

const { handleSubmit, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const { value: isOpen } = useField<boolean>('isOpen')

const editOfficeHours = handleSubmit(async (values) => {
  try {
    console.log(values)

    const updatedOfficeHours = [...props.officeHours!]
    for (const day of values.days) {
      const index = updatedOfficeHours.findIndex((oh) => oh.day === Number(day))

      if (index !== -1) {
        updatedOfficeHours[index] = {
          ...updatedOfficeHours[index],
          openTime: values.isOpen ? parseDateToTimeString(new Date(values.openTime)) : null,
          closeTime: values.isOpen ? parseDateToTimeString(new Date(values.closeTime)) : null,
        }
      }
    }

    const form: EditOfficeHoursRequest = {
      officeHours: updatedOfficeHours,
    }
    await api.employees.employeeEditOrganizationEmployeeOfficeHours(
      authStore.user!.employeeId!,
      authStore.user!.locationId!,
      form,
    )
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
