<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">Add Exclusion Hours</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="addExclusionHours">
          <MultiSelect id="days" label="Select days" :options="dayList" />
          <div class="flex items-center gap-2 mt-2">
            <DateTimePicker id="from" label="From" timeOnly hourFormat="12" :stepMinute="15" />
            <DateTimePicker id="to" label="To" timeOnly hourFormat="12" :stepMinute="15" />
          </div>
          <InputText id="title" label="Title (optional)" />
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="addExclusionHours" class="bg-primary text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '../../../../api'
import * as yup from 'yup'
import { useAuthStore } from '@/stores/auth'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import MultiSelect from '@/components/form-extensions/MultiSelectFluent.vue'
import InputText from '@/components/form-extensions/InputTextFluent.vue'
import { getDays } from '../../../../utils/days'
import type { EditOfficeHoursRequest, OfficeHours } from '../../../../api/api-reference'
import { parseDateToTimeString } from '../../../../utils/timeMethods'

const authStore = useAuthStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  officeHours: OfficeHours[] | undefined
  isModalOpen: boolean
}>()

const initialValues = {
  from: '',
  to: '',
  title: '',
  days: [],
}

const dayList = getDays()

const schema = yup.object({
  from: yup.string().required('From time is required'),
  to: yup
    .string()
    .required('To time is required')
    .test('is-greater', 'To must be later than From', function (value) {
      const { from } = this.parent
      if (!from || !value) return true
      return value > from
    }),
  days: yup
    .array()
    .of(yup.string().required())
    .min(1, 'At least one day is required')
    .required('Days are required'),
  title: yup.string().nullable(),
})

const { handleSubmit, resetForm } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const addExclusionHours = handleSubmit(async (values) => {
  try {
    console.log(values)

    const updatedOfficeHours = [...props.officeHours!]
    for (const day of values.days) {
      const index = updatedOfficeHours.findIndex((oh) => oh.day === Number(day))

      if (index !== -1) {
        updatedOfficeHours[index].exclusions?.push({
          from: parseDateToTimeString(new Date(values.from)) ?? undefined,
          to: parseDateToTimeString(new Date(values.to)) ?? undefined,
          title: values.title,
        })
      }
    }

    const form: EditOfficeHoursRequest = {
      officeHours: updatedOfficeHours,
    }
    await api.employees.employeeEditOrganizationEmployeeOfficeHours(
      authStore.user!.employeeId!,
      authStore.user!.locationId!,
      form,
    )
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
