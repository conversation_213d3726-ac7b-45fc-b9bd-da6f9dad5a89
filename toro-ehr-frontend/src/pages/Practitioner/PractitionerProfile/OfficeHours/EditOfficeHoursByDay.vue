<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">{{ getDayName(day) }}</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="editOfficeHours">
          <div class="flex items-center gap-2">
            <ToggleSwitch v-model="isOpen" />
            <label>{{ isOpenLabel }}</label>
          </div>
          <div class="flex items-center gap-2 mt-2">
            <DateTimePicker
              id="openTime"
              label="Open"
              timeOnly
              hourFormat="12"
              :stepMinute="15"
              :disabled="!isOpen"
            />
            <DateTimePicker
              id="closeTime"
              label="Close"
              timeOnly
              hourFormat="12"
              :stepMinute="15"
              :disabled="!isOpen"
            />
          </div>
        </form>
        <Divider />
        <EditExlusionHoursByDay v-model="exclusions" :isDisabled="isOpen" />
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="editOfficeHours" class="bg-primary text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm, useField } from 'vee-validate'
import { computed, ref, watch } from 'vue'
import { api } from '../../../../api'
import type {
  DayOfWeek,
  EditOfficeHoursRequest,
  Exclusion,
  OfficeHours,
} from '../../../../api/api-reference'
import * as yup from 'yup'
import { useAuthStore } from '@/stores/auth'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import { getDayName } from '../../../../utils/timeMethods'
import ToggleSwitch from 'primevue/toggleswitch'
import Divider from 'primevue/divider'
import { parseDateToTimeString, parseTimeToToday } from '@/utils/timeMethods'
import EditExlusionHoursByDay from './EditExclusionHoursByDay.vue'

const authStore = useAuthStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  day: DayOfWeek | undefined
  officeHours: OfficeHours[] | undefined
  isModalOpen: boolean
}>()

const exclusions = ref<Exclusion[]>([])

const now = new Date()
const initialValues = {
  isOpen: true,
  openTime: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 9, 0, 0, 0),
  closeTime: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 17, 0, 0, 0),
}

const isOpenLabel = computed(() => (isOpen.value ? 'open' : 'closed'))

const schema = yup.object({
  isOpen: yup.boolean().required(),
  openTime: yup.string().when('isOpen', {
    is: true,
    then: (schema) => schema.required('Open time is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  closeTime: yup.string().when('isOpen', {
    is: true,
    then: (schema) =>
      schema
        .required('Close time is required')
        .test('is-greater', 'Close time must be later than Open time', function (value) {
          const { openTime } = this.parent
          if (!openTime || !value) return true
          return value > openTime
        }),
    otherwise: (schema) => schema.notRequired(),
  }),
})

const { handleSubmit, resetForm, setValues } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const { value: isOpen } = useField<boolean>('isOpen')

const setExclusions = () => {
  const index = props.officeHours?.findIndex((oh) => oh.day === props.day)

  if (props.officeHours && (index || index === 0) && index !== -1) {
    exclusions.value = props.officeHours[index].exclusions ?? []
  }
}

watch(
  () => props.day,
  (day) => {
    resetForm()
    setExclusions()
    const dailyOfficeHours = props.officeHours?.filter((x) => x.day == day)[0]
    if (dailyOfficeHours) {
      const formValues = {
        isOpen: !!dailyOfficeHours.openTime && !!dailyOfficeHours.closeTime,
        openTime: parseTimeToToday(dailyOfficeHours.openTime),
        closeTime: parseTimeToToday(dailyOfficeHours.closeTime),
      }
      setValues(formValues)
    }
  },
)

const editOfficeHours = handleSubmit(async (values) => {
  try {
    const updatedOfficeHours = [...props.officeHours!]
    const index = updatedOfficeHours.findIndex((oh) => oh.day === props.day)

    if (index !== -1) {
      updatedOfficeHours[index] = {
        ...updatedOfficeHours[index],
        openTime: values.isOpen ? parseDateToTimeString(values.openTime) : null,
        closeTime: values.isOpen ? parseDateToTimeString(values.closeTime) : null,
        exclusions: exclusions.value,
      }
    }
    const form: EditOfficeHoursRequest = {
      officeHours: updatedOfficeHours,
    }
    await api.employees.employeeEditOrganizationEmployeeOfficeHours(
      authStore.user!.employeeId!,
      authStore.user!.locationId!,
      form,
    )
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
