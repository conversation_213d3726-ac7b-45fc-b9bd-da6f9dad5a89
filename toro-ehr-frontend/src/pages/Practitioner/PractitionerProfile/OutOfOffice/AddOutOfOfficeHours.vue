<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <h2 class="text-lg font-bold mb-4">Out of Office</h2>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="addOutOfOffice()">
          <DateTimePicker
            id="startAt"
            label="Start"
            :manualInput="false"
            showTime
            hourFormat="12"
            :stepMinute="15"
          />
          <DateTimePicker
            id="endAt"
            label="End"
            :manualInput="false"
            showTime
            hourFormat="12"
            :stepMinute="15"
          />
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="addOutOfOffice" class="bg-primary text-white px-4 py-2 rounded">
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '../../../../api'
import type { OutOfOfficeHours } from '../../../../api/api-reference'
import * as yup from 'yup'
import { useAuthStore } from '@/stores/auth'
import DateTimePicker from '../../../../components/form-extensions/DateTimePickerFluent.vue'
import { watch } from 'vue'

const authStore = useAuthStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  outOfOfficeHours: OutOfOfficeHours[] | undefined
}>()

const now = new Date()
const initialValues = {
  startAt: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
  endAt: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
}

const schema = yup.object({
  startAt: yup.date().required('Start is required'),
  endAt: yup
    .date()
    .required('End is required')
    .test('is-greater', 'End must be later than Start time', function (value) {
      const { startAt } = this.parent
      if (!startAt || !value) return true
      return value > startAt
    }),
})

const { handleSubmit, resetForm, setValues } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

watch(
  () => props.isModalOpen,
  (isModalOpen: boolean) => {
    if (isModalOpen) {
      resetForm()
      setValues(initialValues)
    }
  },
)

const addOutOfOffice = handleSubmit(async (values) => {
  try {
    const updatedOutOfOfficeHours = [...props.outOfOfficeHours!]
    updatedOutOfOfficeHours.push({
      startAt: values.startAt.toISOString(),
      endAt: values.endAt.toISOString(),
    })

    await api.employees.employeeEditLocationEmployeeOutOfOfficeHours(
      authStore.user!.employeeId!,
      authStore.user!.locationId!,
      {
        outOfOfficeHoursList: updatedOutOfOfficeHours,
      },
    )
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
