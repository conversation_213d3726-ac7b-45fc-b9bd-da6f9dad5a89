<template>
  <TableSection>
    <TableHeader>
      <template #buttons>
        <a
          class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
          href="#"
          @click.prevent="openOutOfOfficeHoursModal()"
        >
          <PlusIcon class="shrink-0 w-4 h-4" />
          Out of office
        </a>
      </template>
    </TableHeader>
    <!-- Table -->
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                Start
              </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold uppercase tracking-wide text-gray-800"> End </span>
            </div>
          </th>

          <th scope="col" class="px-6 py-3 text-end"></th>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200">
        <tr v-for="(row, index) in outOfOfficeHoursList" :key="index">
          <td class="h-px w-48 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-500">{{ formatDate(row.startAt) }}</span>
            </div>
          </td>

          <td class="h-px w-48 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-500">{{ formatDate(row.endAt) }}</span>
            </div>
          </td>

          <td class="size-px whitespace-nowrap">
            <Button
              icon="pi pi-trash"
              aria-label="Delete"
              severity="danger"
              variant="text"
              @click="removeOutOfOfficeHours(index)"
            />
          </td>
        </tr>
      </tbody>
    </table>
    <!-- End Table -->
  </TableSection>
  <Teleport to="body">
    <AddOutOfOfficeHours
      :isModalOpen="isAddOutOfOfficeHoursModalOpen"
      :outOfOfficeHours="outOfOfficeHoursList"
      @close="closeModal"
    />
  </Teleport>

  <Divider />
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import type { OutOfOfficeHours } from '@/api/api-reference.ts'
import { useAuthStore } from '@/stores/auth.ts'
import { usePractitionerStore } from '@/stores/practitioner'
import TableSection from '@/components/table/TableSection.vue'
import TableHeader from '@/components/table/TableHeader.vue'
import Divider from 'primevue/divider'
import { PlusIcon } from '@heroicons/vue/24/outline'
import Button from 'primevue/button'
import AddOutOfOfficeHours from './AddOutOfOfficeHours.vue'
import { formatDate } from '@/utils/timeMethods'
import { api } from '../../../../api'

const authStore = useAuthStore()
const practitionerStore = usePractitionerStore()

const outOfOfficeHoursList = ref<OutOfOfficeHours[]>()

const isAddOutOfOfficeHoursModalOpen = ref(false)

onMounted(async () => {
  outOfOfficeHoursList.value = practitionerStore.organizationPractitioner?.outOfOfficeHours
})

watch(
  () => practitionerStore.organizationPractitioner,
  (newValue) => {
    outOfOfficeHoursList.value = newValue?.outOfOfficeHours
  },
)

const openOutOfOfficeHoursModal = () => {
  isAddOutOfOfficeHoursModalOpen.value = true
}

const removeOutOfOfficeHours = async (index: number) => {
  console.log(index)
  const updatedOutOfOfficeHours = [...outOfOfficeHoursList.value!]
  updatedOutOfOfficeHours.splice(index, 1)

  await api.employees.employeeEditLocationEmployeeOutOfOfficeHours(
    authStore.user!.employeeId!,
    authStore.user!.locationId!,
    {
      outOfOfficeHoursList: updatedOutOfOfficeHours,
    },
  )
  practitionerStore.getLocationEmployee(authStore.user!.employeeId!, authStore.user!.locationId!)
}

const closeModal = () => {
  practitionerStore.getLocationEmployee(authStore.user!.employeeId!, authStore.user!.locationId!)
  isAddOutOfOfficeHoursModalOpen.value = false
}
</script>
