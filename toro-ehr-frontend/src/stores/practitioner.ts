import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { LocationEmployeeResponse } from '../api/api-reference'
import { api } from '../api'

export const usePractitionerStore = defineStore('practitioner', () => {
  const organizationPractitioner = ref<LocationEmployeeResponse | null>(null)

  async function getLocationEmployee(employeeId: string, locationId: string) {
    try {
      // Fetch location practitioner
      const orgPractitionerResponse = await api.employees.employeeGetLocationEmployee(
        employeeId,
        locationId,
      )
      setLocationPractitioner(orgPractitionerResponse.data)
    } catch (error) {
      console.error(error)
    }
  }

  function setLocationPractitioner(locPractitioner: LocationEmployeeResponse) {
    organizationPractitioner.value = locPractitioner
  }

  return { organizationPractitioner, getLocationEmployee }
})
