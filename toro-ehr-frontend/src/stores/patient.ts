import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { PatientProfileResponse } from '../api/api-reference'
import { api } from '../api'

export const usePatientStore = defineStore('patient', () => {
  const patientProfile = ref<PatientProfileResponse | null>(null)

  async function getPatientProfile(patientId: string | null = null) {
    try {
      // Fetch profile
      const patientProfile = await api.patients.patientGetPatientProfile({ patientId })
      setPatientProfile(patientProfile.data)
    } catch (error) {
      console.error(error)
    }
  }

  function setPatientProfile(patientProfileResponse: PatientProfileResponse) {
    patientProfile.value = patientProfileResponse
  }

  return { patientProfile, getPatientProfile }
})
