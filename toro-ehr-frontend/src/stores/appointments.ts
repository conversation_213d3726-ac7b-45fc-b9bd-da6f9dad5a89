import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { LocationWithPractitionersResponse, SelectListItem } from '../api/api-reference'
import { api } from '@/api'

export const useAppointmentsStore = defineStore('appointments', () => {
  const practitionerLookups = ref<SelectListItem[]>([])
  const locationLookups = ref<SelectListItem[]>([])

  const locationsWithPractitioners = ref<LocationWithPractitionersResponse[]>([])

  async function getPractitionerLookups() {
    try {
      const practitionerLookupsResponse = await api.employees.employeeListEmployeesLookup()
      practitionerLookups.value = practitionerLookupsResponse.data
    } catch (error) {
      console.error(error)
    }
  }

  async function getLocationsWithPractitioners() {
    try {
      const response = await api.locations.locationGetLocationsWithDoctors()

      locationsWithPractitioners.value = response.data.map((item) => ({
        ...item,
        locationName: `${item.organizationName} - ${item.locationName}`,
      }))
    } catch (error) {
      console.error(error)
    }
  }

  async function getLocationsLookups() {
    try {
      locationLookups.value = (await api.locations.locationListLocationsLookup()).data
    } catch (error) {
      console.error(error)
    }
  }

  return {
    practitionerLookups,
    locationLookups,
    locationsWithPractitioners,
    getPractitionerLookups,
    getLocationsWithPractitioners,
    getLocationsLookups
  }
})
