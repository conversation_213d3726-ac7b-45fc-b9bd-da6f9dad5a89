import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/api'
import type { NotificationResponse, NotificationType } from '@/api/api-reference'
import { NotificationStatus } from '@/api/api-reference'
import { useAuthStore } from './auth'

export const useNotificationsStore = defineStore('notifications', () => {
  const notifications = ref<NotificationResponse[]>([])
  const unreadCount = ref(0)
  const isLoading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const totalPages = ref(0)
  const totalItems = ref(0)

  // polling interval reference
  let pollingInterval: number | null = null

  const authStore = useAuthStore()

  // filter notifications based on user role
  const filteredNotifications = computed(() => {
    const userRole = authStore.user?.selectedUserRole

    return notifications.value.filter(notification => {
      if (userRole === 'Patient') {
        // patients see appointment notifications, profile/questionnaire completion reminders, questionnaire updates, and messages
        return notification.notificationType === 'Appointment Confirmed' ||
               notification.notificationType === 'Appointment Canceled' ||
               notification.notificationType === 'Complete Profile' ||
               notification.notificationType === 'Complete Questionnaires' ||
               notification.notificationType === 'Questionnaire Updated' ||
               notification.notificationType === 'Message Received'
      } else if (userRole === 'Employee') {
        // employees see all except AppointmentConfirmed and patient-specific notifications
        return notification.notificationType !== 'Appointment Confirmed' &&
               notification.notificationType !== 'Complete Profile' &&
               notification.notificationType !== 'Complete Questionnaires'
      }
      return false
    })
  })



  async function fetchNotifications(page = 1) {
    try {
      isLoading.value = true
      const response = await api.notifications.notificationGetUserNotifications({
        pageNumber: page,
        pageSize: pageSize.value
      })

      if (response.data) {
        if (page === 1) {
          notifications.value = response.data.items || []
        } else {
          // append for pagination
          notifications.value.push(...(response.data.items || []))
        }

        currentPage.value = response.data.pageNumber || 1
        totalPages.value = response.data.totalPages || 0
        totalItems.value = response.data.totalItems || 0
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    } finally {
      isLoading.value = false
    }
  }

  async function fetchUnreadCount() {
    try {
      const response = await api.notifications.notificationGetUnreadNotificationCount({
        metadata: { requestId: Math.random().toString(36).substr(2, 9), silent: true }
      })
      unreadCount.value = response.data || 0
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
    }
  }

  async function markAsRead(notificationId: string) {
    try {
      await api.notifications.notificationMarkNotificationsAsRead({
        notificationIds: [notificationId]
      })

      // update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.status = NotificationStatus.Read
        notification.readAt = new Date().toISOString()
      }

      // update unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  async function markMultipleAsRead(notificationIds: string[]) {
    try {
      await api.notifications.notificationMarkNotificationsAsRead({
        notificationIds
      })

      // update local state for all notifications
      const now = new Date().toISOString()
      notifications.value.forEach(notification => {
        if (notification.id && notificationIds.includes(notification.id)) {
          notification.status = NotificationStatus.Read
          notification.readAt = now
        }
      })

      // update unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Failed to mark notifications as read:', error)
      throw error
    }
  }

  async function deleteNotification(notificationId: string) {
    try {
      await api.notifications.notificationDeleteNotification(notificationId)

      // remove from local state
      notifications.value = notifications.value.filter(n => n.id !== notificationId)

      // update unread count
      await fetchUnreadCount()
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }

  function startPolling() {
    // clear existing interval
    if (pollingInterval) {
      clearInterval(pollingInterval)
    }

    // only start polling if user is logged in
    if (!authStore.user) {
      return
    }

    // optimized polling: only fetch unread count every 30 seconds
    // notifications are fetched only when user interacts (opens dropdown/page)
    pollingInterval = setInterval(async () => {
      // check if user is still logged in before making API calls
      if (!authStore.user) {
        stopPolling()
        return
      }

      // only fetch unread count for performance
      await fetchUnreadCount()
    }, 30000)
  }

  function stopPolling() {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
    }
  }

  async function loadMore() {
    if (currentPage.value < totalPages.value && !isLoading.value) {
      await fetchNotifications(currentPage.value + 1)
    }
  }

  function reset() {
    notifications.value = []
    unreadCount.value = 0
    currentPage.value = 1
    totalPages.value = 0
    totalItems.value = 0
    stopPolling()
  }

  return {
    notifications: filteredNotifications,
    unreadCount, // use the raw unread count from API, not filtered
    isLoading,
    currentPage,
    pageSize,
    totalPages,
    totalItems,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markMultipleAsRead,
    deleteNotification,
    startPolling,
    stopPolling,
    loadMore,
    reset
  }
})
