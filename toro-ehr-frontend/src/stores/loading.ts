import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface LoadingRequest {
  message: string
  showOverlay: boolean
}

export const useLoadingStore = defineStore('loading', () => {
  // track multiple concurrent requests with their types
  const activeRequests = ref<Map<string, LoadingRequest>>(new Map())

  // computed state for overlay (only show if any request needs overlay)
  const isOverlayLoading = computed(() => {
    return Array.from(activeRequests.value.values()).some(req => req.showOverlay)
  })

  // computed state for simple spinner (show if any request is active but no overlay needed)
  const isSpinnerLoading = computed(() => {
    return activeRequests.value.size > 0 && !isOverlayLoading.value
  })

  const currentMessage = computed(() => {
    const requests = Array.from(activeRequests.value.values())
    return requests[0]?.message || ''
  })

  // start loading for a specific request
  const startLoading = (message: string = '', requestId: string, showOverlay: boolean = true) => {
    activeRequests.value.set(requestId, { message, showOverlay })
  }

  // stop loading for a specific request
  const stopLoading = (requestId: string) => {
    activeRequests.value.delete(requestId)
  }

  // clear all loading states (emergency cleanup)
  const clearAll = () => {
    activeRequests.value.clear()
  }

  return {
    isOverlayLoading,
    isSpinnerLoading,
    currentMessage,
    startLoading,
    stopLoading,
    clearAll
  }
})
